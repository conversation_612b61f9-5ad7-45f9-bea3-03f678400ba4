# 🎯 Interactive Graph System - Dynamic Code Navigation

## 📋 Overview

This document describes a comprehensive interactive graph system that provides:
- **Dynamic zoom and navigation**
- **Collapsible/expandable components**
- **Direct function linking**
- **Multi-level detail views**
- **Real-time code navigation**

## 🎨 Interactive Graph Features

### 1. Dynamic Zoom & Navigation
- **Mouse wheel zoom** - Zoom in/out smoothly
- **Pan and drag** - Navigate large graphs easily
- **Fit to screen** - Auto-resize to optimal view
- **Mini-map** - Overview navigation for large graphs

### 2. Collapsible Components
- **Click to expand/collapse** sub-systems
- **Progressive disclosure** - Show details on demand
- **Breadcrumb navigation** - Track your location
- **Level indicators** - Know your depth

### 3. Direct Function Linking
- **Click any function node** → Jump to source code
- **Hover for quick preview** - See function signature
- **Context menu** - Additional actions (debug, test, refactor)
- **Cross-references** - See all usages

### 4. Multi-Level Views
- **Architecture Level** - High-level system overview
- **Component Level** - Individual component details
- **Function Level** - Specific function implementations
- **Line Level** - Exact code location

## 🚀 Implementation Plan

### Phase 1: Enhanced Mermaid with Interactivity
### Phase 2: Custom D3.js Interactive Graphs
### Phase 3: VSCode Integration
### Phase 4: Real-time Code Synchronization

---

## 📊 Level 1: High-Level Architecture Graph

```mermaid
graph TB
    subgraph "🎯 Click any component to expand details"
        UI[🖥️ User Interface Layer<br/>Click to expand]
        AGENTS[🤖 Agent System<br/>Click to expand]
        LLM[🧠 LLM Integration<br/>Click to expand]
        COGNITIVE[🔍 Cognitive Processing<br/>Click to expand]
        KNOWLEDGE[📊 Knowledge Graph<br/>Click to expand]
        PARSING[⚙️ Code Parsing<br/>Click to expand]
    end
    
    UI --> AGENTS
    AGENTS --> LLM
    AGENTS --> COGNITIVE
    COGNITIVE --> KNOWLEDGE
    COGNITIVE --> PARSING
    KNOWLEDGE --> PARSING
    
    click UI "javascript:expandComponent('UI')"
    click AGENTS "javascript:expandComponent('AGENTS')"
    click LLM "javascript:expandComponent('LLM')"
    click COGNITIVE "javascript:expandComponent('COGNITIVE')"
    click KNOWLEDGE "javascript:expandComponent('KNOWLEDGE')"
    click PARSING "javascript:expandComponent('PARSING')"
    
    style UI fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style AGENTS fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style LLM fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style COGNITIVE fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style KNOWLEDGE fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style PARSING fill:#f1f8e9,stroke:#33691e,stroke-width:2px
```

## 📊 Level 2: Agent System Detailed View

```mermaid
graph TB
    subgraph "🤖 Agent System - Expandable Components"
        MA[🎯 Master Agent<br/>src/agents/master-agent.ts<br/>Click to view code]
        CA[🧠 Cognitive Agent<br/>src/agents/cognitive-agent.ts<br/>Click to view code]
        
        subgraph "📋 Sub-Agent Registry - Click to expand"
            PA[📝 Planner Agent<br/>Click for details]
            CRA[📖 Code Reader Agent<br/>Click for details]
            RA[🤔 Reasoner Agent<br/>Click for details]
            BDA[🐛 Bug Detector Agent<br/>Click for details]
            RFA[🔧 Refactorer Agent<br/>Click for details]
            TA[🧪 Tester Agent<br/>Click for details]
            GMA[🕸️ Graph Mind Agent<br/>Click for details]
        end
        
        subgraph "💾 Agent Memory - Click to expand"
            AM[🧠 Agent Memory<br/>src/agents/agent-memory.ts]
            QH[📚 Query History]
            UP[👤 User Patterns]
            LS[📈 Learning System]
        end
    end
    
    MA --> CA
    MA --> PA
    MA --> CRA
    MA --> RA
    MA --> BDA
    MA --> RFA
    MA --> TA
    MA --> GMA
    
    CA --> AM
    AM --> QH
    AM --> UP
    AM --> LS
    
    click MA "javascript:openFile('src/agents/master-agent.ts')"
    click CA "javascript:openFile('src/agents/cognitive-agent.ts')"
    click PA "javascript:expandAgent('PlannerAgent')"
    click CRA "javascript:expandAgent('CodeReaderAgent')"
    click RA "javascript:expandAgent('ReasonerAgent')"
    click BDA "javascript:expandAgent('BugDetectorAgent')"
    click RFA "javascript:expandAgent('RefactorerAgent')"
    click TA "javascript:expandAgent('TesterAgent')"
    click GMA "javascript:expandAgent('GraphMindAgent')"
    click AM "javascript:openFile('src/agents/agent-memory.ts')"
    
    style MA fill:#ffeb3b,stroke:#f57f17,stroke-width:3px
    style CA fill:#4caf50,stroke:#1b5e20,stroke-width:3px
```

## 📊 Level 3: Individual Agent Deep Dive

```mermaid
graph TB
    subgraph "🤔 Reasoner Agent - Function Level Detail"
        RA_MAIN[🎯 ReasonerAgent.execute<br/>Line 45-120<br/>Click to view]
        
        subgraph "🔍 Core Functions - Click any to view code"
            RA_ANALYZE[analyzeLogic()<br/>Line 125-180<br/>Click to view]
            RA_INFER[inferRelationships()<br/>Line 185-240<br/>Click to view]
            RA_VALIDATE[validateReasoning()<br/>Line 245-290<br/>Click to view]
            RA_SYNTHESIZE[synthesizeConclusions()<br/>Line 295-350<br/>Click to view]
        end
        
        subgraph "🧠 Reasoning Strategies"
            DEDUCTIVE[Deductive Reasoning<br/>Line 355-400]
            INDUCTIVE[Inductive Reasoning<br/>Line 405-450]
            ABDUCTIVE[Abductive Reasoning<br/>Line 455-500]
            CAUSAL[Causal Reasoning<br/>Line 505-550]
        end
        
        subgraph "📊 Knowledge Integration"
            KG_QUERY[Knowledge Graph Query<br/>Line 555-600]
            PATTERN_MATCH[Pattern Matching<br/>Line 605-650]
            CONFIDENCE[Confidence Calculation<br/>Line 655-700]
        end
    end
    
    RA_MAIN --> RA_ANALYZE
    RA_MAIN --> RA_INFER
    RA_MAIN --> RA_VALIDATE
    RA_MAIN --> RA_SYNTHESIZE
    
    RA_ANALYZE --> DEDUCTIVE
    RA_ANALYZE --> INDUCTIVE
    RA_INFER --> ABDUCTIVE
    RA_INFER --> CAUSAL
    
    RA_VALIDATE --> KG_QUERY
    RA_SYNTHESIZE --> PATTERN_MATCH
    RA_SYNTHESIZE --> CONFIDENCE
    
    click RA_MAIN "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 45)"
    click RA_ANALYZE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 125)"
    click RA_INFER "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 185)"
    click RA_VALIDATE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 245)"
    click RA_SYNTHESIZE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 295)"
    click DEDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 355)"
    click INDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 405)"
    click ABDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 455)"
    click CAUSAL "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 505)"
    click KG_QUERY "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 555)"
    click PATTERN_MATCH "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 605)"
    click CONFIDENCE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 655)"
```

## 🎮 Interactive Controls

### JavaScript Functions for Interactivity

```javascript
// Core navigation functions
function expandComponent(componentName) {
    // Show detailed view of component
    showDetailedView(componentName);
    updateBreadcrumb(componentName);
}

function expandAgent(agentName) {
    // Show agent-specific detailed graph
    showAgentDetails(agentName);
    updateBreadcrumb(['Agents', agentName]);
}

function openFile(filePath) {
    // Open file in VSCode
    vscode.postMessage({
        command: 'openFile',
        filePath: filePath
    });
}

function openFileAtLine(filePath, lineNumber) {
    // Open file at specific line
    vscode.postMessage({
        command: 'openFileAtLine',
        filePath: filePath,
        lineNumber: lineNumber
    });
}

function showFunctionDetails(functionName, filePath, lineNumber) {
    // Show function details in popup
    showPopup({
        title: functionName,
        content: getFunctionPreview(filePath, lineNumber),
        actions: [
            { label: 'View Code', action: () => openFileAtLine(filePath, lineNumber) },
            { label: 'Debug', action: () => debugFunction(functionName) },
            { label: 'Test', action: () => testFunction(functionName) },
            { label: 'Find Usages', action: () => findUsages(functionName) }
        ]
    });
}
```

## 🎯 Enhanced Graph Features

### 1. Zoom and Pan Controls
```html
<div class="graph-controls">
    <button onclick="zoomIn()">🔍 Zoom In</button>
    <button onclick="zoomOut()">🔍 Zoom Out</button>
    <button onclick="fitToScreen()">📐 Fit to Screen</button>
    <button onclick="resetView()">🔄 Reset View</button>
</div>
```

### 2. Level Navigation
```html
<div class="level-navigator">
    <span class="breadcrumb">
        <a href="#" onclick="showLevel('architecture')">Architecture</a> > 
        <a href="#" onclick="showLevel('agents')">Agents</a> > 
        <span class="current">Reasoner Agent</span>
    </span>
</div>
```

### 3. Search and Filter
```html
<div class="graph-search">
    <input type="text" placeholder="Search functions, components..." onkeyup="filterGraph(this.value)">
    <select onchange="filterByType(this.value)">
        <option value="all">All Components</option>
        <option value="agents">Agents Only</option>
        <option value="functions">Functions Only</option>
        <option value="classes">Classes Only</option>
    </select>
</div>
```
