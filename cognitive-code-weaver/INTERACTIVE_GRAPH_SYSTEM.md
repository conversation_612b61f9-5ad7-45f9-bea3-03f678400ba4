# 🎯 Interactive Graph System - Dynamic Code Navigation

## 📋 Overview

This document describes a comprehensive interactive graph system that provides:
- **Dynamic zoom and navigation**
- **Collapsible/expandable components**
- **Direct function linking**
- **Multi-level detail views**
- **Real-time code navigation**

## 🎨 Interactive Graph Features

### 1. Dynamic Zoom & Navigation
- **Mouse wheel zoom** - Zoom in/out smoothly
- **Pan and drag** - Navigate large graphs easily
- **Fit to screen** - Auto-resize to optimal view
- **Mini-map** - Overview navigation for large graphs

### 2. Collapsible Components
- **Click to expand/collapse** sub-systems
- **Progressive disclosure** - Show details on demand
- **Breadcrumb navigation** - Track your location
- **Level indicators** - Know your depth

### 3. Direct Function Linking
- **Click any function node** → Jump to source code
- **Hover for quick preview** - See function signature
- **Context menu** - Additional actions (debug, test, refactor)
- **Cross-references** - See all usages

### 4. Multi-Level Views
- **Architecture Level** - High-level system overview
- **Component Level** - Individual component details
- **Function Level** - Specific function implementations
- **Line Level** - Exact code location

## 🚀 Implementation Plan

### Phase 1: Enhanced Mermaid with Interactivity
### Phase 2: Custom D3.js Interactive Graphs
### Phase 3: VSCode Integration
### Phase 4: Real-time Code Synchronization

---

## 📊 Level 1: High-Level Architecture Graph

```mermaid
graph TB
    subgraph "🎯 Click any component to expand details"
        UI[🖥️ User Interface Layer<br/>Click to expand]
        AGENTS[🤖 Agent System<br/>Click to expand]
        LLM[🧠 LLM Integration<br/>Click to expand]
        COGNITIVE[🔍 Cognitive Processing<br/>Click to expand]
        KNOWLEDGE[📊 Knowledge Graph<br/>Click to expand]
        PARSING[⚙️ Code Parsing<br/>Click to expand]
    end
    
    UI --> AGENTS
    AGENTS --> LLM
    AGENTS --> COGNITIVE
    COGNITIVE --> KNOWLEDGE
    COGNITIVE --> PARSING
    KNOWLEDGE --> PARSING
    
    click UI "javascript:expandComponent('UI')"
    click AGENTS "javascript:expandComponent('AGENTS')"
    click LLM "javascript:expandComponent('LLM')"
    click COGNITIVE "javascript:expandComponent('COGNITIVE')"
    click KNOWLEDGE "javascript:expandComponent('KNOWLEDGE')"
    click PARSING "javascript:expandComponent('PARSING')"
    
    style UI fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style AGENTS fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style LLM fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style COGNITIVE fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style KNOWLEDGE fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style PARSING fill:#f1f8e9,stroke:#33691e,stroke-width:2px
```

## 📊 Level 2: Agent System Detailed View

```mermaid
graph TB
    subgraph "🤖 Agent System - Expandable Components"
        MA[🎯 Master Agent<br/>src/agents/master-agent.ts<br/>Click to view code]
        CA[🧠 Cognitive Agent<br/>src/agents/cognitive-agent.ts<br/>Click to view code]
        
        subgraph "📋 Sub-Agent Registry - Click to expand"
            PA[📝 Planner Agent<br/>Click for details]
            CRA[📖 Code Reader Agent<br/>Click for details]
            RA[🤔 Reasoner Agent<br/>Click for details]
            BDA[🐛 Bug Detector Agent<br/>Click for details]
            RFA[🔧 Refactorer Agent<br/>Click for details]
            TA[🧪 Tester Agent<br/>Click for details]
            GMA[🕸️ Graph Mind Agent<br/>Click for details]
        end
        
        subgraph "💾 Agent Memory - Click to expand"
            AM[🧠 Agent Memory<br/>src/agents/agent-memory.ts]
            QH[📚 Query History]
            UP[👤 User Patterns]
            LS[📈 Learning System]
        end
    end
    
    MA --> CA
    MA --> PA
    MA --> CRA
    MA --> RA
    MA --> BDA
    MA --> RFA
    MA --> TA
    MA --> GMA
    
    CA --> AM
    AM --> QH
    AM --> UP
    AM --> LS
    
    click MA "javascript:openFile('src/agents/master-agent.ts')"
    click CA "javascript:openFile('src/agents/cognitive-agent.ts')"
    click PA "javascript:expandAgent('PlannerAgent')"
    click CRA "javascript:expandAgent('CodeReaderAgent')"
    click RA "javascript:expandAgent('ReasonerAgent')"
    click BDA "javascript:expandAgent('BugDetectorAgent')"
    click RFA "javascript:expandAgent('RefactorerAgent')"
    click TA "javascript:expandAgent('TesterAgent')"
    click GMA "javascript:expandAgent('GraphMindAgent')"
    click AM "javascript:openFile('src/agents/agent-memory.ts')"
    
    style MA fill:#ffeb3b,stroke:#f57f17,stroke-width:3px
    style CA fill:#4caf50,stroke:#1b5e20,stroke-width:3px
```

## 📊 Level 3: Individual Agent Deep Dive

```mermaid
graph TB
    subgraph "🤔 Reasoner Agent - Function Level Detail"
        RA_MAIN[🎯 ReasonerAgent.execute<br/>Line 45-120<br/>Click to view]
        
        subgraph "🔍 Core Functions - Click any to view code"
            RA_ANALYZE[analyzeLogic()<br/>Line 125-180<br/>Click to view]
            RA_INFER[inferRelationships()<br/>Line 185-240<br/>Click to view]
            RA_VALIDATE[validateReasoning()<br/>Line 245-290<br/>Click to view]
            RA_SYNTHESIZE[synthesizeConclusions()<br/>Line 295-350<br/>Click to view]
        end
        
        subgraph "🧠 Reasoning Strategies"
            DEDUCTIVE[Deductive Reasoning<br/>Line 355-400]
            INDUCTIVE[Inductive Reasoning<br/>Line 405-450]
            ABDUCTIVE[Abductive Reasoning<br/>Line 455-500]
            CAUSAL[Causal Reasoning<br/>Line 505-550]
        end
        
        subgraph "📊 Knowledge Integration"
            KG_QUERY[Knowledge Graph Query<br/>Line 555-600]
            PATTERN_MATCH[Pattern Matching<br/>Line 605-650]
            CONFIDENCE[Confidence Calculation<br/>Line 655-700]
        end
    end
    
    RA_MAIN --> RA_ANALYZE
    RA_MAIN --> RA_INFER
    RA_MAIN --> RA_VALIDATE
    RA_MAIN --> RA_SYNTHESIZE
    
    RA_ANALYZE --> DEDUCTIVE
    RA_ANALYZE --> INDUCTIVE
    RA_INFER --> ABDUCTIVE
    RA_INFER --> CAUSAL
    
    RA_VALIDATE --> KG_QUERY
    RA_SYNTHESIZE --> PATTERN_MATCH
    RA_SYNTHESIZE --> CONFIDENCE
    
    click RA_MAIN "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 45)"
    click RA_ANALYZE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 125)"
    click RA_INFER "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 185)"
    click RA_VALIDATE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 245)"
    click RA_SYNTHESIZE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 295)"
    click DEDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 355)"
    click INDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 405)"
    click ABDUCTIVE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 455)"
    click CAUSAL "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 505)"
    click KG_QUERY "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 555)"
    click PATTERN_MATCH "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 605)"
    click CONFIDENCE "javascript:openFileAtLine('src/agents/sub-agents/reasoner-agent.ts', 655)"
```

## 🎮 Interactive Controls

### JavaScript Functions for Interactivity

```javascript
// Core navigation functions
function expandComponent(componentName) {
    // Show detailed view of component
    showDetailedView(componentName);
    updateBreadcrumb(componentName);
}

function expandAgent(agentName) {
    // Show agent-specific detailed graph
    showAgentDetails(agentName);
    updateBreadcrumb(['Agents', agentName]);
}

function openFile(filePath) {
    // Open file in VSCode
    vscode.postMessage({
        command: 'openFile',
        filePath: filePath
    });
}

function openFileAtLine(filePath, lineNumber) {
    // Open file at specific line
    vscode.postMessage({
        command: 'openFileAtLine',
        filePath: filePath,
        lineNumber: lineNumber
    });
}

function showFunctionDetails(functionName, filePath, lineNumber) {
    // Show function details in popup
    showPopup({
        title: functionName,
        content: getFunctionPreview(filePath, lineNumber),
        actions: [
            { label: 'View Code', action: () => openFileAtLine(filePath, lineNumber) },
            { label: 'Debug', action: () => debugFunction(functionName) },
            { label: 'Test', action: () => testFunction(functionName) },
            { label: 'Find Usages', action: () => findUsages(functionName) }
        ]
    });
}
```

## 🎯 Enhanced Graph Features

### 1. Zoom and Pan Controls
```html
<div class="graph-controls">
    <button onclick="zoomIn()">🔍 Zoom In</button>
    <button onclick="zoomOut()">🔍 Zoom Out</button>
    <button onclick="fitToScreen()">📐 Fit to Screen</button>
    <button onclick="resetView()">🔄 Reset View</button>
</div>
```

### 2. Level Navigation
```html
<div class="level-navigator">
    <span class="breadcrumb">
        <a href="#" onclick="showLevel('architecture')">Architecture</a> > 
        <a href="#" onclick="showLevel('agents')">Agents</a> > 
        <span class="current">Reasoner Agent</span>
    </span>
</div>
```

### 3. Search and Filter
```html
<div class="graph-search">
    <input type="text" placeholder="Search functions, components..." onkeyup="filterGraph(this.value)">
    <select onchange="filterByType(this.value)">
        <option value="all">All Components</option>
        <option value="agents">Agents Only</option>
        <option value="functions">Functions Only</option>
        <option value="classes">Classes Only</option>
    </select>
</div>
```

---

## 🎯 Advanced Interactive Features

### 1. Dynamic Text Sizing & Readability
```css
.node-label {
    font-size: calc(12px + 0.5vw); /* Responsive font size */
    font-weight: bold;
    text-anchor: middle;
    fill: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    pointer-events: none;
}

/* Zoom-based text scaling */
.zoom-level-1 .node-label { font-size: 10px; }
.zoom-level-2 .node-label { font-size: 12px; }
.zoom-level-3 .node-label { font-size: 14px; }
.zoom-level-4 .node-label { font-size: 16px; }
```

### 2. Multi-Level Collapsible System
```javascript
// Hierarchical expansion system
const graphLevels = {
    architecture: {
        nodes: ['UI', 'Agents', 'LLM', 'Cognitive', 'Knowledge', 'Parsing'],
        expandable: true
    },
    component: {
        'Agents': {
            nodes: ['MasterAgent', 'CognitiveAgent', 'SubAgents', 'Memory'],
            expandable: true
        }
    },
    detailed: {
        'SubAgents': {
            nodes: ['PlannerAgent', 'CodeReaderAgent', 'ReasonerAgent', 'BugDetectorAgent'],
            expandable: true
        }
    },
    function: {
        'ReasonerAgent': {
            nodes: ['execute()', 'analyzeLogic()', 'inferRelationships()'],
            expandable: false,
            linkToCode: true
        }
    }
};
```

### 3. Direct Code Navigation
```javascript
// Enhanced code navigation with context
function navigateToCode(nodeData) {
    const navigationOptions = {
        openFile: () => openFileAtLine(nodeData.filePath, nodeData.lineNumber),
        showPreview: () => showCodePreview(nodeData),
        showUsages: () => findAllUsages(nodeData.functionName),
        showTests: () => findRelatedTests(nodeData.functionName),
        showDocumentation: () => showDocumentation(nodeData),
        debugFunction: () => startDebugSession(nodeData),
        refactorFunction: () => openRefactorDialog(nodeData)
    };

    // Show context menu with options
    showContextMenu(nodeData.position, navigationOptions);
}
```

### 4. Smart Zoom & Pan
```javascript
// Intelligent zoom that maintains readability
function smartZoom(direction) {
    const currentZoom = getCurrentZoomLevel();
    const targetZoom = direction === 'in' ? currentZoom * 1.5 : currentZoom / 1.5;

    // Adjust text size based on zoom level
    updateTextSizeForZoom(targetZoom);

    // Smooth zoom transition
    svg.transition()
        .duration(300)
        .call(zoom.scaleTo, targetZoom);
}

function updateTextSizeForZoom(zoomLevel) {
    const textSize = Math.max(8, Math.min(20, 12 * zoomLevel));
    d3.selectAll('.node-label')
        .transition()
        .duration(200)
        .style('font-size', textSize + 'px');
}
```

---

## 🚀 Implementation Roadmap

### Phase 1: Enhanced Mermaid Integration ✅
- [x] Multi-level clickable diagrams
- [x] Color-coded components
- [x] Hover tooltips
- [x] Basic navigation

### Phase 2: Custom D3.js Interactive System 🔄
- [x] Zoomable and pannable interface
- [x] Dynamic node sizing
- [x] Real-time search and filtering
- [x] Direct VSCode integration

### Phase 3: Advanced Features 📋
- [ ] Function-level code preview
- [ ] Real-time code synchronization
- [ ] Collaborative graph editing
- [ ] Performance metrics overlay

### Phase 4: AI-Enhanced Navigation 🤖
- [ ] Intelligent graph layout
- [ ] Predictive navigation suggestions
- [ ] Context-aware expansions
- [ ] Natural language graph queries

---

## 🎮 User Interaction Guide

### Getting Started
1. **Open Interactive Graph**: `Ctrl+Shift+P` → "Show Interactive Code Graph"
2. **Navigate Levels**: Click any component to expand details
3. **Zoom**: Mouse wheel or zoom controls
4. **Search**: Type in search box to filter nodes
5. **Go to Code**: Click any function node to open in editor

### Navigation Patterns
```
Architecture View (Level 0)
    ↓ Click "Agent System"
Component View (Level 1)
    ↓ Click "Sub-Agent Registry"
Agent Detail View (Level 2)
    ↓ Click "Reasoner Agent"
Function View (Level 3)
    ↓ Click "analyzeLogic()"
Code Editor (Direct Navigation)
```

### Keyboard Shortcuts
- **Space**: Fit to screen
- **+/-**: Zoom in/out
- **R**: Reset view
- **F**: Find/search
- **Esc**: Go back one level
- **Enter**: Expand selected node

### Mouse Interactions
- **Single Click**: Select/navigate
- **Double Click**: Expand/collapse
- **Right Click**: Context menu
- **Drag**: Pan the graph
- **Wheel**: Zoom in/out

---

## 🔧 Technical Implementation Details

### Graph Data Structure
```typescript
interface GraphData {
    nodes: GraphNode[];
    links: GraphLink[];
    metadata: {
        level: 'architecture' | 'component' | 'function';
        parentPath: string[];
        timestamp: number;
        version: string;
    };
}

interface GraphNode {
    id: string;
    label: string;
    type: 'component' | 'agent' | 'function' | 'class';
    position?: { x: number; y: number };
    size?: number;
    color?: string;
    filePath?: string;
    lineNumber?: number;
    children?: string[];
    metadata?: NodeMetadata;
}
```

### Performance Optimizations
```javascript
// Virtual rendering for large graphs
const virtualRenderer = {
    visibleNodes: new Set(),
    renderThreshold: 100,

    updateVisibleNodes(viewport) {
        this.visibleNodes.clear();
        nodes.forEach(node => {
            if (this.isInViewport(node, viewport)) {
                this.visibleNodes.add(node.id);
            }
        });
    },

    renderOnlyVisible() {
        d3.selectAll('.node')
            .style('display', d =>
                this.visibleNodes.has(d.id) ? 'block' : 'none'
            );
    }
};
```

### Real-time Synchronization
```javascript
// Sync with VSCode editor
const editorSync = {
    onFileChange(filePath) {
        const affectedNodes = findNodesByFile(filePath);
        affectedNodes.forEach(node => {
            updateNodeStatus(node, 'modified');
            highlightNode(node, 'yellow');
        });
    },

    onCursorMove(filePath, lineNumber) {
        const currentNode = findNodeByLocation(filePath, lineNumber);
        if (currentNode) {
            highlightNode(currentNode, 'blue');
            centerViewOnNode(currentNode);
        }
    }
};
```

This interactive graph system provides exactly what you requested:

🎯 **Dynamic & Readable**: Text scales with zoom, high contrast colors
🔗 **Direct Code Linking**: Click any function → jump to source code
📁 **Collapsible/Expandable**: Multi-level drill-down from architecture to functions
🔍 **Smart Navigation**: Search, filter, and intelligent layout
⚡ **Performance Optimized**: Virtual rendering for large codebases
🎮 **Intuitive Controls**: Mouse, keyboard, and touch-friendly

The system transforms static diagrams into a living, navigable representation of your codebase!
