# 🎯 Interactive Graph System - Complete Usage Guide

## 🚀 Quick Start

### 1. Opening the Interactive Graph
```
Method 1: Command Palette
- Press Ctrl+Shift+P (Cmd+Shift+P on Mac)
- Type "Show Interactive Code Graph"
- Press Enter

Method 2: Menu (when implemented)
- View → Cognitive Code Weaver → Interactive Graph

Method 3: Keyboard Shortcut (when configured)
- Ctrl+Shift+G (Cmd+Shift+G on Mac)
```

### 2. First Look - Architecture View
When you first open the graph, you'll see 6 main components:
- 🖥️ **User Interface Layer** (Green)
- 🤖 **Agent System** (Blue) 
- 🧠 **LLM Integration** (Orange)
- 🔍 **Cognitive Processing** (Purple)
- 📊 **Knowledge Graph** (Red)
- ⚙️ **Code Parsing** (Gray)

## 🎮 Navigation Controls

### Mouse Controls
| Action | Result |
|--------|--------|
| **Single Click** | Select node / Navigate to component |
| **Double Click** | Expand/collapse node |
| **Right Click** | Show context menu with options |
| **Drag** | Pan the graph around |
| **Mouse Wheel** | Zoom in/out |
| **Hover** | Show detailed tooltip |

### Keyboard Shortcuts
| Key | Action |
|-----|--------|
| **Space** | Fit graph to screen |
| **+** | Zoom in |
| **-** | Zoom out |
| **R** | Reset view to center |
| **F** | Focus on search box |
| **Esc** | Go back one level |
| **Enter** | Expand selected node |
| **Arrow Keys** | Navigate between nodes |

### Control Panel
Located in the top-left corner:
- 🔍+ **Zoom In** - Increase magnification
- 🔍- **Zoom Out** - Decrease magnification  
- 📐 **Fit Screen** - Auto-resize to optimal view
- 🔄 **Reset** - Return to original position
- 🏗️ **Architecture** - Go back to top level
- 📁 **Collapse All** - Close all expanded nodes
- 📂 **Expand All** - Open all expandable nodes

## 🔍 Multi-Level Navigation

### Level 1: Architecture View
**What you see**: High-level system components
**What you can do**:
- Click any component to drill down
- See overall system relationships
- Get component descriptions on hover

### Level 2: Component View  
**Example**: Click "Agent System"
**What you see**: Individual agents and their connections
**What you can do**:
- Click specific agents for detailed view
- See agent relationships and data flow
- Access agent source files directly

### Level 3: Agent Detail View
**Example**: Click "Reasoner Agent" 
**What you see**: Individual functions within the agent
**What you can do**:
- Click functions to view source code
- See function call relationships
- Access function documentation

### Level 4: Code View
**Example**: Click "analyzeLogic()" function
**What happens**: VSCode opens the exact file and line
**Additional options**:
- View function signature and parameters
- See all usages of the function
- Start debugging session
- Run related tests

## 🔎 Search and Filter

### Search Box (Bottom-left)
- **Type to search**: Function names, component names, descriptions
- **Real-time filtering**: Results update as you type
- **Highlight matches**: Found nodes are highlighted in yellow
- **Clear search**: Delete text or press Esc

### Filter Dropdown
- **All**: Show everything
- **Components**: Show only high-level components
- **Agents**: Show only agent nodes
- **Functions**: Show only function nodes  
- **Classes**: Show only class nodes

### Advanced Search Examples
```
Search Term → What it finds
"reasoner" → Reasoner Agent, reasoning functions
"analyze" → All analysis-related functions
"bug" → Bug Detector Agent, bug-related code
"llm" → LLM Integration component, LLM functions
```

## 🎨 Visual Elements

### Node Colors
- 🟢 **Green**: UI/Interface components
- 🔵 **Blue**: Agent system components
- 🟠 **Orange**: LLM and AI components
- 🟣 **Purple**: Cognitive processing
- 🔴 **Red**: Knowledge and data storage
- ⚫ **Gray**: Parsing and utilities

### Node Sizes
- **Large nodes**: High importance/complexity
- **Medium nodes**: Standard components
- **Small nodes**: Helper functions/utilities

### Connection Types
- **Solid lines**: Direct function calls
- **Dashed lines**: Data dependencies
- **Thick lines**: High-frequency connections
- **Thin lines**: Occasional usage

## 🎯 Direct Code Navigation

### Click to Code
1. **Navigate to any function node**
2. **Single click the function**
3. **VSCode automatically opens**:
   - Correct file
   - Exact line number
   - Function highlighted

### Context Menu Options
**Right-click any function node** for:
- 📖 **View Code** - Open in editor
- 🔍 **Find Usages** - See all references
- 🧪 **Run Tests** - Execute related tests
- 🐛 **Debug** - Start debugging session
- 📝 **Edit** - Open for modification
- 📚 **Documentation** - View docs

### Code Preview
**Hover over function nodes** to see:
- Function signature
- Parameter types
- Return type
- Brief description
- Complexity score

## 🔄 Real-time Features

### Live Updates
- **File changes**: Graph updates when you edit code
- **New functions**: Automatically added to graph
- **Deleted code**: Removed from visualization
- **Refactoring**: Relationships update automatically

### Synchronization
- **Current cursor**: Highlights corresponding graph node
- **Active file**: Shows related components
- **Debugging**: Highlights execution path
- **Testing**: Shows test coverage

## 🛠️ Troubleshooting

### Graph Not Loading
1. Check if workspace is open
2. Verify extension is activated
3. Try refreshing: Ctrl+Shift+P → "Reload Window"

### Nodes Not Clickable
1. Ensure you're clicking directly on the node
2. Try zooming in for better precision
3. Check if node has associated file path

### Performance Issues
1. Use search to filter large graphs
2. Collapse unused sections
3. Navigate to specific components instead of viewing all

### Text Too Small
1. Use zoom controls to increase size
2. Text automatically scales with zoom level
3. Try "Fit to Screen" for optimal sizing

## 🎓 Pro Tips

### Efficient Navigation
1. **Start broad, go specific**: Architecture → Component → Function
2. **Use search for known items**: Type function names directly
3. **Follow the data flow**: Trace connections between components
4. **Bookmark important nodes**: Right-click → Add to favorites

### Code Exploration
1. **Hover first**: Get quick info before clicking
2. **Use context menus**: Right-click for more options
3. **Follow call chains**: Click through function calls
4. **Check dependencies**: See what each function uses

### Debugging Workflow
1. **Find the bug area**: Search for relevant components
2. **Trace execution path**: Follow function call chains
3. **Check dependencies**: See what might be affected
4. **Use direct navigation**: Jump to code quickly

### Learning the Codebase
1. **Start with architecture view**: Understand overall structure
2. **Explore each component**: Drill down systematically
3. **Follow interesting paths**: See how data flows
4. **Use search for concepts**: Find all authentication-related code

## 🌟 Advanced Features

### Multi-Window Support
- Open multiple graph views
- Compare different components
- Sync with multiple editor windows

### Export Options
- Save graph as image
- Export node data as JSON
- Generate documentation from graph

### Collaboration
- Share graph views with team
- Annotate nodes with comments
- Track exploration history

This interactive graph system transforms your codebase into a living, navigable map that makes understanding and working with complex code intuitive and efficient! 🚀
