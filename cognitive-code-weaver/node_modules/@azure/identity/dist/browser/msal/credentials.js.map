{"version": 3, "file": "credentials.js", "sourceRoot": "", "sources": ["../../../src/msal/credentials.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport type { AuthenticationRecord } from \"./types.js\";\n\n/**\n * The MSAL clients `getToken` requests can receive a `correlationId` and `disableAutomaticAuthentication`.\n * (which is used to prevent `getToken` from triggering the manual authentication if `getTokenSilent`  fails).\n * @internal\n */\nexport interface CredentialFlowGetTokenOptions extends GetTokenOptions {\n  /**\n   * Unique identifier useful to track outgoing requests.\n   */\n  correlationId?: string;\n  /**\n   * Makes getToken throw if a manual authentication is necessary.\n   */\n  disableAutomaticAuthentication?: boolean;\n  /**\n   * Authority, to overwrite the default one, if necessary.\n   */\n  authority?: string;\n  /**\n   * Claims received from challenges.\n   */\n  claims?: string;\n  /**\n   * Indicates to allow Continuous Access Evaluation or not\n   */\n  enableCae?: boolean;\n  /**\n   * Client Assertion\n   */\n  getAssertion?: () => Promise<string>;\n}\n\n/**\n * Simplified representation of the internal authentication flow.\n * @internal\n */\nexport interface CredentialFlow {\n  /**\n   * Clears the MSAL cache.\n   */\n  logout(): Promise<void>;\n  /**\n   * Tries to load the active account, either from memory or from MSAL.\n   */\n  getActiveAccount(): Promise<AuthenticationRecord | undefined>;\n  /**\n   * Calls to the implementation's doGetToken method.\n   */\n  getToken(scopes?: string[], options?: CredentialFlowGetTokenOptions): Promise<AccessToken | null>;\n}\n"]}