{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,cAAc,uBAAuB,CAAC;AAKtC,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAC;AAEjF,OAAO,EACL,mBAAmB,EAEnB,4BAA4B,EAC5B,uBAAuB,EACvB,gCAAgC,EAChC,0BAA0B,EAC1B,8BAA8B,EAC9B,2BAA2B,GAE5B,MAAM,aAAa,CAAC;AAGrB,OAAO,EAAE,6BAA6B,EAAE,+BAA+B,EAAE,MAAM,iBAAiB,CAAC;AAejG,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAC;AAEjF,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAC;AAGjF,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAC;AAOjF,OAAO,EAAE,qBAAqB,EAAE,MAAM,wCAAwC,CAAC;AAG/E,OAAO,EAAE,2BAA2B,EAAE,MAAM,8CAA8C,CAAC;AAO3F,OAAO,EAAE,yBAAyB,EAAE,MAAM,4CAA4C,CAAC;AAGvF,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAC;AAEzE,OAAO,EAAE,2BAA2B,EAAE,MAAM,8CAA8C,CAAC;AAE3F,OAAO,EAAE,4BAA4B,EAAE,MAAM,+CAA+C,CAAC;AAM7F,OAAO,EAAE,yBAAyB,EAAE,MAAM,kDAAkD,CAAC;AAM7F,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAC;AAM7E,OAAO,EAAE,wBAAwB,IAAI,wBAAwB,EAAE,MAAM,2CAA2C,CAAC;AAEjH,OAAO,EAAE,2BAA2B,EAAE,MAAM,8CAA8C,CAAC;AAE3F,OAAO,EAAE,yBAAyB,EAAE,MAAM,4CAA4C,CAAC;AAQvF,OAAO,EAAE,0BAA0B,EAAE,MAAM,6CAA6C,CAAC;AAEzF,OAAO,EAAE,0BAA0B,EAAE,MAAM,6CAA6C,CAAC;AAEzF,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAC;AAC7E,OAAO,EAAE,0BAA0B,EAAE,MAAM,6CAA6C,CAAC;AAMzF,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAErD;;GAEG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,IAAI,sBAAsB,EAAE,CAAC;AACtC,CAAC;AAED,OAAO,EAAE,sBAAsB,EAAiC,MAAM,oBAAoB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport * from \"./plugins/consumer.js\";\n\nexport { IdentityPlugin } from \"./plugins/provider.js\";\n\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential.js\";\n\nexport {\n  AuthenticationError,\n  ErrorResponse,\n  AggregateAuthenticationError,\n  AuthenticationErrorName,\n  AggregateAuthenticationErrorName,\n  CredentialUnavailableError,\n  CredentialUnavailableErrorName,\n  AuthenticationRequiredError,\n  AuthenticationRequiredErrorOptions,\n} from \"./errors.js\";\n\nexport { AuthenticationRecord } from \"./msal/types.js\";\nexport { serializeAuthenticationRecord, deserializeAuthenticationRecord } from \"./msal/utils.js\";\nexport { TokenCredentialOptions } from \"./tokenCredentialOptions.js\";\nexport { MultiTenantTokenCredentialOptions } from \"./credentials/multiTenantTokenCredentialOptions.js\";\nexport { AuthorityValidationOptions } from \"./credentials/authorityValidationOptions.js\";\n// TODO: Export again once we're ready to release this feature.\n// export { RegionalAuthority } from \"./regionalAuthority\";\n\nexport { BrokerAuthOptions } from \"./credentials/brokerAuthOptions.js\";\nexport {\n  BrokerOptions,\n  BrokerEnabledOptions,\n  BrokerDisabledOptions,\n} from \"./msal/nodeFlows/brokerOptions.js\";\nexport { InteractiveCredentialOptions } from \"./credentials/interactiveCredentialOptions.js\";\n\nexport { ChainedTokenCredential } from \"./credentials/chainedTokenCredential.js\";\n\nexport { ClientSecretCredential } from \"./credentials/clientSecretCredential.js\";\nexport { ClientSecretCredentialOptions } from \"./credentials/clientSecretCredentialOptions.js\";\n\nexport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential.js\";\nexport {\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./credentials/defaultAzureCredentialOptions.js\";\n\nexport { EnvironmentCredential } from \"./credentials/environmentCredential.js\";\nexport { EnvironmentCredentialOptions } from \"./credentials/environmentCredentialOptions.js\";\n\nexport { ClientCertificateCredential } from \"./credentials/clientCertificateCredential.js\";\nexport {\n  ClientCertificateCredentialPEMConfiguration,\n  ClientCertificatePEMCertificatePath,\n  ClientCertificatePEMCertificate,\n} from \"./credentials/clientCertificateCredentialModels.js\";\nexport { ClientCertificateCredentialOptions } from \"./credentials/clientCertificateCredentialOptions.js\";\nexport { ClientAssertionCredential } from \"./credentials/clientAssertionCredential.js\";\nexport { ClientAssertionCredentialOptions } from \"./credentials/clientAssertionCredentialOptions.js\";\nexport { CredentialPersistenceOptions } from \"./credentials/credentialPersistenceOptions.js\";\nexport { AzureCliCredential } from \"./credentials/azureCliCredential.js\";\nexport { AzureCliCredentialOptions } from \"./credentials/azureCliCredentialOptions.js\";\nexport { AzureDeveloperCliCredential } from \"./credentials/azureDeveloperCliCredential.js\";\nexport { AzureDeveloperCliCredentialOptions } from \"./credentials/azureDeveloperCliCredentialOptions.js\";\nexport { InteractiveBrowserCredential } from \"./credentials/interactiveBrowserCredential.js\";\nexport {\n  InteractiveBrowserCredentialNodeOptions,\n  InteractiveBrowserCredentialInBrowserOptions,\n  BrowserLoginStyle,\n} from \"./credentials/interactiveBrowserCredentialOptions.js\";\nexport { ManagedIdentityCredential } from \"./credentials/managedIdentityCredential/index.js\";\nexport {\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n  ManagedIdentityCredentialObjectIdOptions,\n} from \"./credentials/managedIdentityCredential/options.js\";\nexport { DeviceCodeCredential } from \"./credentials/deviceCodeCredential.js\";\nexport {\n  DeviceCodePromptCallback,\n  DeviceCodeInfo,\n} from \"./credentials/deviceCodeCredentialOptions.js\";\nexport { DeviceCodeCredentialOptions } from \"./credentials/deviceCodeCredentialOptions.js\";\nexport { AzurePipelinesCredential as AzurePipelinesCredential } from \"./credentials/azurePipelinesCredential.js\";\nexport { AzurePipelinesCredentialOptions as AzurePipelinesCredentialOptions } from \"./credentials/azurePipelinesCredentialOptions.js\";\nexport { AuthorizationCodeCredential } from \"./credentials/authorizationCodeCredential.js\";\nexport { AuthorizationCodeCredentialOptions } from \"./credentials/authorizationCodeCredentialOptions.js\";\nexport { AzurePowerShellCredential } from \"./credentials/azurePowerShellCredential.js\";\nexport { AzurePowerShellCredentialOptions } from \"./credentials/azurePowerShellCredentialOptions.js\";\nexport {\n  OnBehalfOfCredentialOptions,\n  OnBehalfOfCredentialSecretOptions,\n  OnBehalfOfCredentialCertificateOptions,\n  OnBehalfOfCredentialAssertionOptions,\n} from \"./credentials/onBehalfOfCredentialOptions.js\";\nexport { UsernamePasswordCredential } from \"./credentials/usernamePasswordCredential.js\";\nexport { UsernamePasswordCredentialOptions } from \"./credentials/usernamePasswordCredentialOptions.js\";\nexport { VisualStudioCodeCredential } from \"./credentials/visualStudioCodeCredential.js\";\nexport { VisualStudioCodeCredentialOptions } from \"./credentials/visualStudioCodeCredentialOptions.js\";\nexport { OnBehalfOfCredential } from \"./credentials/onBehalfOfCredential.js\";\nexport { WorkloadIdentityCredential } from \"./credentials/workloadIdentityCredential.js\";\nexport { WorkloadIdentityCredentialOptions } from \"./credentials/workloadIdentityCredentialOptions.js\";\nexport { BrowserCustomizationOptions } from \"./credentials/browserCustomizationOptions.js\";\nexport { TokenCachePersistenceOptions } from \"./msal/nodeFlows/tokenCachePersistenceOptions.js\";\n\nexport { TokenCredential, GetTokenOptions, AccessToken } from \"@azure/core-auth\";\nexport { logger } from \"./util/logging.js\";\n\nexport { AzureAuthorityHosts } from \"./constants.js\";\n\n/**\n * Returns a new instance of the {@link DefaultAzureCredential}.\n */\nexport function getDefaultAzureCredential(): TokenCredential {\n  return new DefaultAzureCredential();\n}\n\nexport { getBearerTokenProvider, GetBearerTokenProviderOptions } from \"./tokenProvider.js\";\n"]}