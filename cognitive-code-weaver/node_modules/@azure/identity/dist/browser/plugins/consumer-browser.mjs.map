{"version": 3, "file": "consumer-browser.mjs", "sourceRoot": "", "sources": ["../../../src/plugins/consumer-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,+DAA+D;AAE/D,MAAM,UAAU,iBAAiB,CAAC,OAAgB;IAChD,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AACjF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n// This module is a shim for the plugin consumer in the browser\n\nexport function useIdentityPlugin(_plugin: unknown): void {\n  throw new Error(\"Identity plugins are not supported in browser environments.\");\n}\n"]}