{"version": 3, "file": "tenantIdUtils.js", "sourceRoot": "", "sources": ["../../../src/util/tenantIdUtils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAEvE,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAC3C,OAAO,EAAE,yBAAyB,EAAE,MAAM,gCAAgC,CAAC;AAE3E;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,MAAwB,EAAE,QAAgB;IACtE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,4KAA4K,CAC7K,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,MAAwB,EACxB,QAAiB,EACjB,QAAiB;IAEjB,IAAI,QAAQ,EAAE,CAAC;QACb,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAChC,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,QAAQ,GAAG,uBAAuB,CAAC;IACrC,CAAC;IACD,IAAI,QAAQ,KAAK,uBAAuB,EAAE,CAAC;QACzC,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mCAAmC,CACjD,0BAAqC;IAErC,IAAI,CAAC,0BAA0B,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3E,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,0BAA0B,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC7C,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,OAAO,0BAA0B,CAAC;AACpC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { ALL_TENANTS, DeveloperSignOnClientId } from \"../constants.js\";\nimport type { CredentialLogger } from \"./logging.js\";\nimport { formatError } from \"./logging.js\";\nexport { processMultiTenantRequest } from \"./processMultiTenantRequest.js\";\n\n/**\n * @internal\n */\nexport function checkTenantId(logger: CredentialLogger, tenantId: string): void {\n  if (!tenantId.match(/^[0-9a-zA-Z-.]+$/)) {\n    const error = new Error(\n      \"Invalid tenant id provided. You can locate your tenant id by following the instructions listed here: https://learn.microsoft.com/partner-center/find-ids-and-domain-names.\",\n    );\n    logger.info(formatError(\"\", error));\n    throw error;\n  }\n}\n\n/**\n * @internal\n */\nexport function resolveTenantId(\n  logger: CredentialLogger,\n  tenantId?: string,\n  clientId?: string,\n): string {\n  if (tenantId) {\n    checkTenantId(logger, tenantId);\n    return tenantId;\n  }\n  if (!clientId) {\n    clientId = DeveloperSignOnClientId;\n  }\n  if (clientId !== DeveloperSignOnClientId) {\n    return \"common\";\n  }\n  return \"organizations\";\n}\n\n/**\n * @internal\n */\nexport function resolveAdditionallyAllowedTenantIds(\n  additionallyAllowedTenants?: string[],\n): string[] {\n  if (!additionallyAllowedTenants || additionallyAllowedTenants.length === 0) {\n    return [];\n  }\n\n  if (additionallyAllowedTenants.includes(\"*\")) {\n    return ALL_TENANTS;\n  }\n\n  return additionallyAllowedTenants;\n}\n"]}