import type { CredentialLogger } from "./logging.js";
/**
 * Ensures the scopes value is an array.
 * @internal
 */
export declare function ensureScopes(scopes: string | string[]): string[];
/**
 * Throws if the received scope is not valid.
 * @internal
 */
export declare function ensureValidScopeForDevTimeCreds(scope: string, logger: CredentialLogger): void;
/**
 * Returns the resource out of a scope.
 * @internal
 */
export declare function getScopeResource(scope: string): string;
//# sourceMappingURL=scopeUtils.d.ts.map