{"version": 3, "file": "tracing.js", "sourceRoot": "", "sources": ["../../../src/util/tracing.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAE,mBAAmB,EAAE,MAAM,qBAAqB,CAAC;AAE1D;;;GAGG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,mBAAmB,CAAC;IAC/C,SAAS,EAAE,eAAe;IAC1B,WAAW,EAAE,iBAAiB;IAC9B,cAAc,EAAE,WAAW;CAC5B,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { SDK_VERSION } from \"../constants.js\";\nimport { createTracingClient } from \"@azure/core-tracing\";\n\n/**\n * Creates a span using the global tracer.\n * @internal\n */\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.AAD\",\n  packageName: \"@azure/identity\",\n  packageVersion: SDK_VERSION,\n});\n"]}