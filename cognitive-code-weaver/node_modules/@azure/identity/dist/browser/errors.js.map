{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/errors.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAyDlC,SAAS,eAAe,CAAC,aAAkB;IACzC,OAAO,CACL,aAAa;QACb,OAAO,aAAa,CAAC,KAAK,KAAK,QAAQ;QACvC,OAAO,aAAa,CAAC,iBAAiB,KAAK,QAAQ,CACpD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAE3E;;;;GAIG;AACH,MAAM,OAAO,0BAA2B,SAAQ,KAAK;IACnD,YAAY,OAAgB,EAAE,OAA6B;QACzD,2JAA2J;QAC3J,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;IAC7C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,qBAAqB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,OAAO,mBAAoB,SAAQ,KAAK;IAW5C,YACE,UAAkB,EAClB,SAA6C,EAC7C,OAA6B;QAE7B,IAAI,aAAa,GAAkB;YACjC,KAAK,EAAE,SAAS;YAChB,gBAAgB,EAAE,oEAAoE;SACvF,CAAC;QAEF,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/B,aAAa,GAAG,wCAAwC,CAAC,SAAS,CAAC,CAAC;QACtE,CAAC;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,iEAAiE;gBACjE,uBAAuB;gBACvB,MAAM,kBAAkB,GAAuB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACrE,aAAa,GAAG,wCAAwC,CAAC,kBAAkB,CAAC,CAAC;YAC/E,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;oBACvB,aAAa,GAAG;wBACd,KAAK,EAAE,iBAAiB;wBACxB,gBAAgB,EAAE,0DAA0D,SAAS,EAAE;qBACxF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG;wBACd,KAAK,EAAE,eAAe;wBACtB,gBAAgB,EAAE,oDAAoD,SAAS,EAAE;qBAClF,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,GAAG;gBACd,KAAK,EAAE,eAAe;gBACtB,gBAAgB,EAAE,oEAAoE;aACvF,CAAC;QACJ,CAAC;QAED,KAAK,CACH,GAAG,aAAa,CAAC,KAAK,iBAAiB,UAAU,oBAAoB,aAAa,CAAC,gBAAgB,GAAG;QACtG,2JAA2J;QAC3J,OAAO,CACR,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,iDAAiD;QACjD,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACtC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAAG,8BAA8B,CAAC;AAE/E;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,KAAK;IAOrD,YAAY,MAAa,EAAE,YAAqB;QAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,KAAK,CAAC,GAAG,YAAY,KAAK,WAAW,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,iDAAiD;QACjD,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;IAC/C,CAAC;CACF;AAED,SAAS,wCAAwC,CAAC,SAA6B;IAC7E,OAAO;QACL,KAAK,EAAE,SAAS,CAAC,KAAK;QACtB,gBAAgB,EAAE,SAAS,CAAC,iBAAiB;QAC7C,aAAa,EAAE,SAAS,CAAC,cAAc;QACvC,UAAU,EAAE,SAAS,CAAC,WAAW;QACjC,SAAS,EAAE,SAAS,CAAC,SAAS;QAC9B,OAAO,EAAE,SAAS,CAAC,QAAQ;KAC5B,CAAC;AACJ,CAAC;AAwBD;;GAEG;AACH,MAAM,OAAO,2BAA4B,SAAQ,KAAK;IAUpD;IACE;;OAEG;IACH,OAA2C;QAE3C,KAAK,CACH,OAAO,CAAC,OAAO;QACf,2JAA2J;QAC3J,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CACrD,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;IAC5C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * See the official documentation for more details:\n *\n * https://learn.microsoft.com/en-us/azure/active-directory/develop/v1-protocols-oauth-code#error-response-1\n *\n * NOTE: This documentation is for v1 OAuth support but the same error\n * response details still apply to v2.\n */\nexport interface ErrorResponse {\n  /**\n   * The string identifier for the error.\n   */\n  error: string;\n\n  /**\n   * The error's description.\n   */\n  errorDescription: string;\n\n  /**\n   * An array of codes pertaining to the error(s) that occurred.\n   */\n  errorCodes?: number[];\n\n  /**\n   * The timestamp at which the error occurred.\n   */\n  timestamp?: string;\n\n  /**\n   * The trace identifier for this error occurrence.\n   */\n  traceId?: string;\n\n  /**\n   * The correlation ID to be used for tracking the source of the error.\n   */\n  correlationId?: string;\n}\n\n/**\n * Used for internal deserialization of OAuth responses. Public model is ErrorResponse\n * @internal\n */\nexport interface OAuthErrorResponse {\n  error: string;\n  error_description: string;\n  error_codes?: number[];\n  timestamp?: string;\n  trace_id?: string;\n  correlation_id?: string;\n}\n\nfunction isErrorResponse(errorResponse: any): errorResponse is OAuthErrorResponse {\n  return (\n    errorResponse &&\n    typeof errorResponse.error === \"string\" &&\n    typeof errorResponse.error_description === \"string\"\n  );\n}\n\n/**\n * The Error.name value of an CredentialUnavailable\n */\nexport const CredentialUnavailableErrorName = \"CredentialUnavailableError\";\n\n/**\n * This signifies that the credential that was tried in a chained credential\n * was not available to be used as the credential. Rather than treating this as\n * an error that should halt the chain, it's caught and the chain continues\n */\nexport class CredentialUnavailableError extends Error {\n  constructor(message?: string, options?: { cause?: unknown }) {\n    // @ts-expect-error - TypeScript does not recognize this until we use ES2022 as the target; however, all our major runtimes do support the `cause` property\n    super(message, options);\n    this.name = CredentialUnavailableErrorName;\n  }\n}\n\n/**\n * The Error.name value of an AuthenticationError\n */\nexport const AuthenticationErrorName = \"AuthenticationError\";\n\n/**\n * Provides details about a failure to authenticate with Azure Active\n * Directory.  The `errorResponse` field contains more details about\n * the specific failure.\n */\nexport class AuthenticationError extends Error {\n  /**\n   * The HTTP status code returned from the authentication request.\n   */\n  public readonly statusCode: number;\n\n  /**\n   * The error response details.\n   */\n  public readonly errorResponse: ErrorResponse;\n\n  constructor(\n    statusCode: number,\n    errorBody: object | string | undefined | null,\n    options?: { cause?: unknown },\n  ) {\n    let errorResponse: ErrorResponse = {\n      error: \"unknown\",\n      errorDescription: \"An unknown error occurred and no additional details are available.\",\n    };\n\n    if (isErrorResponse(errorBody)) {\n      errorResponse = convertOAuthErrorResponseToErrorResponse(errorBody);\n    } else if (typeof errorBody === \"string\") {\n      try {\n        // Most error responses will contain JSON-formatted error details\n        // in the response body\n        const oauthErrorResponse: OAuthErrorResponse = JSON.parse(errorBody);\n        errorResponse = convertOAuthErrorResponseToErrorResponse(oauthErrorResponse);\n      } catch (e: any) {\n        if (statusCode === 400) {\n          errorResponse = {\n            error: \"invalid_request\",\n            errorDescription: `The service indicated that the request was invalid.\\n\\n${errorBody}`,\n          };\n        } else {\n          errorResponse = {\n            error: \"unknown_error\",\n            errorDescription: `An unknown error has occurred. Response body:\\n\\n${errorBody}`,\n          };\n        }\n      }\n    } else {\n      errorResponse = {\n        error: \"unknown_error\",\n        errorDescription: \"An unknown error occurred and no additional details are available.\",\n      };\n    }\n\n    super(\n      `${errorResponse.error} Status code: ${statusCode}\\nMore details:\\n${errorResponse.errorDescription},`,\n      // @ts-expect-error - TypeScript does not recognize this until we use ES2022 as the target; however, all our major runtimes do support the `cause` property\n      options,\n    );\n    this.statusCode = statusCode;\n    this.errorResponse = errorResponse;\n\n    // Ensure that this type reports the correct name\n    this.name = AuthenticationErrorName;\n  }\n}\n\n/**\n * The Error.name value of an AggregateAuthenticationError\n */\nexport const AggregateAuthenticationErrorName = \"AggregateAuthenticationError\";\n\n/**\n * Provides an `errors` array containing {@link AuthenticationError} instance\n * for authentication failures from credentials in a {@link ChainedTokenCredential}.\n */\nexport class AggregateAuthenticationError extends Error {\n  /**\n   * The array of error objects that were thrown while trying to authenticate\n   * with the credentials in a {@link ChainedTokenCredential}.\n   */\n  public errors: any[];\n\n  constructor(errors: any[], errorMessage?: string) {\n    const errorDetail = errors.join(\"\\n\");\n    super(`${errorMessage}\\n${errorDetail}`);\n    this.errors = errors;\n\n    // Ensure that this type reports the correct name\n    this.name = AggregateAuthenticationErrorName;\n  }\n}\n\nfunction convertOAuthErrorResponseToErrorResponse(errorBody: OAuthErrorResponse): ErrorResponse {\n  return {\n    error: errorBody.error,\n    errorDescription: errorBody.error_description,\n    correlationId: errorBody.correlation_id,\n    errorCodes: errorBody.error_codes,\n    timestamp: errorBody.timestamp,\n    traceId: errorBody.trace_id,\n  };\n}\n\n/**\n * Optional parameters to the {@link AuthenticationRequiredError}\n */\nexport interface AuthenticationRequiredErrorOptions {\n  /**\n   * The list of scopes for which the token will have access.\n   */\n  scopes: string[];\n  /**\n   * The options passed to the getToken request.\n   */\n  getTokenOptions?: GetTokenOptions;\n  /**\n   * The message of the error.\n   */\n  message?: string;\n  /**\n   * The underlying cause, if any, that caused the authentication to fail.\n   */\n  cause?: unknown;\n}\n\n/**\n * Error used to enforce authentication after trying to retrieve a token silently.\n */\nexport class AuthenticationRequiredError extends Error {\n  /**\n   * The list of scopes for which the token will have access.\n   */\n  public scopes: string[];\n  /**\n   * The options passed to the getToken request.\n   */\n  public getTokenOptions?: GetTokenOptions;\n\n  constructor(\n    /**\n     * Optional parameters. A message can be specified. The {@link GetTokenOptions} of the request can also be specified to more easily associate the error with the received parameters.\n     */\n    options: AuthenticationRequiredErrorOptions,\n  ) {\n    super(\n      options.message,\n      // @ts-expect-error - TypeScript does not recognize this until we use ES2022 as the target; however, all our major runtimes do support the `cause` property\n      options.cause ? { cause: options.cause } : undefined,\n    );\n    this.scopes = options.scopes;\n    this.getTokenOptions = options.getTokenOptions;\n    this.name = \"AuthenticationRequiredError\";\n  }\n}\n"]}