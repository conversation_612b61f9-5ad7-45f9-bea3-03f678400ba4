{"version": 3, "file": "identityClient.js", "sourceRoot": "", "sources": ["../../../src/client/identityClient.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAyClC,wEAWC;AAhDD,oDAAmD;AACnD,gDAA0C;AAE1C,kEAAqF;AAErF,4CAA4E;AAC5E,+EAAkF;AAClF,kDAAoE;AACpE,mDAAmD;AACnD,mDAA4C;AAG5C,gFAG2D;AAE3D,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAiB1C;;GAEG;AACH,SAAgB,8BAA8B,CAAC,OAAgC;IAC7E,iGAAiG;IACjG,IAAI,aAAa,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC;IAE3C,iFAAiF;IACjF,IAAI,kBAAM,EAAE,CAAC;QACX,aAAa,GAAG,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;IACpE,CAAC;IAED,wHAAwH;IACxH,OAAO,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,mCAAoB,CAAC;AAC/C,CAAC;AAED;;;;;;GAMG;AACH,MAAa,cAAe,SAAQ,2BAAa;IAQ/C,YAAY,OAAgC;;QAC1C,MAAM,cAAc,GAAG,qBAAqB,0BAAW,EAAE,CAAC;QAC1D,MAAM,eAAe,GAAG,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,0CAAE,eAAe;YAChE,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,eAAe,IAAI,cAAc,EAAE;YACjE,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC;QAExB,MAAM,OAAO,GAAG,8BAA8B,CAAC,OAAO,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,KAAK,+BACH,kBAAkB,EAAE,iCAAiC,EACrD,YAAY,EAAE;gBACZ,UAAU,EAAE,CAAC;aACd,IACE,OAAO,KACV,gBAAgB,EAAE;gBAChB,eAAe;aAChB,EACD,OAAO,IACP,CAAC;QAzBG,4BAAuB,GAAY,KAAK,CAAC;QA2B/C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,8BAA8B,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,8BAA8B,CAAC;QAC9F,4BAA4B;QAC5B,IAAI,CAAC,sBAAsB,qBAAQ,OAAO,CAAE,CAAC;QAE7C,2BAA2B;QAC3B,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,uBAAuB,EAAE,CAAC;YACrC,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,uBAAuB,CAAC;QACjE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAwB;QAC7C,mBAAM,CAAC,IAAI,CAAC,6CAA6C,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE,CAAC;YAChF,MAAM,UAAU,GAA4B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAE5E,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE9B,MAAM,KAAK,GAAG;gBACZ,WAAW,EAAE;oBACX,KAAK,EAAE,UAAU,CAAC,YAAY;oBAC9B,kBAAkB,EAAE,IAAA,mCAAwB,EAAC,UAAU,CAAC;oBACxD,qBAAqB,EAAE,IAAA,gCAAqB,EAAC,UAAU,CAAC;oBACxD,SAAS,EAAE,QAAQ;iBACL;gBAChB,YAAY,EAAE,UAAU,CAAC,aAAa;aACvC,CAAC;YAEF,mBAAM,CAAC,IAAI,CACT,oBAAoB,OAAO,CAAC,GAAG,gCAAgC,KAAK,CAAC,WAAW,CAAC,kBAAkB,EAAE,CACtG,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,+BAAmB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5E,mBAAM,CAAC,OAAO,CACZ,sDAAsD,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,aAAa,CAAC,gBAAgB,EAAE,CACjH,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,QAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,YAAgC,EAChC,YAAgC,EAChC,UAA2B,EAAE;QAE7B,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,mBAAM,CAAC,IAAI,CACT,2DAA2D,QAAQ,aAAa,MAAM,UAAU,CACjG,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,YAAY;YAC3B,KAAK,EAAE,MAAM;SACd,CAAC;QAEF,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC9B,aAAqB,CAAC,aAAa,GAAG,YAAY,CAAC;QACtD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,aAAa,CAAC,CAAC;QAEjD,OAAO,0BAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAA,yDAA8B,EAAC,QAAQ,CAAC,CAAC;gBAC3D,MAAM,OAAO,GAAG,IAAA,0CAAqB,EAAC;oBACpC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,SAAS,EAAE;oBACrD,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE;oBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,OAAO,EAAE,IAAA,sCAAiB,EAAC;wBACzB,MAAM,EAAE,kBAAkB;wBAC1B,cAAc,EAAE,mCAAmC;qBACpD,CAAC;oBACF,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACtD,mBAAM,CAAC,IAAI,CAAC,kDAAkD,QAAQ,EAAE,CAAC,CAAC;gBAC1E,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,IACE,GAAG,CAAC,IAAI,KAAK,mCAAuB;oBACpC,GAAG,CAAC,aAAa,CAAC,KAAK,KAAK,sBAAsB,EAClD,CAAC;oBACD,qDAAqD;oBACrD,yDAAyD;oBACzD,0CAA0C;oBAC1C,mBAAM,CAAC,IAAI,CAAC,uDAAuD,QAAQ,EAAE,CAAC,CAAC;oBAC/E,OAAO,IAAI,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,mBAAM,CAAC,OAAO,CACZ,0DAA0D,QAAQ,KAAK,GAAG,EAAE,CAC7E,CAAC;oBACF,MAAM,GAAG,CAAC;gBACZ,CAAC;YACH,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,gFAAgF;IAChF,mEAAmE;IAEnE,mBAAmB,CAAC,aAAqB;QACvC,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QACnE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACtD,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC;QAClD,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE;YACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YACpD,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC;QACF,OAAO,UAAU,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,aAAa,CAAC,aAAsB;QAClC,MAAM,GAAG,GAAG,aAAa,IAAI,eAAe,CAAC;QAC7C,MAAM,WAAW,GAAG;YAClB,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACzC,uDAAuD;YACvD,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;SACtD,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QACD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,UAAU,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED,gBAAgB,CAAC,OAA+B;;QAC9C,MAAM,SAAS,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAC3B,KAAK,CAAC,GAAG,EACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAC7B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,mBAAmB,CAAC,CAAC;QAChD,OAAO,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC;IAC3F,CAAC;IAED,yCAAyC;IAEzC,KAAK,CAAC,mBAAmB,CACvB,GAAW,EACX,OAA+B;QAE/B,MAAM,OAAO,GAAG,IAAA,0CAAqB,EAAC;YACpC,GAAG;YACH,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,OAAO,EAAE,IAAA,sCAAiB,EAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC;YAC5C,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;SACvD,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;YACvE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,GAAW,EACX,OAA+B;QAE/B,MAAM,OAAO,GAAG,IAAA,0CAAqB,EAAC;YACpC,GAAG;YACH,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI;YACnB,OAAO,EAAE,IAAA,sCAAiB,EAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,CAAC;YAC5C,uBAAuB,EAAE,IAAI,CAAC,uBAAuB;YACrD,4DAA4D;YAC5D,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SACtE,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;YACvE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACrC,CAAC;IACD;;;;;;;;;;;OAWG;IACK,cAAc,CAAC,QAA0B;QAC/C,IAAI,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YACjE,OAAO;QACT,CAAC;QACD,MAAM,cAAc,GAAG,kCAAkC,CAAC;QAC1D,IAAI,CAAC;YACH,MAAM,MAAM,GAAI,QAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,uEAAuE;gBACvE,OAAO;YACT,CAAC;YACD,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CACvD,CAAC;YAEF,mBAAM,CAAC,IAAI,CACT,sCAAsC,KAAK,gBAAgB,GAAG,0BAC5D,GAAG,IAAI,cACT,uBAAuB,GAAG,EAAE,CAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,mBAAM,CAAC,OAAO,CACZ,6FAA6F,EAC7F,CAAC,CAAC,OAAO,CACV,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjSD,wCAiSC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { INetworkModule, NetworkRequestOptions, NetworkResponse } from \"@azure/msal-node\";\nimport type { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { ServiceClient } from \"@azure/core-client\";\nimport { isNode } from \"@azure/core-util\";\nimport type { PipelineRequest, PipelineResponse } from \"@azure/core-rest-pipeline\";\nimport { createHttpHeaders, createPipelineRequest } from \"@azure/core-rest-pipeline\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport { AuthenticationError, AuthenticationErrorName } from \"../errors.js\";\nimport { getIdentityTokenEndpointSuffix } from \"../util/identityTokenEndpoint.js\";\nimport { DefaultAuthorityHost, SDK_VERSION } from \"../constants.js\";\nimport { tracingClient } from \"../util/tracing.js\";\nimport { logger } from \"../util/logging.js\";\nimport type { TokenCredentialOptions } from \"../tokenCredentialOptions.js\";\nimport type { TokenResponseParsedBody } from \"../credentials/managedIdentityCredential/utils.js\";\nimport {\n  parseExpirationTimestamp,\n  parseRefreshTimestamp,\n} from \"../credentials/managedIdentityCredential/utils.js\";\n\nconst noCorrelationId = \"noCorrelationId\";\n\n/**\n * An internal type used to communicate details of a token request's\n * response that should not be sent back as part of the access token.\n */\nexport interface TokenResponse {\n  /**\n   * The AccessToken to be returned from getToken.\n   */\n  accessToken: AccessToken;\n  /**\n   * The refresh token if the 'offline_access' scope was used.\n   */\n  refreshToken?: string;\n}\n\n/**\n * @internal\n */\nexport function getIdentityClientAuthorityHost(options?: TokenCredentialOptions): string {\n  // The authorityHost can come from options or from the AZURE_AUTHORITY_HOST environment variable.\n  let authorityHost = options?.authorityHost;\n\n  // The AZURE_AUTHORITY_HOST environment variable can only be provided in Node.js.\n  if (isNode) {\n    authorityHost = authorityHost ?? process.env.AZURE_AUTHORITY_HOST;\n  }\n\n  // If the authorityHost is not provided, we use the default one from the public cloud: https://login.microsoftonline.com\n  return authorityHost ?? DefaultAuthorityHost;\n}\n\n/**\n * The network module used by the Identity credentials.\n *\n * It allows for credentials to abort any pending request independently of the MSAL flow,\n * by calling to the `abortRequests()` method.\n *\n */\nexport class IdentityClient extends ServiceClient implements INetworkModule {\n  public authorityHost: string;\n  private allowLoggingAccountIdentifiers?: boolean;\n  private abortControllers: Map<string, AbortController[] | undefined>;\n  private allowInsecureConnection: boolean = false;\n  // used for WorkloadIdentity\n  private tokenCredentialOptions: TokenCredentialOptions;\n\n  constructor(options?: TokenCredentialOptions) {\n    const packageDetails = `azsdk-js-identity/${SDK_VERSION}`;\n    const userAgentPrefix = options?.userAgentOptions?.userAgentPrefix\n      ? `${options.userAgentOptions.userAgentPrefix} ${packageDetails}`\n      : `${packageDetails}`;\n\n    const baseUri = getIdentityClientAuthorityHost(options);\n    if (!baseUri.startsWith(\"https:\")) {\n      throw new Error(\"The authorityHost address must use the 'https' protocol.\");\n    }\n\n    super({\n      requestContentType: \"application/json; charset=utf-8\",\n      retryOptions: {\n        maxRetries: 3,\n      },\n      ...options,\n      userAgentOptions: {\n        userAgentPrefix,\n      },\n      baseUri,\n    });\n\n    this.authorityHost = baseUri;\n    this.abortControllers = new Map();\n    this.allowLoggingAccountIdentifiers = options?.loggingOptions?.allowLoggingAccountIdentifiers;\n    // used for WorkloadIdentity\n    this.tokenCredentialOptions = { ...options };\n\n    // used for ManagedIdentity\n    if (options?.allowInsecureConnection) {\n      this.allowInsecureConnection = options.allowInsecureConnection;\n    }\n  }\n\n  async sendTokenRequest(request: PipelineRequest): Promise<TokenResponse | null> {\n    logger.info(`IdentityClient: sending token request to [${request.url}]`);\n    const response = await this.sendRequest(request);\n    if (response.bodyAsText && (response.status === 200 || response.status === 201)) {\n      const parsedBody: TokenResponseParsedBody = JSON.parse(response.bodyAsText);\n\n      if (!parsedBody.access_token) {\n        return null;\n      }\n\n      this.logIdentifiers(response);\n\n      const token = {\n        accessToken: {\n          token: parsedBody.access_token,\n          expiresOnTimestamp: parseExpirationTimestamp(parsedBody),\n          refreshAfterTimestamp: parseRefreshTimestamp(parsedBody),\n          tokenType: \"Bearer\",\n        } as AccessToken,\n        refreshToken: parsedBody.refresh_token,\n      };\n\n      logger.info(\n        `IdentityClient: [${request.url}] token acquired, expires on ${token.accessToken.expiresOnTimestamp}`,\n      );\n      return token;\n    } else {\n      const error = new AuthenticationError(response.status, response.bodyAsText);\n      logger.warning(\n        `IdentityClient: authentication error. HTTP status: ${response.status}, ${error.errorResponse.errorDescription}`,\n      );\n      throw error;\n    }\n  }\n\n  async refreshAccessToken(\n    tenantId: string,\n    clientId: string,\n    scopes: string,\n    refreshToken: string | undefined,\n    clientSecret: string | undefined,\n    options: GetTokenOptions = {},\n  ): Promise<TokenResponse | null> {\n    if (refreshToken === undefined) {\n      return null;\n    }\n    logger.info(\n      `IdentityClient: refreshing access token with client ID: ${clientId}, scopes: ${scopes} started`,\n    );\n\n    const refreshParams = {\n      grant_type: \"refresh_token\",\n      client_id: clientId,\n      refresh_token: refreshToken,\n      scope: scopes,\n    };\n\n    if (clientSecret !== undefined) {\n      (refreshParams as any).client_secret = clientSecret;\n    }\n\n    const query = new URLSearchParams(refreshParams);\n\n    return tracingClient.withSpan(\n      \"IdentityClient.refreshAccessToken\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const urlSuffix = getIdentityTokenEndpointSuffix(tenantId);\n          const request = createPipelineRequest({\n            url: `${this.authorityHost}/${tenantId}/${urlSuffix}`,\n            method: \"POST\",\n            body: query.toString(),\n            abortSignal: options.abortSignal,\n            headers: createHttpHeaders({\n              Accept: \"application/json\",\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n            }),\n            tracingOptions: updatedOptions.tracingOptions,\n          });\n\n          const response = await this.sendTokenRequest(request);\n          logger.info(`IdentityClient: refreshed token for client ID: ${clientId}`);\n          return response;\n        } catch (err: any) {\n          if (\n            err.name === AuthenticationErrorName &&\n            err.errorResponse.error === \"interaction_required\"\n          ) {\n            // It's likely that the refresh token has expired, so\n            // return null so that the credential implementation will\n            // initiate the authentication flow again.\n            logger.info(`IdentityClient: interaction required for client ID: ${clientId}`);\n            return null;\n          } else {\n            logger.warning(\n              `IdentityClient: failed refreshing token for client ID: ${clientId}: ${err}`,\n            );\n            throw err;\n          }\n        }\n      },\n    );\n  }\n\n  // Here is a custom layer that allows us to abort requests that go through MSAL,\n  // since MSAL doesn't allow us to pass options all the way through.\n\n  generateAbortSignal(correlationId: string): AbortSignalLike {\n    const controller = new AbortController();\n    const controllers = this.abortControllers.get(correlationId) || [];\n    controllers.push(controller);\n    this.abortControllers.set(correlationId, controllers);\n    const existingOnAbort = controller.signal.onabort;\n    controller.signal.onabort = (...params) => {\n      this.abortControllers.set(correlationId, undefined);\n      if (existingOnAbort) {\n        existingOnAbort.apply(controller.signal, params);\n      }\n    };\n    return controller.signal;\n  }\n\n  abortRequests(correlationId?: string): void {\n    const key = correlationId || noCorrelationId;\n    const controllers = [\n      ...(this.abortControllers.get(key) || []),\n      // MSAL passes no correlation ID to the get requests...\n      ...(this.abortControllers.get(noCorrelationId) || []),\n    ];\n    if (!controllers.length) {\n      return;\n    }\n    for (const controller of controllers) {\n      controller.abort();\n    }\n    this.abortControllers.set(key, undefined);\n  }\n\n  getCorrelationId(options?: NetworkRequestOptions): string {\n    const parameter = options?.body\n      ?.split(\"&\")\n      .map((part) => part.split(\"=\"))\n      .find(([key]) => key === \"client-request-id\");\n    return parameter && parameter.length ? parameter[1] || noCorrelationId : noCorrelationId;\n  }\n\n  // The MSAL network module methods follow\n\n  async sendGetRequestAsync<T>(\n    url: string,\n    options?: NetworkRequestOptions,\n  ): Promise<NetworkResponse<T>> {\n    const request = createPipelineRequest({\n      url,\n      method: \"GET\",\n      body: options?.body,\n      allowInsecureConnection: this.allowInsecureConnection,\n      headers: createHttpHeaders(options?.headers),\n      abortSignal: this.generateAbortSignal(noCorrelationId),\n    });\n\n    const response = await this.sendRequest(request);\n\n    this.logIdentifiers(response);\n\n    return {\n      body: response.bodyAsText ? JSON.parse(response.bodyAsText) : undefined,\n      headers: response.headers.toJSON(),\n      status: response.status,\n    };\n  }\n\n  async sendPostRequestAsync<T>(\n    url: string,\n    options?: NetworkRequestOptions,\n  ): Promise<NetworkResponse<T>> {\n    const request = createPipelineRequest({\n      url,\n      method: \"POST\",\n      body: options?.body,\n      headers: createHttpHeaders(options?.headers),\n      allowInsecureConnection: this.allowInsecureConnection,\n      // MSAL doesn't send the correlation ID on the get requests.\n      abortSignal: this.generateAbortSignal(this.getCorrelationId(options)),\n    });\n\n    const response = await this.sendRequest(request);\n\n    this.logIdentifiers(response);\n\n    return {\n      body: response.bodyAsText ? JSON.parse(response.bodyAsText) : undefined,\n      headers: response.headers.toJSON(),\n      status: response.status,\n    };\n  }\n\n  /**\n   *\n   * @internal\n   */\n  getTokenCredentialOptions(): TokenCredentialOptions {\n    return this.tokenCredentialOptions;\n  }\n  /**\n   * If allowLoggingAccountIdentifiers was set on the constructor options\n   * we try to log the account identifiers by parsing the received access token.\n   *\n   * The account identifiers we try to log are:\n   * - `appid`: The application or Client Identifier.\n   * - `upn`: User Principal Name.\n   *   - It might not be available in some authentication scenarios.\n   *   - If it's not available, we put a placeholder: \"No User Principal Name available\".\n   * - `tid`: Tenant Identifier.\n   * - `oid`: Object Identifier of the authenticated user.\n   */\n  private logIdentifiers(response: PipelineResponse): void {\n    if (!this.allowLoggingAccountIdentifiers || !response.bodyAsText) {\n      return;\n    }\n    const unavailableUpn = \"No User Principal Name available\";\n    try {\n      const parsed = (response as any).parsedBody || JSON.parse(response.bodyAsText);\n      const accessToken = parsed.access_token;\n      if (!accessToken) {\n        // Without an access token allowLoggingAccountIdentifiers isn't useful.\n        return;\n      }\n      const base64Metadata = accessToken.split(\".\")[1];\n      const { appid, upn, tid, oid } = JSON.parse(\n        Buffer.from(base64Metadata, \"base64\").toString(\"utf8\"),\n      );\n\n      logger.info(\n        `[Authenticated account] Client ID: ${appid}. Tenant ID: ${tid}. User Principal Name: ${\n          upn || unavailableUpn\n        }. Object ID (user): ${oid}`,\n      );\n    } catch (e: any) {\n      logger.warning(\n        \"allowLoggingAccountIdentifiers was set, but we couldn't log the account information. Error:\",\n        e.message,\n      );\n    }\n  }\n}\n"]}