{"version": 3, "file": "consumer.js", "sourceRoot": "", "sources": ["../../../src/plugins/consumer.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AA8ClC,8CAEC;AA7CD,qEAG0C;AAE1C,gGAAuF;AAEvF;;;;GAIG;AACH,MAAM,aAAa,GAAuB;IACxC,kBAAkB,EAAE,yCAAwB;IAC5C,yBAAyB,EAAE,gDAA+B;IAC1D,uBAAuB,EAAE,uDAAuB;CACjD,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,SAAgB,iBAAiB,CAAC,MAAsB;IACtD,MAAM,CAAC,aAAa,CAAC,CAAC;AACxB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AzurePluginContext, IdentityPlugin } from \"./provider.js\";\nimport {\n  msalNodeFlowCacheControl,\n  msalNodeFlowNativeBrokerControl,\n} from \"../msal/nodeFlows/msalPlugins.js\";\n\nimport { vsCodeCredentialControl } from \"../credentials/visualStudioCodeCredential.js\";\n\n/**\n * The context passed to an Identity plugin. This contains objects that\n * plugins can use to set backend implementations.\n * @internal\n */\nconst pluginContext: AzurePluginContext = {\n  cachePluginControl: msalNodeFlowCacheControl,\n  nativeBrokerPluginControl: msalNodeFlowNativeBrokerControl,\n  vsCodeCredentialControl: vsCodeCredentialControl,\n};\n\n/**\n * Extend Azure Identity with additional functionality. Pass a plugin from\n * a plugin package, such as:\n *\n * - `@azure/identity-cache-persistence`: provides persistent token caching\n * - `@azure/identity-vscode`: provides the dependencies of\n *   `VisualStudioCodeCredential` and enables it\n *\n * Example:\n *\n * ```ts snippet:consumer_example\n * import { useIdentityPlugin, DeviceCodeCredential } from \"@azure/identity\";\n *\n * useIdentityPlugin(cachePersistencePlugin);\n * // The plugin has the capability to extend `DeviceCodeCredential` and to\n * // add middleware to the underlying credentials, such as persistence.\n * const credential = new DeviceCodeCredential({\n *   tokenCachePersistenceOptions: {\n *     enabled: true,\n *   },\n * });\n * ```\n *\n * @param plugin - the plugin to register\n */\nexport function useIdentityPlugin(plugin: IdentityPlugin): void {\n  plugin(pluginContext);\n}\n"]}