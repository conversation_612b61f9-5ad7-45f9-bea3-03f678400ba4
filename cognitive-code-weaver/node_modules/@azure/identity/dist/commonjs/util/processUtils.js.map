{"version": 3, "file": "processUtils.js", "sourceRoot": "", "sources": ["../../../src/util/processUtils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,oEAA8C;AAE9C;;;GAGG;AACU,QAAA,YAAY,GAAG;IAC1B;;;OAGG;IACH,QAAQ,CACN,IAAY,EACZ,MAAgB,EAChB,OAAwD;QAExD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;gBACrE,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5B,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACnC,CAAC;gBACD,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC5B,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACnC,CAAC;gBACD,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;oBACpB,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as childProcess from \"child_process\";\n\n/**\n * Easy to mock childProcess utils.\n * @internal\n */\nexport const processUtils = {\n  /**\n   * Promisifying childProcess.execFile\n   * @internal\n   */\n  execFile(\n    file: string,\n    params: string[],\n    options?: childProcess.ExecFileOptionsWithStringEncoding,\n  ): Promise<string | Buffer> {\n    return new Promise((resolve, reject) => {\n      childProcess.execFile(file, params, options, (error, stdout, stderr) => {\n        if (Buffer.isBuffer(stdout)) {\n          stdout = stdout.toString(\"utf8\");\n        }\n        if (Buffer.isBuffer(stderr)) {\n          stderr = stderr.toString(\"utf8\");\n        }\n        if (stderr || error) {\n          reject(stderr ? new Error(stderr) : error);\n        } else {\n          resolve(stdout);\n        }\n      });\n    });\n  },\n};\n"]}