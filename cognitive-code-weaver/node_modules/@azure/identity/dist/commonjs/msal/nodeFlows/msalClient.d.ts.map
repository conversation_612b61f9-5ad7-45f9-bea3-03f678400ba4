{"version": 3, "file": "msalClient.d.ts", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/msalClient.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,IAAI,MAAM,kBAAkB,CAAC;AAEzC,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACrE,OAAO,KAAK,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC1E,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAiB9D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACxD,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,kDAAkD,CAAC;AACjG,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,KAAK,EAAE,uCAAuC,EAAE,MAAM,0DAA0D,CAAC;AACxH,OAAO,KAAK,EAAE,4BAA4B,EAAE,MAAM,mCAAmC,CAAC;AAUtF;;GAEG;AACH,MAAM,WAAW,6BAA8B,SAAQ,eAAe;IACpE;;;;;;OAMG;IACH,8BAA8B,CAAC,EAAE,OAAO,CAAC;CAC1C;AAED;;GAEG;AACH,MAAM,WAAW,0BAA2B,SAAQ,6BAA6B;IAC/E;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,2BAA2B,CAAC,EAAE,uCAAuC,CAAC,6BAA6B,CAAC,CAAC;IACrG;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB;;;;;;;;;OASG;IACH,kBAAkB,CAChB,MAAM,EAAE,MAAM,EAAE,EAChB,kBAAkB,EAAE,MAAM,EAC1B,iBAAiB,EAAE,MAAM,GAAG,gBAAgB,GAAG,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,EACtE,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC,CAAC;IAExB;;;;;OAKG;IACH,4BAA4B,CAC1B,MAAM,EAAE,MAAM,EAAE,EAChB,OAAO,EAAE,0BAA0B,GAClC,OAAO,CAAC,WAAW,CAAC,CAAC;IACxB;;;;;;;;OAQG;IACH,0BAA0B,CACxB,MAAM,EAAE,MAAM,EAAE,EAChB,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC,CAAC;IACxB;;;;;;;OAOG;IACH,oBAAoB,CAClB,MAAM,EAAE,MAAM,EAAE,EAChB,kBAAkB,EAAE,wBAAwB,EAC5C,OAAO,CAAC,EAAE,6BAA6B,GACtC,OAAO,CAAC,WAAW,CAAC,CAAC;IACxB;;;;;;;OAOG;IACH,2BAA2B,CACzB,MAAM,EAAE,MAAM,EAAE,EAChB,WAAW,EAAE,gBAAgB,EAC7B,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC,CAAC;IAExB;;;;;;;OAOG;IACH,yBAAyB,CACvB,MAAM,EAAE,MAAM,EAAE,EAChB,eAAe,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,EACtC,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC,CAAC;IAExB;;;;;;;OAOG;IACH,sBAAsB,CACpB,MAAM,EAAE,MAAM,EAAE,EAChB,YAAY,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,eAAe,GACxB,OAAO,CAAC,WAAW,CAAC,CAAC;IAExB;;;;;;;;;;;OAWG;IACH,2BAA2B,CACzB,MAAM,EAAE,MAAM,EAAE,EAChB,WAAW,EAAE,MAAM,EACnB,iBAAiB,EAAE,MAAM,EACzB,YAAY,CAAC,EAAE,MAAM,EACrB,OAAO,CAAC,EAAE,6BAA6B,GACtC,OAAO,CAAC,WAAW,CAAC,CAAC;IAExB;;;;OAIG;IACH,gBAAgB,IAAI,oBAAoB,GAAG,SAAS,CAAC;CACtD;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;IAE9B;;OAEG;IACH,4BAA4B,CAAC,EAAE,4BAA4B,CAAC;IAE5D;;OAEG;IACH,aAAa,CAAC,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC,eAAe,CAAC,CAAC;IAE1E;;OAEG;IACH,cAAc,CAAC,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAE5E;;OAEG;IACH,sBAAsB,CAAC,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC;IAElE;;OAEG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAC;IAEnC;;OAEG;IACH,MAAM,CAAC,EAAE,gBAAgB,CAAC;IAE1B;;OAEG;IACH,oBAAoB,CAAC,EAAE,oBAAoB,CAAC;CAC7C;AAED;;;;;;;GAOG;AACH,wBAAgB,yBAAyB,CACvC,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,iBAAiB,GAAE,iBAAsB,GACxC,IAAI,CAAC,aAAa,CAoCpB;AAyBD;;;;;;;;;GASG;AACH,wBAAgB,gBAAgB,CAC9B,QAAQ,EAAE,MAAM,EAChB,QAAQ,EAAE,MAAM,EAChB,uBAAuB,GAAE,iBAAsB,GAC9C,UAAU,CA0gBZ"}