{"version": 3, "file": "brokerOptions.js", "sourceRoot": "", "sources": ["../../../../src/msal/nodeFlows/brokerOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * Parameters that enable WAM broker authentication in the InteractiveBrowserCredential.\n */\nexport type BrokerOptions = BrokerEnabledOptions | BrokerDisabledOptions;\n\n/**\n * Parameters when WAM broker authentication is disabled.\n */\nexport interface BrokerDisabledOptions {\n  /**\n   * If set to true, broker will be enabled for WAM support on Windows.\n   */\n  enabled: false;\n\n  /**\n   * If set to true, MSA account will be passed through, required for WAM authentication.\n   */\n  legacyEnableMsaPassthrough?: undefined;\n  /**\n   * Window handle for parent window, required for WAM authentication.\n   */\n  parentWindowHandle: undefined;\n}\n\n/**\n * Parameters when WAM broker authentication is enabled.\n */\nexport interface BrokerEnabledOptions {\n  /**\n   * If set to true, broker will be enabled for WAM support on Windows.\n   */\n  enabled: true;\n  /**\n   * If set to true, MSA account will be passed through, required for WAM authentication.\n   */\n  legacyEnableMsaPassthrough?: boolean;\n  /**\n   * Window handle for parent window, required for WAM authentication.\n   */\n  parentWindowHandle: Uint8Array;\n\n  /**\n   * If set to true, the credential will attempt to use the default broker account for authentication before falling back to interactive authentication.\n   * Default is set to false.\n   */\n  useDefaultBrokerAccount?: boolean;\n}\n"]}