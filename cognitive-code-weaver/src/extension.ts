import * as vscode from 'vscode';
import { LLMClient } from './core/llm-client';
import { logger } from './core/logger';
import { InteractiveGraphViewer } from './ui/interactive-graph-viewer';

export async function activate(context: vscode.ExtensionContext) {
  try {
    // Initialize logger
    logger.info('Extension', 'Activating Cognitive Code Weaver extension');

    // Initialize LLM components
    const llmClient = new LLMClient();

    // Initialize Interactive Graph Viewer
    let graphViewer: InteractiveGraphViewer | undefined;

  // Register commands
  context.subscriptions.push(
    // Main Cognitive Code Weaver Commands
    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.analyzeWorkspace',
      async () => {
        vscode.window.showInformationMessage('Workspace analysis feature is being prepared. Please use other commands for now.');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.askQuestion',
      async () => {
        const question = await vscode.window.showInputBox({
          prompt: 'Ask a question about your code',
          placeHolder: 'e.g., How does authentication work in this project?'
        });

        if (question) {
          try {
            await vscode.window.withProgress(
              {
                location: vscode.ProgressLocation.Notification,
                title: 'Processing your question...',
                cancellable: false
              },
              async () => {
                // Simple LLM response for now
                const response = await llmClient.generateResponse([
                  {
                    role: 'system',
                    content: 'You are a helpful code assistant. Answer questions about code clearly and concisely.'
                  },
                  {
                    role: 'user',
                    content: question
                  }
                ]);

                // Show result in a new document
                const doc = await vscode.workspace.openTextDocument({
                  content: `Question: ${question}\n\nAnswer:\n${response.content}`,
                  language: 'markdown'
                });
                await vscode.window.showTextDocument(doc);
              }
            );
          } catch (error) {
            vscode.window.showErrorMessage(`Error processing question: ${error}`);
          }
        }
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.explainCode',
      async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
          vscode.window.showErrorMessage('No active editor found');
          return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);

        if (!selectedText) {
          vscode.window.showWarningMessage('Please select some code to explain');
          return;
        }

        try {
          await vscode.window.withProgress(
            {
              location: vscode.ProgressLocation.Notification,
              title: 'Explaining selected code...',
              cancellable: false
            },
            async () => {
              const explanation = await llmClient.generateResponse([
                {
                  role: 'system',
                  content: 'You are a code explanation expert. Explain the selected code clearly and concisely.'
                },
                {
                  role: 'user',
                  content: `Please explain this code:\n\n${selectedText}`
                }
              ]);

              // Show explanation in a new document
              const doc = await vscode.workspace.openTextDocument({
                content: `Code Explanation\n================\n\nSelected Code:\n\`\`\`\n${selectedText}\n\`\`\`\n\nExplanation:\n${explanation.content}`,
                language: 'markdown'
              });
              await vscode.window.showTextDocument(doc, vscode.ViewColumn.Beside);
            }
          );
        } catch (error) {
          vscode.window.showErrorMessage(`Error explaining code: ${error}`);
        }
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.findSimilar',
      async () => {
        vscode.window.showInformationMessage('Find Similar Code feature is being prepared. Please use other commands for now.');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.showStatus',
      () => {
        vscode.window.showInformationMessage('Cognitive Code Weaver is active and ready!');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.showDetails',
      async () => {
        const message = `Cognitive Code Weaver Status:\n- Extension: Active\n- LLM Client: Ready\n- Database: Connected`;
        vscode.window.showInformationMessage(message);
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.refreshExplorer',
      () => {
        vscode.commands.executeCommand('workbench.action.reloadWindow');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.openSettings',
      () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'cognitiveCodeWeaver');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.findSimilar',
      async () => {
        vscode.window.showInformationMessage('Find Similar Code feature is being prepared. Please use other commands for now.');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.showStatus',
      () => {
        vscode.window.showInformationMessage('Cognitive Code Weaver is active and ready!');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.showDetails',
      async () => {
        const message = `Cognitive Code Weaver Status:\n- Extension: Active\n- LLM Client: Ready\n- Database: Connected`;
        vscode.window.showInformationMessage(message);
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.refreshExplorer',
      () => {
        vscode.commands.executeCommand('workbench.action.reloadWindow');
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.testLLMConnection',
      async () => {
        try {
          await vscode.window.withProgress(
            {
              location: vscode.ProgressLocation.Notification,
              title: 'Testing LLM connection...',
              cancellable: false
            },
            async (progress) => {
              progress.report({ message: 'Validating configuration...' });

              // Test configuration
              const isValid = await llmClient.validateConfiguration();
              if (!isValid) {
                vscode.window.showErrorMessage('LLM configuration is invalid. Please check your API key in settings (Ctrl+Shift+P → "Preferences: Open Settings" → search "cognitiveCodeWeaver").');
                return;
              }

              progress.report({ message: 'Testing API connection...' });

              // Test simple request
              const response = await llmClient.generateResponse([
                {
                  role: 'user',
                  content: 'Hello! Please respond with "LLM connection successful!"'
                }
              ]);

              vscode.window.showInformationMessage(`✅ LLM Connection Test Successful!\n\nProvider: ${llmClient.getProviderInfo().name}\nResponse: ${response.content}`);
            }
          );
        } catch (error) {
          logger.error('Extension', `LLM connection test failed: ${error}`);
          vscode.window.showErrorMessage(`❌ LLM Test Failed: ${error}\n\nPlease check:\n1. Your API key is correct\n2. You have internet connection\n3. The LLM service is available`);
        }
      }
    ),

    vscode.commands.registerCommand(
      'cognitiveCodeWeaver.showInteractiveGraph',
      async () => {
        try {
          if (graphViewer) {
            graphViewer.show();
          } else {
            graphViewer = new InteractiveGraphViewer(context);
            graphViewer.show();
          }
          logger.info('Extension', 'Interactive graph viewer opened');
        } catch (error) {
          logger.error('Extension', `Failed to open interactive graph: ${error}`);
          vscode.window.showErrorMessage(`Failed to open interactive graph: ${error}`);
        }
      }
    )
  );

  // Register other extension commands and functionality

    logger.info('Extension', 'Cognitive Code Weaver extension activated');
    vscode.window.showInformationMessage('🧠 Cognitive Code Weaver is ready! Use Ctrl+Shift+P and search for "Cognitive Code Weaver" commands.');

  } catch (error) {
    logger.error('Extension', `Failed to activate Cognitive Code Weaver extension: ${error}`);
    vscode.window.showErrorMessage(`Failed to activate Cognitive Code Weaver: ${error}. Please check your configuration and try reloading the window.`);
    throw error;
  }
}

export async function deactivate() {
  // Clean up resources
  logger.info('Extension', 'Deactivating Cognitive Code Weaver extension');

  try {
    // Clean up any resources here
    logger.info('Extension', 'Cognitive Code Weaver extension deactivated');
  } catch (error) {
    logger.error('Extension', `Error during extension deactivation: ${error}`);
  }
}
