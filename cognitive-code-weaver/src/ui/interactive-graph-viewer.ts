/**
 * Interactive Graph Viewer - Dynamic, zoomable, clickable graph system
 * Provides multi-level navigation with direct code linking
 */

import * as vscode from 'vscode';
import * as d3 from 'd3';

export interface GraphNode {
    id: string;
    label: string;
    type: 'component' | 'agent' | 'function' | 'class';
    filePath?: string;
    lineNumber?: number;
    children?: GraphNode[];
    expanded?: boolean;
    metadata?: {
        description?: string;
        parameters?: string[];
        returnType?: string;
        complexity?: number;
    };
}

export interface GraphLink {
    source: string;
    target: string;
    type: 'calls' | 'inherits' | 'uses' | 'contains';
    weight?: number;
}

export class InteractiveGraphViewer {
    private webviewPanel: vscode.WebviewPanel;
    private currentLevel: 'architecture' | 'component' | 'function' = 'architecture';
    private currentComponent: string | null = null;
    private breadcrumb: string[] = [];
    private zoomLevel: number = 1;
    private graphData: { nodes: GraphNode[], links: GraphLink[] };

    constructor(context: vscode.ExtensionContext) {
        this.webviewPanel = vscode.window.createWebviewPanel(
            'interactiveGraph',
            'Interactive Code Graph',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(context.extensionUri, 'media'),
                    vscode.Uri.joinPath(context.extensionUri, 'node_modules', 'd3')
                ]
            }
        );

        this.setupWebview(context);
        this.setupMessageHandling();
        this.loadArchitectureGraph();
    }

    private setupWebview(context: vscode.ExtensionContext): void {
        const d3Uri = this.webviewPanel.webview.asWebviewUri(
            vscode.Uri.joinPath(context.extensionUri, 'node_modules', 'd3', 'dist', 'd3.min.js')
        );

        this.webviewPanel.webview.html = this.getWebviewContent(d3Uri);
    }

    private getWebviewContent(d3Uri: vscode.Uri): string {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Interactive Code Graph</title>
            <script src="${d3Uri}"></script>
            <style>
                body { 
                    margin: 0; 
                    padding: 0; 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: #1e1e1e;
                    color: #ffffff;
                }
                
                .graph-container {
                    width: 100vw;
                    height: 100vh;
                    position: relative;
                    overflow: hidden;
                }
                
                .controls {
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    z-index: 1000;
                    background: rgba(30, 30, 30, 0.9);
                    padding: 10px;
                    border-radius: 8px;
                    border: 1px solid #404040;
                }
                
                .breadcrumb {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    z-index: 1000;
                    background: rgba(30, 30, 30, 0.9);
                    padding: 10px;
                    border-radius: 8px;
                    border: 1px solid #404040;
                }
                
                .search-box {
                    position: absolute;
                    bottom: 10px;
                    left: 10px;
                    z-index: 1000;
                    background: rgba(30, 30, 30, 0.9);
                    padding: 10px;
                    border-radius: 8px;
                    border: 1px solid #404040;
                }
                
                .node {
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .node:hover {
                    stroke-width: 3px;
                    filter: brightness(1.2);
                }
                
                .node-label {
                    font-size: 12px;
                    font-weight: bold;
                    text-anchor: middle;
                    pointer-events: none;
                    fill: #ffffff;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                }
                
                .link {
                    stroke: #666;
                    stroke-width: 2px;
                    marker-end: url(#arrowhead);
                }
                
                .tooltip {
                    position: absolute;
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 10px;
                    border-radius: 5px;
                    font-size: 12px;
                    pointer-events: none;
                    z-index: 1001;
                    max-width: 300px;
                    border: 1px solid #666;
                }
                
                button {
                    background: #007acc;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    margin: 2px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                }
                
                button:hover {
                    background: #005a9e;
                }
                
                input, select {
                    background: #3c3c3c;
                    color: white;
                    border: 1px solid #666;
                    padding: 6px;
                    border-radius: 4px;
                    margin: 2px;
                }
                
                .minimap {
                    position: absolute;
                    bottom: 10px;
                    right: 10px;
                    width: 200px;
                    height: 150px;
                    background: rgba(30, 30, 30, 0.9);
                    border: 1px solid #404040;
                    border-radius: 8px;
                }
            </style>
        </head>
        <body>
            <div class="graph-container">
                <!-- Controls -->
                <div class="controls">
                    <button onclick="zoomIn()">🔍+ Zoom In</button>
                    <button onclick="zoomOut()">🔍- Zoom Out</button>
                    <button onclick="fitToScreen()">📐 Fit Screen</button>
                    <button onclick="resetView()">🔄 Reset</button>
                    <br>
                    <button onclick="showArchitecture()">🏗️ Architecture</button>
                    <button onclick="collapseAll()">📁 Collapse All</button>
                    <button onclick="expandAll()">📂 Expand All</button>
                </div>
                
                <!-- Breadcrumb -->
                <div class="breadcrumb">
                    <span id="breadcrumb-content">Architecture View</span>
                </div>
                
                <!-- Search -->
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="Search components, functions..." onkeyup="filterGraph(this.value)">
                    <select id="filter-select" onchange="filterByType(this.value)">
                        <option value="all">All</option>
                        <option value="component">Components</option>
                        <option value="agent">Agents</option>
                        <option value="function">Functions</option>
                        <option value="class">Classes</option>
                    </select>
                </div>
                
                <!-- Minimap -->
                <div class="minimap">
                    <svg id="minimap-svg" width="200" height="150"></svg>
                </div>
                
                <!-- Main Graph -->
                <svg id="main-graph" width="100%" height="100%">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                        </marker>
                    </defs>
                </svg>
                
                <!-- Tooltip -->
                <div id="tooltip" class="tooltip" style="display: none;"></div>
            </div>
            
            <script>
                // Graph state
                let currentData = null;
                let currentZoom = 1;
                let currentTransform = { x: 0, y: 0 };
                let simulation = null;
                
                // Initialize graph
                const svg = d3.select("#main-graph");
                const width = window.innerWidth;
                const height = window.innerHeight;
                
                const g = svg.append("g");
                
                // Zoom behavior
                const zoom = d3.zoom()
                    .scaleExtent([0.1, 4])
                    .on("zoom", (event) => {
                        currentZoom = event.transform.k;
                        currentTransform = { x: event.transform.x, y: event.transform.y };
                        g.attr("transform", event.transform);
                        updateMinimap();
                    });
                
                svg.call(zoom);
                
                // Graph functions
                function renderGraph(data) {
                    currentData = data;
                    
                    // Clear previous graph
                    g.selectAll("*").remove();
                    
                    // Create simulation
                    simulation = d3.forceSimulation(data.nodes)
                        .force("link", d3.forceLink(data.links).id(d => d.id).distance(100))
                        .force("charge", d3.forceManyBody().strength(-300))
                        .force("center", d3.forceCenter(width / 2, height / 2))
                        .force("collision", d3.forceCollide().radius(50));
                    
                    // Create links
                    const link = g.append("g")
                        .selectAll("line")
                        .data(data.links)
                        .enter().append("line")
                        .attr("class", "link")
                        .style("stroke-width", d => Math.sqrt(d.weight || 1) * 2);
                    
                    // Create nodes
                    const node = g.append("g")
                        .selectAll("g")
                        .data(data.nodes)
                        .enter().append("g")
                        .attr("class", "node")
                        .call(d3.drag()
                            .on("start", dragstarted)
                            .on("drag", dragged)
                            .on("end", dragended));
                    
                    // Add node circles
                    node.append("circle")
                        .attr("r", d => getNodeSize(d))
                        .style("fill", d => getNodeColor(d))
                        .style("stroke", "#fff")
                        .style("stroke-width", 2);
                    
                    // Add node labels
                    node.append("text")
                        .attr("class", "node-label")
                        .attr("dy", ".35em")
                        .text(d => d.label)
                        .style("font-size", d => Math.max(8, getNodeSize(d) / 3) + "px");
                    
                    // Add click handlers
                    node.on("click", handleNodeClick)
                        .on("mouseover", showTooltip)
                        .on("mouseout", hideTooltip);
                    
                    // Update positions on simulation tick
                    simulation.on("tick", () => {
                        link
                            .attr("x1", d => d.source.x)
                            .attr("y1", d => d.source.y)
                            .attr("x2", d => d.target.x)
                            .attr("y2", d => d.target.y);
                        
                        node
                            .attr("transform", d => \`translate(\${d.x},\${d.y})\`);
                    });
                }
                
                function getNodeSize(node) {
                    const sizes = {
                        'component': 40,
                        'agent': 35,
                        'function': 25,
                        'class': 30
                    };
                    return sizes[node.type] || 20;
                }
                
                function getNodeColor(node) {
                    const colors = {
                        'component': '#4CAF50',
                        'agent': '#2196F3',
                        'function': '#FF9800',
                        'class': '#9C27B0'
                    };
                    return colors[node.type] || '#757575';
                }
                
                function handleNodeClick(event, node) {
                    if (node.filePath) {
                        // Open file in VSCode
                        vscode.postMessage({
                            command: 'openFile',
                            filePath: node.filePath,
                            lineNumber: node.lineNumber || 1
                        });
                    } else if (node.children) {
                        // Expand component
                        expandComponent(node.id);
                    }
                }
                
                function showTooltip(event, node) {
                    const tooltip = d3.select("#tooltip");
                    let content = \`<strong>\${node.label}</strong><br>\`;
                    content += \`Type: \${node.type}<br>\`;
                    if (node.filePath) content += \`File: \${node.filePath}<br>\`;
                    if (node.metadata) {
                        if (node.metadata.description) content += \`\${node.metadata.description}<br>\`;
                        if (node.metadata.parameters) content += \`Parameters: \${node.metadata.parameters.join(', ')}<br>\`;
                    }
                    
                    tooltip
                        .style("display", "block")
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 10) + "px")
                        .html(content);
                }
                
                function hideTooltip() {
                    d3.select("#tooltip").style("display", "none");
                }
                
                // Control functions
                function zoomIn() {
                    svg.transition().call(zoom.scaleBy, 1.5);
                }
                
                function zoomOut() {
                    svg.transition().call(zoom.scaleBy, 1 / 1.5);
                }
                
                function fitToScreen() {
                    if (!currentData) return;
                    
                    const bounds = g.node().getBBox();
                    const fullWidth = width;
                    const fullHeight = height;
                    const scale = 0.9 / Math.max(bounds.width / fullWidth, bounds.height / fullHeight);
                    const translate = [fullWidth / 2 - scale * bounds.x - scale * bounds.width / 2, 
                                     fullHeight / 2 - scale * bounds.y - scale * bounds.height / 2];
                    
                    svg.transition()
                        .duration(750)
                        .call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
                }
                
                function resetView() {
                    svg.transition()
                        .duration(750)
                        .call(zoom.transform, d3.zoomIdentity);
                }
                
                // Message handling
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.command) {
                        case 'updateGraph':
                            renderGraph(message.data);
                            break;
                        case 'updateBreadcrumb':
                            document.getElementById('breadcrumb-content').textContent = message.breadcrumb.join(' > ');
                            break;
                    }
                });
                
                // Drag functions
                function dragstarted(event, d) {
                    if (!event.active) simulation.alphaTarget(0.3).restart();
                    d.fx = d.x;
                    d.fy = d.y;
                }
                
                function dragged(event, d) {
                    d.fx = event.x;
                    d.fy = event.y;
                }
                
                function dragended(event, d) {
                    if (!event.active) simulation.alphaTarget(0);
                    d.fx = null;
                    d.fy = null;
                }
                
                // Initialize with architecture view
                vscode.postMessage({ command: 'requestArchitectureGraph' });
            </script>
        </body>
        </html>
        `;
    }

    private setupMessageHandling(): void {
        this.webviewPanel.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'openFile':
                        this.openFile(message.filePath, message.lineNumber);
                        break;
                    case 'requestArchitectureGraph':
                        this.loadArchitectureGraph();
                        break;
                    case 'expandComponent':
                        this.expandComponent(message.componentId);
                        break;
                }
            }
        );
    }

    private async openFile(filePath: string, lineNumber: number = 1): Promise<void> {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            const editor = await vscode.window.showTextDocument(document);
            const position = new vscode.Position(lineNumber - 1, 0);
            editor.selection = new vscode.Selection(position, position);
            editor.revealRange(new vscode.Range(position, position));
        } catch (error) {
            vscode.window.showErrorMessage(`Could not open file: ${filePath}`);
        }
    }

    private loadArchitectureGraph(): void {
        const architectureData = this.getArchitectureGraphData();
        this.updateGraph(architectureData);
        this.updateBreadcrumb(['Architecture']);
    }

    private expandComponent(componentId: string): void {
        const componentData = this.getComponentGraphData(componentId);
        this.updateGraph(componentData);
        this.updateBreadcrumb(['Architecture', componentId]);
    }

    private updateGraph(data: { nodes: GraphNode[], links: GraphLink[] }): void {
        this.graphData = data;
        this.webviewPanel.webview.postMessage({
            command: 'updateGraph',
            data: data
        });
    }

    private updateBreadcrumb(breadcrumb: string[]): void {
        this.breadcrumb = breadcrumb;
        this.webviewPanel.webview.postMessage({
            command: 'updateBreadcrumb',
            breadcrumb: breadcrumb
        });
    }

    private getArchitectureGraphData(): { nodes: GraphNode[], links: GraphLink[] } {
        // This would be populated from actual code analysis
        return {
            nodes: [
                {
                    id: 'ui',
                    label: 'User Interface\nLayer',
                    type: 'component',
                    children: []
                },
                {
                    id: 'agents',
                    label: 'Agent System',
                    type: 'component',
                    children: []
                },
                {
                    id: 'llm',
                    label: 'LLM Integration',
                    type: 'component',
                    children: []
                },
                {
                    id: 'cognitive',
                    label: 'Cognitive\nProcessing',
                    type: 'component',
                    children: []
                },
                {
                    id: 'knowledge',
                    label: 'Knowledge Graph',
                    type: 'component',
                    children: []
                },
                {
                    id: 'parsing',
                    label: 'Code Parsing',
                    type: 'component',
                    children: []
                }
            ],
            links: [
                { source: 'ui', target: 'agents', type: 'uses' },
                { source: 'agents', target: 'llm', type: 'uses' },
                { source: 'agents', target: 'cognitive', type: 'uses' },
                { source: 'cognitive', target: 'knowledge', type: 'uses' },
                { source: 'cognitive', target: 'parsing', type: 'uses' },
                { source: 'knowledge', target: 'parsing', type: 'uses' }
            ]
        };
    }

    private getComponentGraphData(componentId: string): { nodes: GraphNode[], links: GraphLink[] } {
        // This would be populated from actual code analysis
        // For now, returning sample data for agents component
        if (componentId === 'agents') {
            return {
                nodes: [
                    {
                        id: 'master-agent',
                        label: 'Master Agent',
                        type: 'agent',
                        filePath: 'src/agents/master-agent.ts',
                        lineNumber: 1
                    },
                    {
                        id: 'cognitive-agent',
                        label: 'Cognitive Agent',
                        type: 'agent',
                        filePath: 'src/agents/cognitive-agent.ts',
                        lineNumber: 1
                    },
                    {
                        id: 'planner-agent',
                        label: 'Planner Agent',
                        type: 'agent',
                        filePath: 'src/agents/sub-agents/planner-agent.ts',
                        lineNumber: 1
                    },
                    {
                        id: 'code-reader-agent',
                        label: 'Code Reader\nAgent',
                        type: 'agent',
                        filePath: 'src/agents/sub-agents/code-reader-agent.ts',
                        lineNumber: 1
                    },
                    {
                        id: 'reasoner-agent',
                        label: 'Reasoner Agent',
                        type: 'agent',
                        filePath: 'src/agents/sub-agents/reasoner-agent.ts',
                        lineNumber: 1
                    }
                ],
                links: [
                    { source: 'master-agent', target: 'cognitive-agent', type: 'uses' },
                    { source: 'master-agent', target: 'planner-agent', type: 'uses' },
                    { source: 'master-agent', target: 'code-reader-agent', type: 'uses' },
                    { source: 'master-agent', target: 'reasoner-agent', type: 'uses' }
                ]
            };
        }
        
        return { nodes: [], links: [] };
    }

    public show(): void {
        this.webviewPanel.reveal();
    }

    public dispose(): void {
        this.webviewPanel.dispose();
    }
}
