# 🧠 Cognitive Code Weaver - Debug Helper & Architecture Breakdown

## 📋 Table of Contents
1. [Layer 1: VSCode Extension Interface](#layer-1-vscode-extension-interface)
2. [Layer 2: Agent Orchestration](#layer-2-agent-orchestration)
3. [Layer 3: LLM Integration](#layer-3-llm-integration)
4. [Layer 4: Cognitive Processing](#layer-4-cognitive-processing)
5. [Layer 5: Knowledge Graph](#layer-5-knowledge-graph)
6. [Layer 6: Data Parsing & Analysis](#layer-6-data-parsing--analysis)
7. [Debug Workflows](#debug-workflows)
8. [Component Dependencies](#component-dependencies)

---

## Layer 1: VSCode Extension Interface
**Purpose**: User interaction and VSCode integration

### 1.1 Extension Core (`src/extension.ts`)
- **Entry Point**: `activate()` and `deactivate()`
- **Command Registration**: All VSCode commands
- **Status**: ✅ Implemented
- **Debug Points**:
  - Extension activation failures
  - Command registration issues
  - LLM client initialization

### 1.2 UI Components (`src/ui/`)
- **WebView Provider** (`webview-provider.ts`): Chat interface
- **Tree Provider** (`tree-provider.ts`): Explorer view
- **Graph Visualizer** (`graph-visualizer.ts`): Interactive graph display
- **Status Bar** (`status-bar.ts`): Extension status
- **Debug Points**:
  - WebView communication failures
  - Graph rendering performance
  - UI state synchronization

### 1.3 VSCode Integration (`src/vscode-extension/`)
- **Commands** (`commands.ts`): Command implementations
- **Extension** (`extension.ts`): Core extension logic
- **Status**: ✅ Implemented
- **Debug Points**:
  - Command execution errors
  - Context menu integration
  - Keyboard shortcut conflicts

---

## Layer 2: Agent Orchestration
**Purpose**: Multi-agent coordination and task management

### 2.1 Master Agent (`src/agents/master-agent.ts`)
- **Role**: Central orchestrator for all sub-agents
- **Key Methods**:
  - `processQuery()`: Main query processing
  - `createTaskPlan()`: Task decomposition
  - `executeTaskPlan()`: Agent coordination
- **Status**: ✅ Implemented
- **Debug Points**:
  - Task plan creation failures
  - Sub-agent communication errors
  - Memory management issues

### 2.2 Cognitive Agent (`src/agents/cognitive-agent.ts`)
- **Role**: Main intelligence coordinator
- **Components**:
  - Parser management
  - Graph database interaction
  - Semantic analysis coordination
  - Retrieval engine management
- **Status**: ✅ Implemented
- **Debug Points**:
  - Workspace analysis failures
  - Component initialization errors
  - Query processing bottlenecks

### 2.3 Sub-Agent Registry (`src/agents/sub-agent-registry.ts`)
- **Role**: Manages specialized sub-agents
- **Sub-Agents**:
  - `PlannerAgent`: Task planning
  - `CodeReaderAgent`: Code analysis
  - `BugDetectorAgent`: Issue detection
  - `ReasonerAgent`: Logic reasoning
  - `RefactorerAgent`: Code improvement
  - `TesterAgent`: Test execution
  - `GraphMindAgent`: Graph reasoning
- **Status**: ✅ Implemented
- **Debug Points**:
  - Sub-agent registration failures
  - Agent execution timeouts
  - Result aggregation errors

### 2.4 Agent Memory (`src/agents/agent-memory.ts`)
- **Role**: Persistent memory for agents
- **Features**:
  - Query history
  - Learning from interactions
  - Context persistence
- **Status**: ✅ Implemented
- **Debug Points**:
  - Memory storage failures
  - Context retrieval errors
  - Memory cleanup issues

---

## Layer 3: LLM Integration
**Purpose**: Language model communication and orchestration

### 3.1 LLM Client (`src/core/llm-client.ts`)
- **Providers**: OpenAI, Anthropic, Mistral
- **Features**:
  - Multi-provider support
  - Error handling and retries
  - Configuration validation
- **Status**: ✅ Implemented
- **Debug Points**:
  - API key validation
  - Provider connectivity
  - Rate limiting issues
  - Response parsing errors

### 3.2 LLM Orchestrator (`src/llm/llm-orchestrator.ts`)
- **Role**: Hybrid local/cloud LLM management
- **Features**:
  - Request routing
  - Performance metrics
  - Fallback strategies
- **Status**: ✅ Implemented
- **Debug Points**:
  - Routing decision errors
  - Local LLM failures
  - Metric collection issues

### 3.3 Local LLM Client (`src/llm/local-llm-client.ts`)
- **Role**: Local model integration
- **Status**: ✅ Implemented
- **Debug Points**:
  - Model loading failures
  - Resource constraints
  - Performance issues

---

## Layer 4: Cognitive Processing
**Purpose**: Intelligent analysis and understanding

### 4.1 Retrieval Engine (`src/retrieval/retrieval-engine.ts`)
- **Strategies**:
  - `SemanticRetrievalStrategy`: Vector similarity
  - `StructuralRetrievalStrategy`: Graph traversal
  - `ContextualRetrievalStrategy`: User patterns
- **Status**: ✅ Implemented
- **Debug Points**:
  - Strategy selection errors
  - Ranking algorithm issues
  - Context assembly failures

### 4.2 Semantic Analyzer (`src/semantic/semantic-analyzer.ts`)
- **Features**:
  - Concept extraction
  - Topic modeling
  - Similarity calculation
  - Pattern detection
- **Status**: ✅ Implemented
- **Debug Points**:
  - NLP processing errors
  - Concept classification issues
  - Embedding generation failures

### 4.3 Advanced Semantic Analyzer (`src/semantic/advanced-semantic-analyzer.ts`)
- **Features**:
  - InfraNodus-style analysis
  - Concept bridges
  - Structural weak spots
  - Network analysis
- **Status**: ✅ Implemented
- **Debug Points**:
  - Graph algorithm performance
  - Cluster detection accuracy
  - Bridge identification errors

### 4.4 Impact Analyzer (`src/analysis/impact-analyzer.ts`)
- **Role**: Change impact assessment
- **Status**: ✅ Implemented
- **Debug Points**:
  - Dependency tracking errors
  - Impact prediction accuracy
  - Real-time monitoring issues

### 4.5 Autonomous Debugger (`src/debugging/autonomous-debugger.ts`)
- **Role**: AI-powered debugging assistance
- **Status**: ✅ Implemented
- **Debug Points**:
  - Error pattern recognition
  - Solution suggestion quality
  - Context gathering failures

---

## Layer 5: Knowledge Graph
**Purpose**: Structured knowledge storage and querying

### 5.1 Graph Database Interface (`src/graph/graph-database.ts`)
- **Role**: Abstract database interface
- **Operations**: CRUD, queries, traversals
- **Status**: ✅ Implemented

### 5.2 Neo4j Implementation (`src/graph/neo4j-database.ts`)
- **Features**:
  - Connection management
  - Query execution
  - Transaction handling
  - Retry logic
- **Status**: ✅ Implemented
- **Debug Points**:
  - Connection failures
  - Query performance
  - Transaction deadlocks
  - Memory usage

### 5.3 Mock Database (`src/graph/mock-graph-database.ts`)
- **Role**: In-memory testing/fallback
- **Status**: ✅ Implemented
- **Debug Points**:
  - Memory limitations
  - Data persistence issues

### 5.4 Database Factory (`src/graph/database-factory.ts`)
- **Role**: Database instance creation
- **Status**: ✅ Implemented
- **Debug Points**:
  - Configuration parsing
  - Instance creation failures

### 5.5 Knowledge Graph Engine (`src/graph/knowledge-graph-engine.ts`)
- **Features**:
  - Concept extraction
  - Relationship inference
  - User feedback integration
- **Status**: ✅ Implemented
- **Debug Points**:
  - LLM concept extraction
  - Relationship accuracy
  - Feedback processing

### 5.6 Dependency Graph Builder (`src/graph/dependency-graph-builder.ts`)
- **Role**: Code relationship extraction
- **Status**: ✅ Implemented
- **Debug Points**:
  - Symbol resolution errors
  - Circular dependency handling
  - Performance with large codebases

---

## Layer 6: Data Parsing & Analysis
**Purpose**: Code understanding and extraction

### 6.1 Tree-sitter Parser (`src/parsers/tree-sitter-parser.ts`)
- **Languages**: Multi-language support
- **Status**: ✅ Implemented
- **Debug Points**:
  - Grammar loading failures
  - Parse tree generation errors
  - Language detection issues

### 6.2 VSCode Language Server Parser (`src/parsers/vscode-language-server-parser.ts`)
- **Role**: LSP-based parsing
- **Status**: ✅ Implemented
- **Debug Points**:
  - LSP communication failures
  - Symbol resolution errors
  - Workspace analysis timeouts

### 6.3 Language-Specific Parsers
- **Python AST Parser** (`src/parsers/python-ast-parser.ts`)
- **Java AST Parser** (`src/parsers/java-ast-parser.ts`)
- **Status**: ✅ Implemented
- **Debug Points**:
  - Language-specific parsing errors
  - AST traversal issues
  - Metadata extraction failures

---

## Debug Workflows

### 🔍 Common Debug Scenarios

#### 1. Extension Won't Activate
```
Check: src/extension.ts → activate()
→ LLM client initialization
→ Configuration validation
→ Component registration
```

#### 2. Query Processing Fails
```
Check: src/agents/master-agent.ts → processQuery()
→ Task plan creation
→ Sub-agent execution
→ Result aggregation
```

#### 3. Graph Database Issues
```
Check: src/graph/neo4j-database.ts → connect()
→ Connection parameters
→ Authentication
→ Network connectivity
```

#### 4. Semantic Analysis Problems
```
Check: src/semantic/semantic-analyzer.ts
→ NLP processing pipeline
→ Concept extraction
→ Embedding generation
```

#### 5. LLM Communication Errors
```
Check: src/core/llm-client.ts
→ API key validation
→ Provider selection
→ Request formatting
→ Response parsing
```

---

## Component Dependencies

### 🔗 Dependency Graph

```
VSCode Extension (Layer 1)
    ↓
Master Agent (Layer 2.1)
    ↓
Cognitive Agent (Layer 2.2) ← → Sub-Agent Registry (Layer 2.3)
    ↓
LLM Orchestrator (Layer 3.2) ← → LLM Client (Layer 3.1)
    ↓
Retrieval Engine (Layer 4.1) ← → Semantic Analyzer (Layer 4.2)
    ↓
Knowledge Graph Engine (Layer 5.5) ← → Graph Database (Layer 5.1)
    ↓
Parsers (Layer 6.1-6.3)
```

### 🎯 Critical Path Analysis

#### High Priority Components (System Failure if Broken)
1. **Extension Core** (`src/extension.ts`)
2. **LLM Client** (`src/core/llm-client.ts`)
3. **Cognitive Agent** (`src/agents/cognitive-agent.ts`)
4. **Graph Database** (`src/graph/graph-database.ts`)

#### Medium Priority Components (Feature Degradation)
1. **Semantic Analyzer** (`src/semantic/semantic-analyzer.ts`)
2. **Retrieval Engine** (`src/retrieval/retrieval-engine.ts`)
3. **Parsers** (`src/parsers/`)

#### Low Priority Components (Enhanced Features)
1. **Graph Visualizer** (`src/ui/graph-visualizer.ts`)
2. **Advanced Semantic Analyzer** (`src/semantic/advanced-semantic-analyzer.ts`)
3. **Impact Analyzer** (`src/analysis/impact-analyzer.ts`)

---

## 🛠️ Debug Tools & Commands

### VSCode Debug Commands
```bash
# Test LLM Connection
Ctrl+Shift+P → "Cognitive Code Weaver: Test LLM Connection"

# Show Extension Status
Ctrl+Shift+P → "Cognitive Code Weaver: Show Status"

# Analyze Workspace
Ctrl+Shift+P → "Cognitive Code Weaver: Analyze Workspace"

# Open Settings
Ctrl+Shift+P → "Cognitive Code Weaver: Open Settings"
```

### Log Locations
```
Extension Logs: VSCode Output Panel → "Cognitive Code Weaver"
Neo4j Logs: Check Neo4j server logs
System Logs: Check VSCode Developer Tools Console
```

### Configuration Validation
```typescript
// Check in src/core/config.ts
const config = ConfigManager.getInstance().getConfig();
console.log('LLM Config:', config.llm);
console.log('Database Config:', config.databases);
```

---

## 🚨 Common Error Patterns

### 1. "LLM Provider Not Configured"
**Location**: `src/core/llm-client.ts`
**Cause**: Missing or invalid API key
**Solution**: Check VSCode settings for `cognitiveCodeWeaver.llm.apiKey`

### 2. "Graph Database Connection Failed"
**Location**: `src/graph/neo4j-database.ts`
**Cause**: Neo4j not running or wrong credentials
**Solution**: Verify Neo4j service and connection string

### 3. "Workspace Analysis Timeout"
**Location**: `src/agents/cognitive-agent.ts`
**Cause**: Large codebase or parser issues
**Solution**: Check exclude patterns and file size limits

### 4. "Sub-Agent Execution Failed"
**Location**: `src/agents/sub-agent-registry.ts`
**Cause**: Agent initialization or execution error
**Solution**: Check individual sub-agent logs

### 5. "Semantic Analysis Memory Error"
**Location**: `src/semantic/semantic-analyzer.ts`
**Cause**: Large text corpus processing
**Solution**: Increase memory limits or batch processing

---

## 🔧 Performance Monitoring

### Key Metrics to Track
1. **Extension Activation Time** (< 2 seconds)
2. **Workspace Analysis Duration** (< 5 minutes for 100k LOC)
3. **Query Response Time** (< 10 seconds)
4. **Memory Usage** (< 500MB for medium projects)
5. **Graph Database Query Time** (< 1 second for simple queries)

### Performance Debug Points
```typescript
// Add timing measurements
const startTime = Date.now();
// ... operation
const duration = Date.now() - startTime;
logger.info('Operation completed', { duration });
```

---

## 🧪 Testing Strategy

### Unit Tests
- Individual component testing
- Mock dependencies
- Error condition testing

### Integration Tests
- End-to-end workflows
- Database integration
- LLM provider testing

### Performance Tests
- Large codebase analysis
- Memory usage monitoring
- Concurrent operation testing

---

## 📊 Monitoring Dashboard

### Health Check Indicators
- ✅ Extension Active
- ✅ LLM Provider Connected
- ✅ Graph Database Online
- ✅ Parsers Initialized
- ✅ Semantic Analysis Ready

### Performance Indicators
- Query Response Time: < 10s
- Memory Usage: < 500MB
- Database Connections: < 10
- Active Agents: Status
- Cache Hit Rate: > 80%

---

## 📊 Visual Flow Diagrams

### 🔄 Function Flow Graph - Query Processing

```mermaid
graph TD
    A[User Input in VSCode] --> B[Extension Command Handler]
    B --> C{Command Type?}

    C -->|Ask Question| D[askQuestion Command]
    C -->|Explain Code| E[explainCode Command]
    C -->|Analyze Workspace| F[analyzeWorkspace Command]

    D --> G[MasterAgent.processQuery]
    E --> G
    F --> H[CognitiveAgent.analyzeWorkspace]

    G --> I[PlannerAgent.createTaskPlan]
    I --> J[SubAgentRegistry.executeSubAgent]
    J --> K{Agent Type?}

    K -->|CODE_READER| L[CodeReaderAgent.execute]
    K -->|REASONER| M[ReasonerAgent.execute]
    K -->|BUG_DETECTOR| N[BugDetectorAgent.execute]

    L --> O[CognitiveAgent.processQuery]
    M --> O
    N --> O

    O --> P[RetrievalEngine.retrieve]
    P --> Q[SemanticRetrieval.retrieve]
    P --> R[StructuralRetrieval.retrieve]
    P --> S[ContextualRetrieval.retrieve]

    Q --> T[VectorDB Query]
    R --> U[GraphDB Traversal]
    S --> V[UserContext Analysis]

    T --> W[Context Assembly]
    U --> W
    V --> W

    W --> X[LLMClient.generateResponse]
    X --> Y{Provider?}

    Y -->|OpenAI| Z[OpenAIProvider.generateResponse]
    Y -->|Anthropic| AA[AnthropicProvider.generateResponse]
    Y -->|Mistral| BB[MistralProvider.generateResponse]

    Z --> CC[Response Processing]
    AA --> CC
    BB --> CC

    CC --> DD[AgentResponse Creation]
    DD --> EE[VSCode UI Update]

    H --> FF[Multi-Language Parsing]
    FF --> GG[TreeSitterParser.parseFile]
    FF --> HH[VSCodeLSP.parseFile]

    GG --> II[AST Generation]
    HH --> II

    II --> JJ[DependencyGraphBuilder.buildGraph]
    JJ --> KK[SemanticAnalyzer.analyzeSemantics]

    KK --> LL[GraphDatabase.storeNodes]
    LL --> MM[GraphDatabase.storeRelationships]

    MM --> NN[Analysis Complete]
    NN --> EE
```

### 🌊 Data Flow Graph - Workspace Analysis

```mermaid
graph LR
    subgraph "Input Layer"
        A[Source Files]
        B[Configuration]
        C[User Context]
    end

    subgraph "Parsing Layer"
        D[TreeSitter Parser]
        E[VSCode LSP Parser]
        F[Python AST Parser]
        G[Java AST Parser]
    end

    subgraph "Analysis Layer"
        H[Code Elements]
        I[Dependency Graph Builder]
        J[Semantic Analyzer]
        K[Advanced Semantic Analyzer]
    end

    subgraph "Knowledge Layer"
        L[Structural Graph]
        M[Semantic Concepts]
        N[Topic Models]
        O[Embeddings]
    end

    subgraph "Storage Layer"
        P[Neo4j Database]
        Q[Vector Store]
        R[Agent Memory]
    end

    subgraph "Intelligence Layer"
        S[Knowledge Graph Engine]
        T[Retrieval Engine]
        U[LLM Orchestrator]
    end

    subgraph "Output Layer"
        V[Query Responses]
        W[Code Suggestions]
        X[Graph Visualizations]
        Y[Debug Insights]
    end

    A --> D
    A --> E
    A --> F
    A --> G

    D --> H
    E --> H
    F --> H
    G --> H

    H --> I
    H --> J
    H --> K

    I --> L
    J --> M
    J --> N
    J --> O
    K --> M

    L --> P
    M --> P
    N --> P
    O --> Q

    P --> S
    Q --> T
    R --> T

    S --> U
    T --> U

    U --> V
    U --> W
    U --> X
    U --> Y

    B --> D
    B --> E
    C --> T
```

---

## 🔄 Component Interaction Flows

### 1. Query Processing Flow
```
User Input (VSCode)
    ↓
Extension Commands (Layer 1)
    ↓
Master Agent.processQuery() (Layer 2.1)
    ↓
Task Plan Creation (Planner Sub-Agent)
    ↓
Cognitive Agent.processQuery() (Layer 2.2)
    ↓
Retrieval Engine.retrieve() (Layer 4.1)
    ↓ (parallel)
├── Semantic Retrieval (embeddings)
├── Structural Retrieval (graph traversal)
└── Contextual Retrieval (user patterns)
    ↓
Context Assembly & LLM Request (Layer 3)
    ↓
Response Generation & Display
```

### 2. Workspace Analysis Flow
```
Workspace Open/Change Event
    ↓
Cognitive Agent.analyzeWorkspace()
    ↓ (parallel)
├── Tree-sitter Parser (Layer 6.1)
├── VSCode LSP Parser (Layer 6.2)
└── Language-specific Parsers (Layer 6.3)
    ↓
Code Element Extraction
    ↓
Dependency Graph Builder (Layer 5.6)
    ↓ (parallel)
├── Call Graph Generation
├── Inheritance Graph
├── Import Dependencies
└── Symbol Resolution
    ↓
Semantic Analyzer (Layer 4.2)
    ↓ (parallel)
├── Concept Extraction
├── Topic Modeling
├── Pattern Detection
└── Embedding Generation
    ↓
Graph Database Storage (Layer 5)
    ↓
Knowledge Graph Engine Updates
```

### 🔄 Sequence Diagram - User Query Processing

```mermaid
sequenceDiagram
    participant U as User
    participant VSC as VSCode Extension
    participant MA as Master Agent
    participant CA as Cognitive Agent
    participant RE as Retrieval Engine
    participant LLM as LLM Client
    participant GDB as Graph Database
    participant VDB as Vector Database

    U->>VSC: Ask Question Command
    VSC->>MA: processQuery(query, context)

    MA->>MA: createTaskPlan(query)
    Note over MA: Analyze query intent<br/>Select sub-agents

    MA->>CA: processQuery(parsedQuery, context)

    CA->>RE: retrieve(query, context)

    par Parallel Retrieval
        RE->>GDB: findRelatedNodes(entities)
        GDB-->>RE: structural_results
    and
        RE->>VDB: semanticSearch(embedding)
        VDB-->>RE: semantic_results
    and
        RE->>RE: contextualRetrieval(userHistory)
    end

    RE->>RE: rankAndMergeResults()
    RE-->>CA: retrievalResult

    CA->>CA: buildLLMContext(retrievalResult)
    CA->>LLM: generateResponse(messages)

    LLM->>LLM: selectProvider()
    LLM->>LLM: formatRequest()

    alt OpenAI Provider
        LLM->>LLM: callOpenAI(request)
    else Anthropic Provider
        LLM->>LLM: callAnthropic(request)
    else Mistral Provider
        LLM->>LLM: callMistral(request)
    end

    LLM-->>CA: llmResponse
    CA->>CA: extractSuggestions(response)
    CA->>CA: calculateConfidence(result)
    CA-->>MA: agentResponse

    MA->>MA: generateFinalResponse()
    MA-->>VSC: finalResponse
    VSC->>VSC: displayResponse()
    VSC-->>U: Show Result
```

### 🔄 Sequence Diagram - Workspace Analysis

```mermaid
sequenceDiagram
    participant U as User
    participant VSC as VSCode Extension
    participant CA as Cognitive Agent
    participant TSP as TreeSitter Parser
    participant LSP as VSCode LSP Parser
    participant DGB as Dependency Graph Builder
    participant SA as Semantic Analyzer
    participant GDB as Graph Database
    participant KGE as Knowledge Graph Engine

    U->>VSC: Open Workspace / File Change
    VSC->>CA: analyzeWorkspace(workspacePath)

    CA->>CA: findSourceFiles(workspacePath)

    loop For each source file
        par Parallel Parsing
            CA->>TSP: parseFile(filePath)
            TSP-->>CA: astElements
        and
            CA->>LSP: parseFile(filePath)
            LSP-->>CA: lspElements
        end

        CA->>CA: mergeElements(astElements, lspElements)
    end

    CA->>DGB: buildDependencyGraph(allElements)

    par Dependency Analysis
        DGB->>DGB: buildCallGraph()
    and
        DGB->>DGB: buildInheritanceGraph()
    and
        DGB->>DGB: buildImportGraph()
    and
        DGB->>DGB: resolveSymbols()
    end

    DGB-->>CA: dependencyResult

    CA->>SA: analyzeCodebaseSemantics(elements)

    par Semantic Analysis
        SA->>SA: extractConcepts(textCorpus)
    and
        SA->>SA: performTopicModeling(textCorpus)
    and
        SA->>SA: generateEmbeddings(elements)
    and
        SA->>SA: findSemanticRelationships(elements)
    end

    SA-->>CA: semanticResults

    CA->>GDB: storeNodes(elements)
    CA->>GDB: storeRelationships(relationships)

    CA->>KGE: updateKnowledgeGraph(concepts, relationships)
    KGE->>KGE: extractConcepts(elements)
    KGE->>KGE: findConceptRelationships()
    KGE-->>CA: knowledgeGraphUpdated

    CA-->>VSC: analysisComplete
    VSC-->>U: Show Analysis Results
```

### 🔄 Sequence Diagram - Error Handling & Recovery

```mermaid
sequenceDiagram
    participant C as Component
    participant EH as Error Handler
    participant L as Logger
    participant RM as Retry Manager
    participant FB as Fallback Manager
    participant U as User Interface

    C->>EH: throwError(error)
    EH->>L: logError(error, context)
    EH->>EH: classifyError(error)

    alt Transient Error (Network, Timeout)
        EH->>RM: scheduleRetry(operation, attempt)
        RM->>RM: exponentialBackoff(attempt)
        RM->>C: retryOperation()

        alt Retry Success
            C-->>EH: operationSuccess
            EH->>L: logRecovery("retry_success")
        else Retry Failed
            C-->>EH: operationFailed
            EH->>FB: activateFallback(operation)
        end

    else Configuration Error
        EH->>U: showConfigurationError(error)
        U->>U: promptUserForFix()
        U-->>EH: configurationUpdated
        EH->>C: retryWithNewConfig()

    else Resource Error (Memory, Disk)
        EH->>FB: activateFallback(operation)
        FB->>FB: selectFallbackStrategy()

        alt Use Mock Database
            FB->>FB: switchToMockDB()
        else Reduce Processing
            FB->>FB: enableLightweightMode()
        else Cache Cleanup
            FB->>FB: clearCaches()
        end

        FB-->>EH: fallbackActivated
        EH->>C: continueWithFallback()

    else Critical Error
        EH->>EH: gracefulDegradation()
        EH->>U: showCriticalError(error)
        EH->>EH: disableAffectedFeatures()
        EH->>L: logCriticalError(error, stackTrace)
    end

    EH->>U: updateStatus(recoveryStatus)
```

### 🔄 Component State Flow - LLM Provider Selection

```mermaid
stateDiagram-v2
    [*] --> Initializing

    Initializing --> ConfigValidation
    ConfigValidation --> ProviderSelection : Config Valid
    ConfigValidation --> ConfigError : Config Invalid

    ProviderSelection --> OpenAIReady : OpenAI Selected
    ProviderSelection --> AnthropicReady : Anthropic Selected
    ProviderSelection --> MistralReady : Mistral Selected
    ProviderSelection --> LocalLLMReady : Local LLM Selected

    OpenAIReady --> Processing : Request Received
    AnthropicReady --> Processing : Request Received
    MistralReady --> Processing : Request Received
    LocalLLMReady --> Processing : Request Received

    Processing --> Success : Response Generated
    Processing --> RateLimited : Rate Limit Hit
    Processing --> NetworkError : Network Failure
    Processing --> AuthError : Authentication Failed

    RateLimited --> Waiting : Start Backoff
    Waiting --> Processing : Retry Request

    NetworkError --> FallbackProvider : Switch Provider
    AuthError --> ConfigError : Invalid Credentials

    FallbackProvider --> OpenAIReady : Fallback to OpenAI
    FallbackProvider --> AnthropicReady : Fallback to Anthropic
    FallbackProvider --> LocalLLMReady : Fallback to Local
    FallbackProvider --> AllProvidersFailed : No Fallback Available

    Success --> [*]
    ConfigError --> [*]
    AllProvidersFailed --> [*]
```

### 🔄 Data Flow - Semantic Analysis Pipeline

```mermaid
flowchart TD
    A[Raw Code Text] --> B[Text Preprocessing]
    B --> C[Tokenization]
    C --> D[Stop Word Removal]
    D --> E[Stemming/Lemmatization]

    E --> F{Analysis Type}

    F -->|Concept Extraction| G[Named Entity Recognition]
    F -->|Topic Modeling| H[LDA/BERTopic]
    F -->|Embedding Generation| I[Sentence Transformers]
    F -->|Pattern Detection| J[Regex/ML Patterns]

    G --> K[Concept Classification]
    K --> L[Design Patterns]
    K --> M[Architectural Patterns]
    K --> N[Domain Concepts]

    H --> O[Topic Coherence Scoring]
    O --> P[Topic Assignment]
    P --> Q[Document-Topic Matrix]

    I --> R[Vector Normalization]
    R --> S[Similarity Calculation]
    S --> T[Clustering]

    J --> U[Pattern Confidence Scoring]
    U --> V[Pattern Validation]
    V --> W[Pattern Metadata]

    L --> X[Semantic Graph Nodes]
    M --> X
    N --> X
    Q --> Y[Topic Relationships]
    T --> Z[Similarity Relationships]
    W --> AA[Pattern Relationships]

    X --> BB[Knowledge Graph Update]
    Y --> BB
    Z --> BB
    AA --> BB

    BB --> CC[Graph Database Storage]
    CC --> DD[Indexing & Optimization]
    DD --> EE[Query Ready]
```

---

## 🎯 Detailed Debug Procedures

### Procedure 1: Extension Activation Debug
```bash
1. Check VSCode Output Panel
   - View → Output → Select "Cognitive Code Weaver"

2. Verify Extension Installation
   - Extensions View → Search "Cognitive Code Weaver"
   - Check if enabled and version matches

3. Check Configuration
   - Settings → Search "cognitiveCodeWeaver"
   - Verify LLM provider and API key

4. Test Basic Functionality
   - Ctrl+Shift+P → "Cognitive Code Weaver: Test LLM Connection"

5. Check Developer Console
   - Help → Toggle Developer Tools
   - Look for JavaScript errors
```

### Procedure 2: Graph Database Debug
```bash
1. Verify Neo4j Service
   - Check if Neo4j is running: systemctl status neo4j
   - Test connection: cypher-shell -u neo4j -p password

2. Check Connection Configuration
   - VSCode Settings → cognitiveCodeWeaver.database.connectionString
   - Default: bolt://localhost:7687

3. Test Database Operations
   - Extension Command: "Show Status"
   - Check for database connection errors

4. Verify Database Schema
   - Open Neo4j Browser: http://localhost:7474
   - Run: CALL db.schema.visualization()

5. Check Query Performance
   - Monitor slow queries in Neo4j logs
   - Use EXPLAIN/PROFILE for query analysis
```

### Procedure 3: LLM Integration Debug
```bash
1. Validate API Configuration
   - Check API key format and validity
   - Test with curl/postman outside VSCode

2. Check Provider Selection
   - Settings → cognitiveCodeWeaver.llm.provider
   - Supported: openai, anthropic, mistral

3. Monitor Request/Response
   - Enable debug logging in settings
   - Check Output Panel for LLM requests

4. Test Fallback Mechanisms
   - Disable primary provider
   - Verify fallback behavior

5. Check Rate Limiting
   - Monitor API usage quotas
   - Implement request throttling if needed
```

### Procedure 4: Semantic Analysis Debug
```bash
1. Check Text Processing Pipeline
   - Verify NLP library initialization
   - Test tokenization and preprocessing

2. Monitor Concept Extraction
   - Check extracted concepts quality
   - Verify confidence scores

3. Validate Embedding Generation
   - Test embedding model availability
   - Check vector dimensions consistency

4. Debug Topic Modeling
   - Verify topic coherence
   - Check document-topic assignments

5. Performance Analysis
   - Monitor memory usage during analysis
   - Check processing time for large files
```

---

## 🔍 Advanced Debugging Techniques

### 1. Component Isolation Testing
```typescript
// Test individual components in isolation
const semanticAnalyzer = new SemanticAnalyzer();
const testElement = { /* mock code element */ };
const concepts = await semanticAnalyzer.extractConcepts(testElement);
console.log('Extracted concepts:', concepts);
```

### 2. Mock Data Testing
```typescript
// Use mock data to test workflows
const mockGraphDb = new MockGraphDatabase();
const cognitiveAgent = new CognitiveAgent();
// Test with controlled data
```

### 3. Performance Profiling
```typescript
// Add performance markers
console.time('workspace-analysis');
await cognitiveAgent.analyzeWorkspace(workspacePath);
console.timeEnd('workspace-analysis');
```

### 4. Memory Leak Detection
```typescript
// Monitor memory usage
const memUsage = process.memoryUsage();
console.log('Memory usage:', {
  rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
  heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB'
});
```

### 5. Graph Query Debugging
```cypher
// Neo4j debugging queries
CALL db.stats.retrieve('GRAPH COUNTS');
MATCH (n) RETURN labels(n), count(n);
MATCH ()-[r]->() RETURN type(r), count(r);
```

---

## 📋 Debug Checklist

### Pre-Debug Setup
- [ ] Enable debug logging in settings
- [ ] Clear previous logs
- [ ] Note system specifications
- [ ] Record reproduction steps
- [ ] Check recent changes

### During Debug Session
- [ ] Monitor CPU/Memory usage
- [ ] Check network connectivity
- [ ] Verify file permissions
- [ ] Test with minimal configuration
- [ ] Isolate problematic components

### Post-Debug Analysis
- [ ] Document findings
- [ ] Create reproduction test case
- [ ] Update error handling
- [ ] Improve logging
- [ ] Update documentation

---

## 🚀 Quick Fix Commands

### Reset Extension State
```bash
# Reload VSCode window
Ctrl+Shift+P → "Developer: Reload Window"

# Clear extension cache
rm -rf ~/.vscode/extensions/cognitive-code-weaver-*/cache
```

### Database Reset
```cypher
// Clear Neo4j database
MATCH (n) DETACH DELETE n;
```

### Configuration Reset
```json
// Reset to default settings
{
  "cognitiveCodeWeaver.llm.provider": "openai",
  "cognitiveCodeWeaver.database.type": "memory",
  "cognitiveCodeWeaver.debug.enableLogging": true
}
```

---

## 🎯 Additional Flow Diagrams

### 🔄 Agent Collaboration Flow

```mermaid
graph TB
    subgraph "User Request Processing"
        A[User Query] --> B[Master Agent]
        B --> C[Task Planning]
        C --> D[Agent Selection]
    end

    subgraph "Agent Execution Layer"
        D --> E[Planner Agent]
        D --> F[Code Reader Agent]
        D --> G[Reasoner Agent]
        D --> H[Bug Detector Agent]
        D --> I[Refactorer Agent]
        D --> J[Tester Agent]
        D --> K[Graph Mind Agent]
    end

    subgraph "Shared Resources"
        L[Agent Memory]
        M[Knowledge Graph]
        N[Code Context]
        O[User History]
    end

    subgraph "Result Synthesis"
        P[Result Aggregation]
        Q[Confidence Scoring]
        R[Response Generation]
        S[User Presentation]
    end

    E --> L
    F --> M
    G --> N
    H --> M
    I --> N
    J --> O
    K --> M

    L --> P
    M --> P
    N --> P
    O --> P

    P --> Q
    Q --> R
    R --> S
```

### 🔄 Real-time Monitoring Flow

```mermaid
flowchart LR
    subgraph "Event Sources"
        A[File Changes]
        B[User Actions]
        C[System Events]
        D[Error Events]
    end

    subgraph "Event Processing"
        E[Event Collector]
        F[Event Filter]
        G[Event Classifier]
        H[Priority Queue]
    end

    subgraph "Analysis Pipeline"
        I[Incremental Parser]
        J[Diff Analyzer]
        K[Impact Calculator]
        L[Semantic Updater]
    end

    subgraph "Knowledge Update"
        M[Graph Updater]
        N[Vector Store Sync]
        O[Cache Invalidation]
        P[Index Refresh]
    end

    subgraph "User Feedback"
        Q[Status Updates]
        R[Progress Indicators]
        S[Error Notifications]
        T[Suggestions]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    F --> G
    G --> H

    H --> I
    H --> J
    H --> K
    H --> L

    I --> M
    J --> M
    K --> N
    L --> N

    M --> O
    N --> O
    O --> P

    P --> Q
    P --> R
    D --> S
    L --> T
```

---

## 📊 Debug Flow Summary

The visual diagrams above provide comprehensive views of:

### **1. Function Flow Graphs**
- **Query Processing**: Complete path from user input to response
- **Agent Collaboration**: How multiple agents work together
- **Component Interactions**: Detailed method call sequences

### **2. Data Flow Graphs**
- **Workspace Analysis**: Data transformation through parsing layers
- **Semantic Pipeline**: Text processing to knowledge extraction
- **Real-time Monitoring**: Event-driven updates and notifications

### **3. Sequence Diagrams**
- **User Query Processing**: Step-by-step interaction timeline
- **Workspace Analysis**: Parallel processing workflows
- **Error Handling**: Recovery and fallback mechanisms

### **4. State Diagrams**
- **LLM Provider Management**: State transitions and error handling
- **Component Lifecycle**: Initialization, operation, and cleanup states

### **🎯 Key Debug Insights from Flows**

1. **Bottleneck Identification**:
   - LLM API calls (network latency)
   - Graph database queries (complex traversals)
   - Semantic analysis (CPU-intensive NLP)

2. **Failure Points**:
   - Provider authentication failures
   - Database connection issues
   - Memory exhaustion during large file analysis

3. **Recovery Mechanisms**:
   - Automatic provider fallback
   - Graceful degradation to mock database
   - Incremental processing for large codebases

4. **Performance Optimization**:
   - Parallel processing in parsing layer
   - Caching at multiple levels
   - Lazy loading of expensive operations

These diagrams serve as both **architectural documentation** and **debugging roadmaps**, helping developers quickly identify where issues might occur and how data flows through the system.
