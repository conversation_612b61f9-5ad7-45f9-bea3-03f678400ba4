# 🧠 Cognitive Code Weaver - Python Edition

A highly modular, AI-powered code analysis and reasoning system built in Python with a plugin-based architecture.

## 🎯 Overview

This is a complete Python reimplementation of the Cognitive Code Weaver system, designed for maximum modularity and extensibility. The system uses a multi-agent architecture where intelligent agents collaborate to understand, analyze, and reason about codebases.

## 🏗️ Architecture

### Core Principles
- **Modular Design**: Each component is independently replaceable
- **Plugin Architecture**: Agents can be added/removed effortlessly
- **Provider Agnostic**: Support for multiple LLM providers
- **Language Agnostic**: Multi-language code analysis
- **CLI First**: Rich command-line interface with future UI support

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    CLI Interface                            │
├─────────────────────────────────────────────────────────────┤
│                  Agent Orchestrator                         │
├─────────────────────────────────────────────────────────────┤
│  Master Agent │ Cognitive Agent │ Sub-Agent Registry        │
├─────────────────────────────────────────────────────────────┤
│  Planner │ CodeReader │ Reasoner │ BugDetector │ [Plugins]  │
├─────────────────────────────────────────────────────────────┤
│           LLM Integration │ Knowledge Graph                 │
├─────────────────────────────────────────────────────────────┤
│        Code Analysis │ Semantic Processing                  │
├─────────────────────────────────────────────────────────────┤
│              Storage Layer │ Configuration                  │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### Installation
```bash
# Clone the repository
git clone <repo-url>
cd cognitive-code-weaver-py

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### Basic Usage
```bash
# Initialize configuration
ccw init

# Analyze a codebase
ccw analyze /path/to/project

# Ask questions about code
ccw ask "How does authentication work in this project?"

# Interactive mode
ccw interactive

# Show system status
ccw status
```

## 📁 Project Structure

```
cognitive-code-weaver-py/
├── ccw/                          # Main package
│   ├── __init__.py
│   ├── core/                     # Core framework
│   │   ├── agent.py             # Base agent class
│   │   ├── registry.py          # Agent registry
│   │   ├── message_bus.py       # Inter-agent communication
│   │   └── config.py            # Configuration system
│   ├── agents/                   # Agent implementations
│   │   ├── master_agent.py      # Master orchestrator
│   │   ├── cognitive_agent.py   # Cognitive processing
│   │   └── sub_agents/          # Specialized agents
│   │       ├── planner.py
│   │       ├── code_reader.py
│   │       ├── reasoner.py
│   │       └── bug_detector.py
│   ├── llm/                      # LLM integration
│   │   ├── client.py            # Main LLM client
│   │   └── providers/           # Provider implementations
│   │       ├── openai.py
│   │       ├── anthropic.py
│   │       └── local.py
│   ├── analysis/                 # Code analysis
│   │   ├── parser.py            # Multi-language parsing
│   │   ├── ast_analyzer.py      # AST analysis
│   │   └── dependency_graph.py  # Dependency analysis
│   ├── knowledge/                # Knowledge management
│   │   ├── graph.py             # Knowledge graph
│   │   ├── semantic.py          # Semantic analysis
│   │   └── storage.py           # Data storage
│   ├── cli/                      # Command-line interface
│   │   ├── main.py              # Main CLI entry
│   │   ├── commands/            # CLI commands
│   │   └── interactive.py       # Interactive mode
│   └── api/                      # Future API endpoints
│       ├── server.py            # Web server
│       └── endpoints/           # API endpoints
├── tests/                        # Test suite
├── config/                       # Configuration files
├── docs/                         # Documentation
├── requirements.txt              # Dependencies
├── setup.py                      # Package setup
└── README.md                     # This file
```

## 🔧 Configuration

### Environment Variables
```bash
export CCW_LLM_PROVIDER=openai
export CCW_API_KEY=your_api_key
export CCW_MODEL=gpt-4
export CCW_DATABASE_URL=neo4j://localhost:7687
```

### Configuration File (config/ccw.yaml)
```yaml
llm:
  provider: openai
  model: gpt-4
  api_key: ${CCW_API_KEY}
  
database:
  type: neo4j
  url: ${CCW_DATABASE_URL}
  
analysis:
  max_file_size: 1048576
  exclude_patterns:
    - "*/node_modules/*"
    - "*/dist/*"
    - "*/.git/*"
  
agents:
  enabled:
    - planner
    - code_reader
    - reasoner
    - bug_detector
  plugins_dir: ./plugins
```

## 🤖 Agent System

### Adding Custom Agents
```python
from ccw.core.agent import Agent
from ccw.core.registry import agent_registry

@agent_registry.register("custom_agent")
class CustomAgent(Agent):
    def __init__(self, config):
        super().__init__("custom_agent", config)
    
    async def execute(self, task, context):
        # Your agent logic here
        return result
```

### Plugin Architecture
```python
# plugins/my_plugin.py
from ccw.core.plugin import Plugin

class MyPlugin(Plugin):
    def register_agents(self, registry):
        registry.register("my_agent", MyAgent)
    
    def register_commands(self, cli):
        cli.add_command("my_command", my_command_handler)
```

## 🎮 CLI Commands

### Analysis Commands
```bash
ccw analyze <path>              # Analyze codebase
ccw analyze --deep <path>       # Deep analysis with ML
ccw analyze --format json       # Output as JSON
```

### Query Commands
```bash
ccw ask "question"              # Ask about codebase
ccw explain <file:line>         # Explain specific code
ccw find similar <code>         # Find similar code patterns
```

### Management Commands
```bash
ccw init                        # Initialize configuration
ccw status                      # Show system status
ccw agents list                 # List available agents
ccw agents enable <agent>       # Enable specific agent
ccw plugins install <plugin>    # Install plugin
```

### Interactive Mode
```bash
ccw interactive                 # Start interactive session
> analyze ./src
> ask "How does the auth system work?"
> explain src/auth.py:45
> exit
```

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/agents/

# Run with coverage
pytest --cov=ccw tests/
```

## 🔌 Extensibility

### Adding New LLM Providers
```python
from ccw.llm.providers.base import LLMProvider

class CustomProvider(LLMProvider):
    def __init__(self, config):
        super().__init__(config)
    
    async def generate_response(self, messages):
        # Implementation
        pass
```

### Adding New Analysis Types
```python
from ccw.analysis.base import Analyzer

class CustomAnalyzer(Analyzer):
    def analyze(self, code):
        # Custom analysis logic
        return analysis_result
```

## 🌐 Future UI Integration

The system is designed to easily integrate with web UIs:

```python
# API endpoints for UI
from ccw.api.server import app

@app.route('/api/analyze', methods=['POST'])
def analyze_endpoint():
    # Analysis logic
    return jsonify(result)
```

## 📊 Monitoring & Logging

```python
import logging
from ccw.core.monitoring import monitor

# Built-in monitoring
@monitor.track_performance
async def my_function():
    pass

# Structured logging
logger = logging.getLogger('ccw.agents.reasoner')
logger.info("Processing reasoning task", extra={
    "task_id": task.id,
    "complexity": task.complexity
})
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Ready to build intelligent code analysis with maximum modularity!** 🚀
