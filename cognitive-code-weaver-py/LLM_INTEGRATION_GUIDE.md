# 🤖 LLM Integration Guide - Cognitive Code Weaver

## 🎯 **Complete LLM Integration System**

The Cognitive Code Weaver now includes a **comprehensive, provider-agnostic LLM integration system** that supports multiple providers with automatic fallback, caching, and advanced features.

## 🏗️ **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    LLM Client                              │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │   OpenAI        │   Anthropic     │     Local       │   │
│  │   Provider      │   Provider      │   Provider      │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│  Caching │ Fallback │ Load Balancing │ Usage Tracking    │
├─────────────────────────────────────────────────────────────┤
│           Agent Integration Layer                           │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **Key Features**

### ✅ **Multi-Provider Support**
- **OpenAI**: GPT-4, GPT-3.5, function calling, embeddings
- **Anthropic**: Claude 3 (Opus, Sonnet, Haiku), large context windows
- **Local Models**: Ollama, llama.cpp, custom endpoints

### ✅ **Advanced Features**
- **Automatic Fallback** - Seamlessly switch providers on failure
- **Response Caching** - Cache responses for improved performance
- **Streaming Support** - Real-time response streaming
- **Usage Tracking** - Monitor tokens, costs, and performance
- **Error Handling** - Robust retry logic with exponential backoff

### ✅ **Provider-Agnostic Interface**
- Unified API across all providers
- Easy provider switching
- Consistent message format
- Standardized error handling

## 🔧 **Setup & Configuration**

### **1. Install Dependencies**
```bash
# Core LLM libraries
pip install openai anthropic httpx

# Optional: For local models
pip install ollama-python
```

### **2. Set API Keys**
```bash
# OpenAI
export CCW_LLM_API_KEY=your_openai_api_key

# Anthropic (optional)
export CCW_ANTHROPIC_API_KEY=your_anthropic_api_key

# Or set in config file
```

### **3. Configure Providers**
Edit `config/ccw.yaml`:
```yaml
llm:
  provider: openai
  model: gpt-4
  api_key: ${CCW_LLM_API_KEY}
  
  client:
    primary_provider: openai
    fallback_providers:
      - anthropic
      - local
    enable_caching: true
    enable_fallback: true
```

## 💻 **Usage Examples**

### **Basic Text Generation**
```python
from ccw.llm.client import LLMClient
from ccw.llm.providers.base import create_user_message

client = LLMClient()

# Simple text generation
response = await client.generate_response("Explain Python decorators")
print(response.content)

# With conversation context
messages = [
    create_system_message("You are a helpful coding assistant."),
    create_user_message("What is a Python decorator?")
]
response = await client.generate_response(messages)
```

### **Streaming Responses**
```python
# Stream response in real-time
async for chunk in client.generate_stream("Write a Python function"):
    print(chunk, end="", flush=True)
```

### **Provider-Specific Requests**
```python
# Use specific provider
response = await client.generate_response(
    "Explain async/await",
    provider="anthropic",
    max_tokens=500,
    temperature=0.7
)

# Try OpenAI first, fallback to Anthropic
response = await client.generate_response(
    "Debug this code",
    provider="openai"  # Will auto-fallback if OpenAI fails
)
```

### **Embeddings**
```python
# Generate embeddings
texts = ["Python programming", "Machine learning", "Web development"]
embeddings = await client.generate_embeddings(texts)

for i, embedding in enumerate(embeddings):
    print(f"Text {i+1}: {len(embedding)} dimensions")
```

## 🔌 **Provider Details**

### **OpenAI Provider**
```python
# Supported models
models = [
    "gpt-4",
    "gpt-4-turbo", 
    "gpt-3.5-turbo",
    "gpt-3.5-turbo-16k"
]

# Features
- Function calling
- Streaming
- Embeddings (text-embedding-ada-002)
- Image understanding (GPT-4V)
- Cost tracking
```

### **Anthropic Provider**
```python
# Supported models
models = [
    "claude-3-opus-20240229",    # Most capable
    "claude-3-sonnet-20240229",  # Balanced
    "claude-3-haiku-20240307"    # Fastest
]

# Features
- Large context windows (200K tokens)
- Advanced reasoning
- Streaming
- System message support
```

### **Local Provider**
```python
# Supported backends
backends = [
    "ollama",     # Recommended
    "llamacpp",   # llama.cpp server
    "custom"      # Custom HTTP endpoint
]

# Setup Ollama
ollama pull llama2:7b
ollama serve

# Configure
llm:
  local:
    backend: ollama
    base_url: http://localhost:11434
    models: ["llama2:7b", "codellama:7b"]
```

## 🎛️ **Advanced Configuration**

### **Fallback Chain**
```yaml
llm:
  client:
    primary_provider: openai
    fallback_providers:
      - anthropic  # Try Anthropic if OpenAI fails
      - local      # Try local model as last resort
    enable_fallback: true
```

### **Caching Configuration**
```yaml
llm:
  client:
    enable_caching: true
    cache_ttl: 3600  # 1 hour
```

### **Provider-Specific Settings**
```yaml
llm:
  openai:
    models: ["gpt-4", "gpt-3.5-turbo"]
    embedding_model: text-embedding-ada-002
    
  anthropic:
    api_key: ${CCW_ANTHROPIC_API_KEY}
    models: ["claude-3-sonnet-20240229"]
    
  local:
    backend: ollama
    base_url: http://localhost:11434
```

## 📊 **Monitoring & Analytics**

### **Usage Statistics**
```python
# Get usage stats
stats = client.get_usage_stats()
print(f"Total requests: {stats['total_requests']}")
print(f"Total tokens: {stats['total_tokens']}")
print(f"Total cost: ${stats['total_cost']:.4f}")

# Provider-specific stats
for provider, usage in stats['provider_usage'].items():
    print(f"{provider}: {usage['requests']} requests, ${usage['cost']:.4f}")
```

### **Provider Validation**
```python
# Check provider connectivity
validation_results = await client.validate_providers()
for provider, is_valid in validation_results.items():
    print(f"{provider}: {'✓' if is_valid else '✗'}")
```

### **Provider Information**
```python
# Get detailed provider info
for provider_name in client.get_available_providers():
    info = client.get_provider_info(provider_name)
    print(f"{provider_name}: {info['model']} - {info['capabilities']}")
```

## 🔧 **Integration with Agents**

### **Agent LLM Usage**
```python
from ccw.llm.client import llm_client

class MyAgent(Agent):
    async def execute(self, task, context):
        # Use LLM in agent
        messages = [
            create_system_message("You are a code analysis agent."),
            create_user_message(f"Analyze this code: {task.data['code']}")
        ]
        
        response = await llm_client.generate_response(
            messages,
            max_tokens=1000,
            temperature=0.3
        )
        
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"analysis": response.content}
        )
```

### **Streaming in Agents**
```python
async def stream_analysis(self, code):
    messages = [create_user_message(f"Analyze: {code}")]
    
    analysis = ""
    async for chunk in llm_client.generate_stream(messages):
        analysis += chunk
        # Optionally emit progress updates
        await self.emit_progress(chunk)
    
    return analysis
```

## 🧪 **Testing**

### **Run LLM Tests**
```bash
# Test all providers
python examples/llm_integration_test.py

# Test specific features
python -c "
import asyncio
from ccw.llm.client import LLMClient

async def test():
    client = LLMClient()
    response = await client.generate_response('Hello, world!')
    print(response.content)

asyncio.run(test())
"
```

### **Validate Setup**
```bash
# Check provider connectivity
ccw status  # Will show LLM provider status

# Test from CLI
ccw ask "What is Python?" --provider openai
```

## 🚨 **Error Handling**

### **Common Errors**
```python
from ccw.llm.providers.base import (
    LLMProviderError,
    LLMRateLimitError, 
    LLMAuthenticationError
)

try:
    response = await client.generate_response("Hello")
except LLMAuthenticationError:
    print("Invalid API key")
except LLMRateLimitError as e:
    print(f"Rate limited, retry after {e.retry_after}s")
except LLMProviderError as e:
    print(f"Provider error: {e}")
```

### **Automatic Retry**
```python
# Automatic retry with exponential backoff
response = await client.generate_response(
    "Hello",
    max_retries=3,
    retry_delay=1.0
)
```

## 🎯 **Best Practices**

### **1. Provider Selection**
- **OpenAI**: Best for general tasks, function calling
- **Anthropic**: Best for reasoning, large context
- **Local**: Best for privacy, cost control

### **2. Performance Optimization**
- Enable caching for repeated queries
- Use streaming for long responses
- Set appropriate timeouts
- Monitor token usage

### **3. Cost Management**
- Use cheaper models for simple tasks
- Implement token limits
- Monitor usage statistics
- Use local models for development

### **4. Error Resilience**
- Configure fallback providers
- Implement proper error handling
- Use retry logic with backoff
- Validate connections regularly

## 🌟 **Summary**

The LLM integration system provides:

✅ **Multi-provider support** - OpenAI, Anthropic, Local models  
✅ **Automatic fallback** - Seamless provider switching  
✅ **Response caching** - Improved performance and cost savings  
✅ **Streaming support** - Real-time response generation  
✅ **Usage tracking** - Monitor costs and performance  
✅ **Error handling** - Robust retry and fallback logic  
✅ **Agent integration** - Easy LLM usage in agents  

**The system is production-ready and provides a solid foundation for AI-powered code analysis and reasoning!** 🚀
