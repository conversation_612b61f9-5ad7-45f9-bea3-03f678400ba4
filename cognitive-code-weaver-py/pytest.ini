[tool:pytest]
# Pytest configuration for Cognitive Code Weaver

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    --color=yes
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    agent: Agent behavior and workflow tests
    llm: LLM integration tests
    analysis: Code analysis system tests
    slow: Slow running tests (may take >5 seconds)
    performance: Performance and load tests
    smoke: Quick smoke tests for basic functionality
    regression: Regression tests for bug fixes
    
# Async support
asyncio_mode = auto

# Coverage settings
[coverage:run]
source = ccw
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2
fail_under = 80

[coverage:html]
directory = htmlcov
