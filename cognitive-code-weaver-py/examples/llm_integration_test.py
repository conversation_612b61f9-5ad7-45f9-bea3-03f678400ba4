#!/usr/bin/env python3
"""
LLM Integration Test - Comprehensive testing of LLM providers

This script tests all LLM providers and demonstrates various features
including text generation, streaming, embeddings, and error handling.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import ccw
sys.path.insert(0, str(Path(__file__).parent.parent))

from ccw.core.config import init_config
from ccw.llm.client import LLMClient, LLMClientConfig
from ccw.llm.providers.base import (
    create_message, create_system_message, create_user_message,
    MessageRole, LLMProviderError
)


async def test_basic_text_generation():
    """Test basic text generation with different providers"""
    print("\n🤖 Testing Basic Text Generation")
    print("-" * 50)
    
    client = LLMClient()
    
    # Test messages
    messages = [
        create_system_message("You are a helpful coding assistant."),
        create_user_message("Explain what a Python decorator is in simple terms.")
    ]
    
    # Test each available provider
    for provider_name in client.get_available_providers():
        try:
            print(f"\n📡 Testing {provider_name} provider...")
            
            response = await client.generate_response(
                messages=messages,
                provider=provider_name,
                max_tokens=150,
                temperature=0.7
            )
            
            print(f"✅ {provider_name} Response:")
            print(f"   Model: {response.model}")
            print(f"   Content: {response.content[:100]}...")
            print(f"   Tokens: {response.usage.get('total_tokens', 'N/A')}")
            print(f"   Time: {response.response_time:.2f}s")
            
            if response.metadata.get('cost'):
                print(f"   Cost: ${response.metadata['cost']:.4f}")
            
        except Exception as e:
            print(f"❌ {provider_name} failed: {e}")


async def test_streaming_responses():
    """Test streaming text generation"""
    print("\n🌊 Testing Streaming Responses")
    print("-" * 50)
    
    client = LLMClient()
    
    messages = [
        create_user_message("Write a short Python function to calculate fibonacci numbers.")
    ]
    
    for provider_name in client.get_available_providers():
        try:
            print(f"\n📡 Streaming from {provider_name}...")
            
            response_text = ""
            async for chunk in client.generate_stream(
                messages=messages,
                provider=provider_name,
                max_tokens=200
            ):
                response_text += chunk
                print(chunk, end="", flush=True)
            
            print(f"\n✅ {provider_name} streaming complete ({len(response_text)} chars)")
            
        except Exception as e:
            print(f"❌ {provider_name} streaming failed: {e}")


async def test_embeddings():
    """Test embedding generation"""
    print("\n🔢 Testing Embeddings")
    print("-" * 50)
    
    client = LLMClient()
    
    texts = [
        "Python is a programming language",
        "Machine learning is a subset of AI",
        "Databases store structured data"
    ]
    
    for provider_name in client.get_available_providers():
        try:
            print(f"\n📡 Testing embeddings with {provider_name}...")
            
            embeddings = await client.generate_embeddings(
                texts=texts,
                provider=provider_name
            )
            
            print(f"✅ {provider_name} embeddings:")
            for i, embedding in enumerate(embeddings):
                print(f"   Text {i+1}: {len(embedding)} dimensions")
                print(f"   Sample values: {embedding[:5]}")
            
        except Exception as e:
            print(f"❌ {provider_name} embeddings failed: {e}")


async def test_conversation_flow():
    """Test multi-turn conversation"""
    print("\n💬 Testing Conversation Flow")
    print("-" * 50)
    
    client = LLMClient()
    
    # Build a conversation
    messages = [
        create_system_message("You are a helpful Python tutor."),
        create_user_message("What is a list comprehension?"),
    ]
    
    try:
        # First response
        response1 = await client.generate_response(messages)
        print(f"🤖 Assistant: {response1.content[:100]}...")
        
        # Add assistant response to conversation
        messages.append(create_message(MessageRole.ASSISTANT, response1.content))
        
        # Follow-up question
        messages.append(create_user_message("Can you show me an example?"))
        
        # Second response
        response2 = await client.generate_response(messages)
        print(f"🤖 Assistant: {response2.content[:100]}...")
        
        print("✅ Conversation flow test completed")
        
    except Exception as e:
        print(f"❌ Conversation flow test failed: {e}")


async def test_error_handling():
    """Test error handling and fallback mechanisms"""
    print("\n⚠️  Testing Error Handling")
    print("-" * 50)
    
    # Test with invalid API key
    try:
        from ccw.llm.providers.openai_provider import OpenAIProvider
        from ccw.llm.providers.base import LLMConfig
        
        invalid_config = LLMConfig(
            provider="openai",
            model="gpt-3.5-turbo",
            api_key="invalid_key"
        )
        
        provider = OpenAIProvider(invalid_config)
        
        messages = [create_user_message("Hello")]
        await provider.generate_response(messages)
        
        print("❌ Should have failed with invalid API key")
        
    except Exception as e:
        print(f"✅ Correctly caught authentication error: {type(e).__name__}")


async def test_provider_validation():
    """Test provider connection validation"""
    print("\n🔍 Testing Provider Validation")
    print("-" * 50)
    
    client = LLMClient()
    
    validation_results = await client.validate_providers()
    
    for provider_name, is_valid in validation_results.items():
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {provider_name}: {status}")


async def test_usage_statistics():
    """Test usage statistics tracking"""
    print("\n📊 Testing Usage Statistics")
    print("-" * 50)
    
    client = LLMClient()
    
    # Make a few requests
    messages = [create_user_message("Hello, how are you?")]
    
    for i in range(3):
        try:
            await client.generate_response(messages, max_tokens=50)
        except:
            pass  # Ignore errors for this test
    
    # Get usage stats
    stats = client.get_usage_stats()
    
    print(f"📈 Usage Statistics:")
    print(f"   Total Requests: {stats['total_requests']}")
    print(f"   Successful: {stats['successful_requests']}")
    print(f"   Failed: {stats['failed_requests']}")
    print(f"   Total Tokens: {stats['total_tokens']}")
    print(f"   Total Cost: ${stats['total_cost']:.4f}")
    
    print(f"\n📊 Provider Usage:")
    for provider, usage in stats['provider_usage'].items():
        print(f"   {provider}:")
        print(f"     Requests: {usage['requests']}")
        print(f"     Tokens: {usage['tokens']}")
        print(f"     Cost: ${usage['cost']:.4f}")


async def test_caching():
    """Test response caching"""
    print("\n💾 Testing Response Caching")
    print("-" * 50)
    
    client = LLMClient()
    
    messages = [create_user_message("What is 2 + 2?")]
    
    # First request (should hit API)
    start_time = asyncio.get_event_loop().time()
    response1 = await client.generate_response(messages)
    time1 = asyncio.get_event_loop().time() - start_time
    
    # Second request (should hit cache)
    start_time = asyncio.get_event_loop().time()
    response2 = await client.generate_response(messages)
    time2 = asyncio.get_event_loop().time() - start_time
    
    print(f"✅ First request: {time1:.3f}s")
    print(f"✅ Second request: {time2:.3f}s (cached)")
    print(f"   Speedup: {time1/time2:.1f}x faster")
    
    # Verify responses are the same
    if response1.content == response2.content:
        print("✅ Cache working correctly - responses match")
    else:
        print("❌ Cache issue - responses don't match")


async def test_provider_info():
    """Test provider information retrieval"""
    print("\n📋 Testing Provider Information")
    print("-" * 50)
    
    client = LLMClient()
    
    for provider_name in client.get_available_providers():
        info = client.get_provider_info(provider_name)
        if info:
            print(f"\n📊 {provider_name} Information:")
            print(f"   Model: {info['model']}")
            print(f"   Capabilities: {', '.join(info['capabilities'])}")
            
            metrics = info['metrics']
            print(f"   Requests Made: {metrics['requests_made']}")
            print(f"   Average Response Time: {metrics['average_response_time']:.2f}s")


async def main():
    """Run all LLM integration tests"""
    print("🧠 Cognitive Code Weaver - LLM Integration Tests")
    print("=" * 60)
    
    # Initialize configuration
    config_path = Path(__file__).parent.parent / "config" / "ccw.yaml"
    init_config(config_file=str(config_path))
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during tests
    
    # Check for API keys
    api_key = os.getenv("CCW_LLM_API_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  Warning: No API key found. Set CCW_LLM_API_KEY or OPENAI_API_KEY")
        print("   Some tests may fail without valid API credentials.")
    
    try:
        # Run all tests
        await test_provider_validation()
        await test_basic_text_generation()
        await test_streaming_responses()
        await test_embeddings()
        await test_conversation_flow()
        await test_error_handling()
        await test_usage_statistics()
        await test_caching()
        await test_provider_info()
        
        print("\n🎉 All LLM integration tests completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error during tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
