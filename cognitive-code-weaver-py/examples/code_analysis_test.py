#!/usr/bin/env python3
"""
Code Analysis Test - Comprehensive testing of code analysis capabilities

This script demonstrates and tests all code analysis features including
parsing, AST analysis, dependency graphs, metrics, and workspace analysis.
"""

import asyncio
import json
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import ccw
sys.path.insert(0, str(Path(__file__).parent.parent))

from ccw.analysis.parser import CodeParser, LanguageType
from ccw.analysis.ast_analyzer import ASTAnalyzer
from ccw.analysis.dependency_graph import DependencyAnalyzer
from ccw.analysis.metrics import ComplexityAnalyzer
from ccw.analysis.symbols import SymbolExtractor
from ccw.analysis.workspace import WorkspaceAnalyzer


def test_code_parsing():
    """Test code parsing capabilities"""
    print("\n🔍 Testing Code Parsing")
    print("-" * 50)
    
    parser = CodeParser()
    
    # Test Python code
    python_code = '''
def fibonacci(n):
    """Calculate fibonacci number"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result

import math
from collections import defaultdict
'''
    
    print("📄 Parsing Python code...")
    result = parser.parse_content(python_code, LanguageType.PYTHON, "test.py")
    
    print(f"✅ Language detected: {result.language.value}")
    print(f"✅ Functions found: {len(result.functions)}")
    for func in result.functions:
        print(f"   - {func['name']} (line {func['line']})")
    
    print(f"✅ Classes found: {len(result.classes)}")
    for cls in result.classes:
        print(f"   - {cls['name']} (line {cls['line']})")
    
    print(f"✅ Imports found: {len(result.imports)}")
    for imp in result.imports:
        print(f"   - {imp}")
    
    # Test JavaScript code
    js_code = '''
function calculateArea(radius) {
    return Math.PI * radius * radius;
}

const greet = (name) => {
    return `Hello, ${name}!`;
};

class Rectangle {
    constructor(width, height) {
        this.width = width;
        this.height = height;
    }
    
    getArea() {
        return this.width * this.height;
    }
}

import { Component } from 'react';
const express = require('express');
'''
    
    print("\n📄 Parsing JavaScript code...")
    result = parser.parse_content(js_code, LanguageType.JAVASCRIPT, "test.js")
    
    print(f"✅ Language detected: {result.language.value}")
    print(f"✅ Functions found: {len(result.functions)}")
    print(f"✅ Classes found: {len(result.classes)}")
    print(f"✅ Imports found: {len(result.imports)}")


def test_ast_analysis():
    """Test AST analysis capabilities"""
    print("\n🌳 Testing AST Analysis")
    print("-" * 50)
    
    parser = CodeParser()
    ast_analyzer = ASTAnalyzer()
    
    # Complex Python code for analysis
    complex_code = '''
def complex_function(data, threshold=10):
    """A complex function with multiple control flows"""
    results = []
    
    for item in data:
        if item is None:
            continue
            
        if isinstance(item, dict):
            for key, value in item.items():
                if value > threshold:
                    if key.startswith('important_'):
                        try:
                            processed = process_important(value)
                            results.append(processed)
                        except ValueError as e:
                            print(f"Error processing {key}: {e}")
                            continue
                    else:
                        results.append(value * 2)
                elif value < 0:
                    results.append(0)
        elif isinstance(item, (int, float)):
            if item > threshold:
                results.append(item ** 2)
            else:
                results.append(item)
    
    return sorted(results) if results else []

class DataProcessor:
    def __init__(self, config):
        self.config = config
        self.cache = {}
    
    def process(self, data):
        if not data:
            return []
        
        cache_key = hash(str(data))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = complex_function(data, self.config.get('threshold', 5))
        self.cache[cache_key] = result
        return result
'''
    
    print("📊 Analyzing complex Python code...")
    parse_result = parser.parse_content(complex_code, LanguageType.PYTHON, "complex.py")
    analysis = ast_analyzer.analyze(parse_result)
    
    print(f"✅ Functions analyzed: {len(analysis['functions'])}")
    for func in analysis['functions']:
        print(f"   - {func.name}: complexity={func.complexity}, lines={func.line_count}")
    
    print(f"✅ Classes analyzed: {len(analysis['classes'])}")
    for cls in analysis['classes']:
        print(f"   - {cls.name}: methods={len(cls.methods)}, lines={cls.line_count}")
    
    print(f"✅ Complexity metrics:")
    metrics = analysis['complexity_metrics']
    print(f"   - Cyclomatic complexity: {metrics['cyclomatic_complexity']}")
    print(f"   - Functions count: {metrics['functions_count']}")
    print(f"   - Classes count: {metrics['classes_count']}")


def test_dependency_analysis():
    """Test dependency graph analysis"""
    print("\n🔗 Testing Dependency Analysis")
    print("-" * 50)
    
    parser = CodeParser()
    dependency_analyzer = DependencyAnalyzer()
    
    # Create multiple related files
    files_content = {
        "main.py": '''
from utils import helper_function
from models import User, Product
import database

def main():
    db = database.connect()
    user = User("John")
    product = Product("Laptop")
    result = helper_function(user, product)
    return result

if __name__ == "__main__":
    main()
''',
        "utils.py": '''
def helper_function(user, product):
    return f"{user.name} bought {product.name}"

def format_currency(amount):
    return f"${amount:.2f}"
''',
        "models.py": '''
class User:
    def __init__(self, name):
        self.name = name
    
    def get_info(self):
        return f"User: {self.name}"

class Product:
    def __init__(self, name):
        self.name = name
        self.price = 0.0
    
    def set_price(self, price):
        self.price = price
''',
        "database.py": '''
def connect():
    return "database_connection"

def query(sql):
    return []
'''
    }
    
    # Parse all files
    parse_results = []
    for file_path, content in files_content.items():
        result = parser.parse_content(content, LanguageType.PYTHON, file_path)
        parse_results.append(result)
    
    print("🔍 Building dependency graph...")
    dependency_graph = dependency_analyzer.analyze_workspace(parse_results)
    
    print(f"✅ Nodes in graph: {len(dependency_graph.nodes)}")
    print(f"✅ Edges in graph: {len(dependency_graph.edges)}")
    
    # Check for circular dependencies
    circular_deps = dependency_graph.find_circular_dependencies()
    print(f"✅ Circular dependencies: {len(circular_deps)}")
    
    # Calculate coupling metrics
    metrics = dependency_graph.calculate_coupling_metrics()
    print(f"✅ Coupling metrics:")
    print(f"   - Average dependencies: {metrics['average_dependencies']:.2f}")
    print(f"   - Average dependents: {metrics['average_dependents']:.2f}")
    print(f"   - Coupling ratio: {metrics['coupling_ratio']:.3f}")


def test_code_metrics():
    """Test code metrics calculation"""
    print("\n📊 Testing Code Metrics")
    print("-" * 50)
    
    parser = CodeParser()
    complexity_analyzer = ComplexityAnalyzer()
    
    # Code with various complexity patterns
    test_code = '''
def simple_function():
    """A simple function"""
    return "Hello, World!"

def complex_function(data):
    """A complex function with high cyclomatic complexity"""
    result = 0
    
    for item in data:  # +1
        if item > 0:   # +1
            if item % 2 == 0:  # +1
                result += item * 2
            else:  # +1
                result += item
        elif item < 0:  # +1
            result -= abs(item)
        else:
            continue
    
    while result > 1000:  # +1
        result //= 2
    
    try:
        if result > 100:  # +1
            return result / 10
        else:
            return result
    except ZeroDivisionError:  # +1
        return 0

class ComplexClass:
    """A class with multiple methods"""
    
    def __init__(self, value):
        self.value = value
        self.cache = {}
    
    def method1(self, x):
        if x in self.cache:
            return self.cache[x]
        
        result = x * self.value
        self.cache[x] = result
        return result
    
    def method2(self, items):
        return [self.method1(item) for item in items if item > 0]

# Some variables
CONSTANT_VALUE = 42
global_var = "test"
another_var = [1, 2, 3, 4, 5]
'''
    
    print("📈 Calculating code metrics...")
    parse_result = parser.parse_content(test_code, LanguageType.PYTHON, "metrics_test.py")
    metrics = complexity_analyzer.analyze(parse_result)
    
    print(f"✅ File: {metrics.file_path}")
    print(f"✅ Language: {metrics.language.value}")
    
    print(f"✅ Size metrics:")
    print(f"   - Lines of code: {metrics.size.lines_of_code}")
    print(f"   - Source lines: {metrics.size.source_lines_of_code}")
    print(f"   - Comment lines: {metrics.size.comment_lines}")
    print(f"   - Functions: {metrics.size.functions_count}")
    print(f"   - Classes: {metrics.size.classes_count}")
    
    print(f"✅ Complexity metrics:")
    print(f"   - Cyclomatic complexity: {metrics.complexity.cyclomatic_complexity}")
    print(f"   - Cognitive complexity: {metrics.complexity.cognitive_complexity}")
    print(f"   - Nesting depth: {metrics.complexity.nesting_depth}")
    
    print(f"✅ Quality metrics:")
    print(f"   - Comment ratio: {metrics.quality.comment_ratio:.3f}")
    print(f"   - Docstring coverage: {metrics.quality.docstring_coverage:.3f}")
    print(f"   - Naming consistency: {metrics.quality.naming_consistency:.3f}")
    
    print(f"✅ Maintainability index: {metrics.maintainability_index:.2f}")
    print(f"✅ Technical debt ratio: {metrics.technical_debt_ratio:.3f}")


def test_symbol_extraction():
    """Test symbol extraction and analysis"""
    print("\n🔤 Testing Symbol Extraction")
    print("-" * 50)
    
    parser = CodeParser()
    symbol_extractor = SymbolExtractor()
    
    symbol_test_code = '''
import os
from typing import List, Dict

# Global constants
MAX_RETRIES = 3
DEFAULT_TIMEOUT = 30.0

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self, connection_string: str):
        self._connection_string = connection_string
        self.__private_key = "secret"
        self.is_connected = False
    
    def connect(self) -> bool:
        """Establish database connection"""
        try:
            # Connection logic here
            self.is_connected = True
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def _internal_method(self):
        """Internal method for database operations"""
        pass
    
    @property
    def connection_status(self):
        return self.is_connected

def utility_function(data: List[str]) -> Dict[str, int]:
    """Utility function for data processing"""
    result = {}
    for item in data:
        result[item] = len(item)
    return result

# Module-level variable
module_config = {"debug": True, "version": "1.0"}
'''
    
    print("🔍 Extracting symbols...")
    parse_result = parser.parse_content(symbol_test_code, LanguageType.PYTHON, "symbols_test.py")
    symbols = symbol_extractor.extract(parse_result)
    
    print(f"✅ Total symbols found: {len(symbols)}")
    
    # Group symbols by type
    symbol_groups = {}
    for symbol in symbols:
        symbol_type = symbol.symbol_type.value
        if symbol_type not in symbol_groups:
            symbol_groups[symbol_type] = []
        symbol_groups[symbol_type].append(symbol)
    
    for symbol_type, group_symbols in symbol_groups.items():
        print(f"✅ {symbol_type.title()}s ({len(group_symbols)}):")
        for symbol in group_symbols[:5]:  # Show first 5
            visibility = symbol.visibility.value
            scope = symbol.scope or "global"
            print(f"   - {symbol.name} ({visibility}, {scope}) line {symbol.line_number}")


def test_workspace_analysis():
    """Test comprehensive workspace analysis"""
    print("\n🏢 Testing Workspace Analysis")
    print("-" * 50)
    
    workspace_analyzer = WorkspaceAnalyzer()
    
    # Analyze the current project
    current_dir = Path(__file__).parent.parent
    ccw_analysis_dir = current_dir / "ccw" / "analysis"
    
    if ccw_analysis_dir.exists():
        print(f"📁 Analyzing workspace: {ccw_analysis_dir}")
        
        # Limit to a few files for testing
        result = workspace_analyzer.analyze_workspace(
            str(ccw_analysis_dir),
            max_files=5
        )
        
        print(f"✅ Workspace analysis completed!")
        print(f"   - Total files: {result.total_files}")
        print(f"   - Analyzed files: {result.analyzed_files}")
        print(f"   - Skipped files: {result.skipped_files}")
        print(f"   - Errors: {len(result.errors)}")
        
        if result.workspace_metrics:
            print(f"✅ Workspace metrics:")
            metrics = result.workspace_metrics
            print(f"   - Total LOC: {metrics.get('total_lines_of_code', 0)}")
            print(f"   - Total functions: {metrics.get('total_functions', 0)}")
            print(f"   - Total classes: {metrics.get('total_classes', 0)}")
            print(f"   - Average complexity: {metrics.get('average_complexity', 0):.2f}")
        
        if result.quality_report:
            print(f"✅ Quality report:")
            quality = result.quality_report
            print(f"   - Overall grade: {quality.get('overall_grade', 'N/A')}")
            print(f"   - Average maintainability: {quality.get('average_maintainability', 0):.2f}")
            print(f"   - Average comment ratio: {quality.get('average_comment_ratio', 0):.3f}")
        
        if result.errors:
            print(f"⚠️  Errors encountered:")
            for error in result.errors[:3]:  # Show first 3 errors
                print(f"   - {error}")
    else:
        print("⚠️  Analysis directory not found, skipping workspace test")


def test_single_file_analysis():
    """Test comprehensive single file analysis"""
    print("\n📄 Testing Single File Analysis")
    print("-" * 50)
    
    workspace_analyzer = WorkspaceAnalyzer()
    
    # Analyze this test file itself
    test_file = __file__
    print(f"📄 Analyzing file: {test_file}")
    
    result = workspace_analyzer.analyze_file(test_file)
    
    if "errors" in result and result["errors"]:
        print(f"❌ Analysis failed: {result['errors']}")
        return
    
    print(f"✅ File analysis completed!")
    print(f"   - Language: {result['language']}")
    print(f"   - Functions: {len(result['parse_result']['functions'])}")
    print(f"   - Classes: {len(result['parse_result']['classes'])}")
    print(f"   - Symbols: {len(result['symbols'])}")
    
    if "metrics" in result:
        metrics = result["metrics"]
        print(f"✅ Metrics:")
        print(f"   - Lines of code: {metrics['size']['lines_of_code']}")
        print(f"   - Complexity: {metrics['complexity']['cyclomatic_complexity']}")
        print(f"   - Maintainability: {metrics['maintainability_index']:.2f}")


def main():
    """Run all code analysis tests"""
    print("🧠 Cognitive Code Weaver - Code Analysis Tests")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during tests
    
    try:
        # Run all tests
        test_code_parsing()
        test_ast_analysis()
        test_dependency_analysis()
        test_code_metrics()
        test_symbol_extraction()
        test_single_file_analysis()
        test_workspace_analysis()
        
        print("\n🎉 All code analysis tests completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error during tests: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
