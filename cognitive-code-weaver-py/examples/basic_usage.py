#!/usr/bin/env python3
"""
Basic Usage Example for Cognitive Code Weaver

This script demonstrates how to use the Cognitive Code Weaver system
programmatically for code analysis and reasoning.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the parent directory to the path so we can import ccw
sys.path.insert(0, str(Path(__file__).parent.parent))

from ccw.core.config import init_config
from ccw.core.registry import agent_registry
from ccw.core.message_bus import message_bus
from ccw.core.agent import create_agent_task, create_agent_context
from ccw.agents.master_agent import MasterAgent
from ccw.agents.cognitive_agent import CognitiveAgent


async def initialize_system():
    """Initialize the Cognitive Code Weaver system"""
    print("🚀 Initializing Cognitive Code Weaver...")
    
    # Initialize configuration
    config_path = Path(__file__).parent.parent / "config" / "ccw.yaml"
    init_config(config_file=str(config_path))
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Start message bus
    await message_bus.start()
    print("✓ Message bus started")
    
    # Load plugins (if any)
    agent_registry.load_plugins()
    print("✓ Plugins loaded")
    
    # Create and register agents
    master_agent = MasterAgent()
    cognitive_agent = CognitiveAgent()
    
    print("✓ Agents initialized")
    print("✓ System ready!")
    
    return master_agent


async def example_code_analysis():
    """Example: Analyze code structure"""
    print("\n📊 Example 1: Code Analysis")
    print("-" * 40)
    
    master_agent = await initialize_system()
    
    try:
        # Create analysis task
        task = create_agent_task(
            task_type="query_orchestration",
            description="Analyze code structure",
            data={
                "query": "Analyze the structure of this Python project and explain the main components"
            }
        )
        
        # Create context
        context = create_agent_context(
            session_id="example_analysis",
            user_query="Analyze the structure of this Python project",
            workspace_path=str(Path(__file__).parent.parent)
        )
        
        # Execute analysis
        print("🔍 Analyzing code structure...")
        result = await master_agent.execute_with_monitoring(task, context)
        
        # Display results
        if result.status.value == "completed":
            print("✅ Analysis completed successfully!")
            print(f"Answer: {result.data.get('answer', 'No answer provided')}")
            print(f"Confidence: {result.confidence:.2%}")
            
            if result.data.get('suggestions'):
                print("\nSuggestions:")
                for suggestion in result.data['suggestions']:
                    print(f"  • {suggestion}")
        else:
            print(f"❌ Analysis failed: {result.error}")
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
    
    finally:
        await shutdown_system()


async def example_question_answering():
    """Example: Ask questions about code"""
    print("\n❓ Example 2: Question Answering")
    print("-" * 40)
    
    master_agent = await initialize_system()
    
    try:
        questions = [
            "How does the agent system work?",
            "What are the main components of this system?",
            "How do agents communicate with each other?"
        ]
        
        for question in questions:
            print(f"\n🤔 Question: {question}")
            
            # Create query task
            task = create_agent_task(
                task_type="query_orchestration",
                description="Answer user question",
                data={"query": question}
            )
            
            # Create context
            context = create_agent_context(
                session_id=f"qa_{hash(question)}",
                user_query=question,
                workspace_path=str(Path(__file__).parent.parent)
            )
            
            # Execute query
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Display results
            if result.status.value == "completed":
                print(f"💡 Answer: {result.data.get('answer', 'No answer provided')}")
                print(f"🎯 Confidence: {result.confidence:.2%}")
            else:
                print(f"❌ Failed: {result.error}")
                
    except Exception as e:
        print(f"❌ Error during Q&A: {e}")
    
    finally:
        await shutdown_system()


async def example_system_status():
    """Example: Get system status"""
    print("\n📈 Example 3: System Status")
    print("-" * 40)
    
    master_agent = await initialize_system()
    
    try:
        # Create status task
        task = create_agent_task(
            task_type="system_management",
            description="Get system status",
            data={"command": "status"}
        )
        
        # Create context
        context = create_agent_context(
            session_id="status_check"
        )
        
        # Execute status check
        result = await master_agent.execute_with_monitoring(task, context)
        
        # Display results
        if result.status.value == "completed":
            status_data = result.data
            print("📊 System Status:")
            print(f"  • System Status: {status_data.get('system_status', 'unknown')}")
            print(f"  • Active Queries: {status_data.get('active_queries', 0)}")
            print(f"  • Registered Agents: {status_data.get('registered_agents', 0)}")
            print(f"  • Available Agents: {status_data.get('available_agents', 0)}")
            
            metrics = status_data.get('system_metrics', {})
            if metrics:
                print("📈 System Metrics:")
                print(f"  • Queries Processed: {metrics.get('queries_processed', 0)}")
                print(f"  • Average Response Time: {metrics.get('average_response_time', 0):.2f}s")
                print(f"  • Success Rate: {metrics.get('success_rate', 0):.2%}")
        else:
            print(f"❌ Failed to get status: {result.error}")
            
    except Exception as e:
        print(f"❌ Error getting status: {e}")
    
    finally:
        await shutdown_system()


async def example_agent_listing():
    """Example: List all agents"""
    print("\n🤖 Example 4: Agent Listing")
    print("-" * 40)
    
    await initialize_system()
    
    try:
        # Get agent information
        agents_info = agent_registry.list_agents()
        
        print("🤖 Registered Agents:")
        for agent_id, info in agents_info.items():
            status = info.get('status', 'unknown')
            capabilities = info.get('capabilities', [])
            
            print(f"\n  📋 {agent_id}")
            print(f"     Status: {status}")
            print(f"     Capabilities: {', '.join(capabilities) if capabilities else 'None'}")
            
            if 'metrics' in info:
                metrics = info['metrics']
                print(f"     Tasks Completed: {metrics.get('tasks_completed', 0)}")
                print(f"     Tasks Failed: {metrics.get('tasks_failed', 0)}")
                print(f"     Avg Execution Time: {metrics.get('average_execution_time', 0):.2f}s")
                
    except Exception as e:
        print(f"❌ Error listing agents: {e}")
    
    finally:
        await shutdown_system()


async def shutdown_system():
    """Shutdown the system gracefully"""
    print("\n🛑 Shutting down system...")
    
    try:
        await agent_registry.shutdown_all_agents()
        await message_bus.stop()
        print("✓ System shutdown complete")
    except Exception as e:
        print(f"❌ Error during shutdown: {e}")


async def main():
    """Main function to run all examples"""
    print("🧠 Cognitive Code Weaver - Basic Usage Examples")
    print("=" * 50)
    
    try:
        # Run examples
        await example_code_analysis()
        await example_question_answering()
        await example_system_status()
        await example_agent_listing()
        
        print("\n🎉 All examples completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
