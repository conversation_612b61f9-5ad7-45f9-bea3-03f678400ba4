name: 🧠 Cognitive Code Weaver CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  POETRY_VERSION: '1.6.1'

jobs:
  # Quality Gates - Fast feedback
  quality-gates:
    name: 🎯 Quality Gates
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black mypy
        pip install -r requirements.txt
        
    - name: 🔍 Code Formatting Check
      run: |
        black --check --diff .
        
    - name: 🧹 Linting
      run: |
        flake8 ccw/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 ccw/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: 🏷️ Type Checking
      run: |
        mypy ccw/ --ignore-missing-imports --no-strict-optional
        
    - name: 🧪 Environment Validation
      run: |
        python tests/run_tests.py --validate

  # Unit Tests - Core functionality
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: quality-gates
    timeout-minutes: 15
    
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']
        
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🧪 Run Unit Tests
      run: |
        python tests/run_tests.py --type unit --coverage --parallel
        
    - name: 📊 Upload Coverage to Codecov
      if: matrix.python-version == '3.11'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # Integration Tests - System interactions
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    timeout-minutes: 20
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🔗 Run Integration Tests
      run: |
        python tests/run_tests.py --type integration --parallel
        
    - name: 📈 Performance Baseline
      run: |
        python tests/run_tests.py --performance

  # Agent Behavior Tests - AI functionality
  agent-tests:
    name: 🤖 Agent Behavior Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    timeout-minutes: 25
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🤖 Run Agent Tests
      run: |
        python tests/run_tests.py --type agent --parallel
        
    - name: 🧠 LLM Integration Tests
      run: |
        python tests/run_tests.py --markers llm
      env:
        # Mock LLM credentials for testing
        OPENAI_API_KEY: "test-key"
        ANTHROPIC_API_KEY: "test-key"

  # Security Scanning
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: quality-gates
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit
        pip install -r requirements.txt
        
    - name: 🔍 Dependency Security Check
      run: |
        safety check --json
        
    - name: 🛡️ Code Security Analysis
      run: |
        bandit -r ccw/ -f json -o bandit-report.json
        
    - name: 📤 Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json

  # Documentation Tests
  docs-tests:
    name: 📚 Documentation Tests
    runs-on: ubuntu-latest
    needs: quality-gates
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install sphinx sphinx-rtd-theme
        
    - name: 📖 Test Documentation Build
      run: |
        # Test that documentation can be built
        python -c "import ccw; help(ccw)"
        
    - name: 🔗 Test CLI Help
      run: |
        python -m ccw.cli.main --help
        python -m ccw.cli.main analyze --help

  # Build and Package Tests
  build-tests:
    name: 📦 Build Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Build Tools
      run: |
        python -m pip install --upgrade pip
        pip install build twine
        
    - name: 🏗️ Build Package
      run: |
        python -m build
        
    - name: ✅ Check Package
      run: |
        twine check dist/*
        
    - name: 📤 Upload Build Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist-packages
        path: dist/

  # Deployment Readiness Check
  deployment-check:
    name: 🚀 Deployment Readiness
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, agent-tests, security-scan, build-tests]
    if: github.ref == 'refs/heads/main'
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🧪 Smoke Tests
      run: |
        python tests/run_tests.py --markers smoke
        
    - name: 📊 Generate Test Report
      run: |
        python tests/run_tests.py --type all --coverage > test-report.txt
        
    - name: 📤 Upload Test Report
      uses: actions/upload-artifact@v3
      with:
        name: test-report
        path: test-report.txt
        
    - name: ✅ Deployment Ready
      run: |
        echo "🎉 All checks passed! Ready for deployment."
