name: 🔄 Test Automation & Regression

on:
  schedule:
    # Run comprehensive tests daily at 1 AM UTC
    - cron: '0 1 * * *'
    # Run regression tests every 6 hours
    - cron: '0 */6 * * *'
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test suite to run'
        required: true
        default: 'full'
        type: choice
        options:
        - full
        - regression
        - smoke
        - stress
      notify_on_failure:
        description: 'Send notifications on failure'
        required: false
        default: true
        type: boolean

env:
  PYTHON_VERSION: '3.11'
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

jobs:
  # Smoke Tests - Quick validation
  smoke-tests:
    name: 💨 Smoke Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 💨 Run Smoke Tests
      run: |
        python tests/run_tests.py --markers smoke --verbose
        
    - name: 📊 Smoke Test Report
      if: always()
      run: |
        echo "Smoke tests completed at $(date)"
        echo "Status: ${{ job.status }}"

  # Regression Tests - Detect breaking changes
  regression-tests:
    name: 🔄 Regression Tests
    runs-on: ubuntu-latest
    needs: smoke-tests
    if: github.event.inputs.test_suite == 'regression' || github.event.inputs.test_suite == 'full' || github.event_name == 'schedule'
    timeout-minutes: 30
    
    strategy:
      matrix:
        test-category: [unit, integration, agent]
        
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🔄 Run Regression Tests
      run: |
        python tests/run_tests.py --type ${{ matrix.test-category }} --markers regression --parallel --verbose
        
    - name: 📊 Generate Test Report
      if: always()
      run: |
        echo "## Regression Test Report - ${{ matrix.test-category }}" > regression-report-${{ matrix.test-category }}.md
        echo "- **Status**: ${{ job.status }}" >> regression-report-${{ matrix.test-category }}.md
        echo "- **Timestamp**: $(date)" >> regression-report-${{ matrix.test-category }}.md
        echo "- **Commit**: ${{ github.sha }}" >> regression-report-${{ matrix.test-category }}.md
        
    - name: 📤 Upload Regression Report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: regression-reports
        path: regression-report-*.md

  # Stress Tests - System limits and stability
  stress-tests:
    name: 💪 Stress Tests
    runs-on: ubuntu-latest
    needs: smoke-tests
    if: github.event.inputs.test_suite == 'stress' || github.event.inputs.test_suite == 'full' || (github.event_name == 'schedule' && github.event.schedule == '0 1 * * *')
    timeout-minutes: 45
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install psutil
        
    - name: 💪 Large Workspace Stress Test
      run: |
        python -c "
        import tempfile
        import time
        import psutil
        from pathlib import Path
        from ccw.analysis.workspace import WorkspaceAnalyzer
        from tests.utils.test_helpers import TestDataGenerator
        
        print('🔥 Starting large workspace stress test...')
        
        # Create large test workspace
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = TestDataGenerator.create_test_workspace(Path(temp_dir), 100)
            
            # Monitor system resources
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            analyzer = WorkspaceAnalyzer()
            
            start_time = time.time()
            result = analyzer.analyze_workspace(str(workspace), max_files=100)
            end_time = time.time()
            
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_used = final_memory - initial_memory
            
            print(f'✅ Analyzed {result.analyzed_files} files in {end_time - start_time:.2f}s')
            print(f'📊 Memory usage: {memory_used:.2f} MB')
            print(f'⚡ Performance: {result.analyzed_files / (end_time - start_time):.1f} files/sec')
            
            # Stress test thresholds
            if end_time - start_time > 120:  # 2 minutes
                print('⚠️ WARNING: Analysis took longer than expected')
            if memory_used > 1000:  # 1 GB
                print('⚠️ WARNING: High memory usage detected')
                exit(1)
        "
        
    - name: 💪 Concurrent Agent Stress Test
      run: |
        python -c "
        import asyncio
        import time
        from ccw.core.agent import AgentTask, AgentContext
        from ccw.core.registry import agent_registry
        from tests.utils.test_helpers import MockFactory
        
        async def stress_test_agents():
            print('🤖 Starting concurrent agent stress test...')
            
            # Create many mock agents
            agents = []
            for i in range(50):
                agent = MockFactory.create_mock_agent(f'stress_agent_{i}', ['stress_test'])
                await agent.startup()
                agent_registry.register_agent_instance(agent)
                agents.append(agent)
            
            # Create many concurrent tasks
            tasks = []
            for i in range(200):
                task = AgentTask(task_type='stress_test', description=f'Stress task {i}')
                context = AgentContext(session_id=f'stress_session_{i}')
                
                # Distribute tasks across agents
                agent = agents[i % len(agents)]
                tasks.append(agent.execute_with_monitoring(task, context))
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Analyze results
            successful = sum(1 for r in results if not isinstance(r, Exception))
            failed = len(results) - successful
            
            print(f'✅ Completed {successful}/{len(tasks)} tasks in {end_time - start_time:.2f}s')
            print(f'❌ Failed tasks: {failed}')
            print(f'⚡ Throughput: {successful / (end_time - start_time):.1f} tasks/sec')
            
            # Cleanup
            for agent in agents:
                await agent.shutdown()
                agent_registry.unregister_agent(agent.agent_id)
            
            # Stress test thresholds
            if failed > len(tasks) * 0.05:  # 5% failure rate
                print('⚠️ WARNING: High task failure rate')
                exit(1)
        
        asyncio.run(stress_test_agents())
        "

  # Cross-Platform Tests
  cross-platform-tests:
    name: 🌐 Cross-Platform Tests
    runs-on: ${{ matrix.os }}
    needs: smoke-tests
    if: github.event.inputs.test_suite == 'full' || (github.event_name == 'schedule' && github.event.schedule == '0 1 * * *')
    timeout-minutes: 25
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.9', '3.11']
        
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🌐 Run Cross-Platform Tests
      run: |
        python tests/run_tests.py --type unit --parallel
        
    - name: 📊 Platform Test Report
      if: always()
      run: |
        echo "Platform: ${{ matrix.os }}"
        echo "Python: ${{ matrix.python-version }}"
        echo "Status: ${{ job.status }}"

  # Test Data Validation
  test-data-validation:
    name: 📋 Test Data Validation
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 📋 Validate Test Data Generation
      run: |
        python -c "
        import tempfile
        from pathlib import Path
        from tests.utils.test_helpers import TestDataGenerator, MockFactory
        
        print('🔍 Validating test data generation...')
        
        # Test code generation
        for complexity in ['simple', 'medium', 'complex']:
            python_code = TestDataGenerator.generate_python_code(complexity)
            js_code = TestDataGenerator.generate_javascript_code(complexity)
            
            assert len(python_code) > 0, f'Empty Python code for {complexity}'
            assert len(js_code) > 0, f'Empty JavaScript code for {complexity}'
            assert 'def ' in python_code, f'No functions in Python {complexity} code'
            assert 'function' in js_code or '=>' in js_code, f'No functions in JS {complexity} code'
            
            print(f'✅ {complexity} code generation validated')
        
        # Test workspace generation
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = TestDataGenerator.create_test_workspace(Path(temp_dir), 10)
            
            py_files = list(workspace.glob('**/*.py'))
            js_files = list(workspace.glob('**/*.js'))
            
            assert len(py_files) > 0, 'No Python files generated'
            assert len(js_files) > 0, 'No JavaScript files generated'
            
            print(f'✅ Workspace generation validated: {len(py_files)} .py, {len(js_files)} .js files')
        
        # Test mock factories
        mock_agent = MockFactory.create_mock_agent('test', ['test_cap'])
        mock_llm = MockFactory.create_mock_llm_client()
        
        assert mock_agent.agent_id == 'test', 'Mock agent ID mismatch'
        assert 'test_cap' in mock_agent.capabilities, 'Mock agent capabilities mismatch'
        
        print('✅ Mock factories validated')
        print('🎉 All test data validation passed!')
        "

  # Notification and Reporting
  notify-results:
    name: 📢 Notify Results
    runs-on: ubuntu-latest
    needs: [smoke-tests, regression-tests, stress-tests, cross-platform-tests, test-data-validation]
    if: always() && (github.event.inputs.notify_on_failure == 'true' || github.event_name == 'schedule')
    timeout-minutes: 5
    
    steps:
    - name: 📊 Collect Results
      run: |
        echo "## Test Automation Results" > results.md
        echo "**Timestamp**: $(date)" >> results.md
        echo "**Trigger**: ${{ github.event_name }}" >> results.md
        echo "**Commit**: ${{ github.sha }}" >> results.md
        echo "" >> results.md
        echo "### Job Results:" >> results.md
        echo "- Smoke Tests: ${{ needs.smoke-tests.result }}" >> results.md
        echo "- Regression Tests: ${{ needs.regression-tests.result }}" >> results.md
        echo "- Stress Tests: ${{ needs.stress-tests.result }}" >> results.md
        echo "- Cross-Platform Tests: ${{ needs.cross-platform-tests.result }}" >> results.md
        echo "- Test Data Validation: ${{ needs.test-data-validation.result }}" >> results.md
        
    - name: 📤 Upload Results
      uses: actions/upload-artifact@v3
      with:
        name: automation-results
        path: results.md
        
    - name: 📢 Slack Notification
      if: env.SLACK_WEBHOOK_URL != '' && (contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled'))
      run: |
        curl -X POST -H 'Content-type: application/json' \
        --data '{"text":"🚨 CCW Test Automation Alert: Some tests failed in automated run. Check GitHub Actions for details."}' \
        ${{ env.SLACK_WEBHOOK_URL }}
        
    - name: ✅ Success Summary
      if: needs.smoke-tests.result == 'success' && needs.regression-tests.result == 'success'
      run: |
        echo "🎉 All critical tests passed!"
        echo "✅ Smoke tests: PASSED"
        echo "✅ Regression tests: PASSED"
