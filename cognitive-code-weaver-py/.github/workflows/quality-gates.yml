name: 🎯 Quality Gates

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'
  COVERAGE_THRESHOLD: 80
  COMPLEXITY_THRESHOLD: 10
  DUPLICATION_THRESHOLD: 5

jobs:
  # Code Quality Analysis
  code-quality:
    name: 📊 Code Quality Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for better analysis
        
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Quality Tools
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install radon vulture bandit safety
        pip install flake8-complexity flake8-docstrings flake8-import-order
        
    - name: 📏 Code Complexity Analysis
      run: |
        echo "## Code Complexity Report" > complexity-report.md
        echo "### Cyclomatic Complexity" >> complexity-report.md
        radon cc ccw/ --min B --show-complexity >> complexity-report.md
        
        echo "### Maintainability Index" >> complexity-report.md
        radon mi ccw/ --min B >> complexity-report.md
        
        echo "### Halstead Metrics" >> complexity-report.md
        radon hal ccw/ >> complexity-report.md
        
        # Check complexity thresholds
        COMPLEX_FILES=$(radon cc ccw/ --min C --json | python -c "
        import sys, json
        data = json.load(sys.stdin)
        complex_files = []
        for file, metrics in data.items():
            for item in metrics:
                if item['complexity'] > ${{ env.COMPLEXITY_THRESHOLD }}:
                    complex_files.append(f'{file}:{item[\"name\"]}')
        print('\n'.join(complex_files))
        ")
        
        if [ ! -z "$COMPLEX_FILES" ]; then
          echo "❌ High complexity detected:"
          echo "$COMPLEX_FILES"
          exit 1
        else
          echo "✅ Complexity check passed"
        fi
        
    - name: 🧹 Dead Code Detection
      run: |
        echo "## Dead Code Analysis" > dead-code-report.md
        vulture ccw/ --min-confidence 80 >> dead-code-report.md || true
        
        # Check for significant dead code
        DEAD_CODE_COUNT=$(vulture ccw/ --min-confidence 80 | wc -l)
        if [ $DEAD_CODE_COUNT -gt 10 ]; then
          echo "⚠️ Significant dead code detected: $DEAD_CODE_COUNT items"
          echo "Review dead-code-report.md for details"
        else
          echo "✅ Dead code check passed"
        fi
        
    - name: 🔍 Code Duplication Analysis
      run: |
        python -c "
        import ast
        import os
        from collections import defaultdict
        
        def get_function_signatures(file_path):
            try:
                with open(file_path, 'r') as f:
                    tree = ast.parse(f.read())
                
                signatures = []
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # Simple signature: name + arg count
                        sig = f'{node.name}_{len(node.args.args)}'
                        signatures.append((sig, file_path, node.lineno))
                return signatures
            except:
                return []
        
        # Collect all function signatures
        all_signatures = defaultdict(list)
        for root, dirs, files in os.walk('ccw'):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    signatures = get_function_signatures(file_path)
                    for sig, path, line in signatures:
                        all_signatures[sig].append((path, line))
        
        # Find duplicates
        duplicates = {sig: locations for sig, locations in all_signatures.items() if len(locations) > 1}
        
        if duplicates:
            print('## Code Duplication Report')
            for sig, locations in duplicates.items():
                print(f'### Duplicate: {sig}')
                for path, line in locations:
                    print(f'  - {path}:{line}')
            
            if len(duplicates) > ${{ env.DUPLICATION_THRESHOLD }}:
                print(f'❌ High duplication detected: {len(duplicates)} duplicates')
                exit(1)
            else:
                print(f'⚠️ Some duplication detected: {len(duplicates)} duplicates')
        else:
            print('✅ No significant duplication detected')
        "
        
    - name: 📤 Upload Quality Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: quality-reports
        path: |
          complexity-report.md
          dead-code-report.md

  # Security Quality Gates
  security-quality:
    name: 🔒 Security Quality Gates
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Security Tools
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit safety semgrep
        
    - name: 🛡️ Security Vulnerability Scan
      run: |
        echo "## Security Analysis Report" > security-report.md
        
        echo "### Dependency Vulnerabilities" >> security-report.md
        safety check --json >> safety-report.json || true
        
        echo "### Code Security Issues" >> security-report.md
        bandit -r ccw/ -f json -o bandit-report.json || true
        
        # Check for high severity issues
        HIGH_SEVERITY=$(python -c "
        import json
        try:
            with open('bandit-report.json') as f:
                data = json.load(f)
            high_severity = [r for r in data.get('results', []) if r.get('issue_severity') == 'HIGH']
            print(len(high_severity))
        except:
            print(0)
        ")
        
        if [ $HIGH_SEVERITY -gt 0 ]; then
          echo "❌ High severity security issues detected: $HIGH_SEVERITY"
          exit 1
        else
          echo "✅ Security scan passed"
        fi
        
    - name: 🔐 Secrets Detection
      run: |
        # Simple secrets detection
        SECRETS_FOUND=$(grep -r -i -E "(api_key|password|secret|token)" ccw/ --include="*.py" | grep -v "test" | wc -l)
        
        if [ $SECRETS_FOUND -gt 0 ]; then
          echo "⚠️ Potential secrets detected in code"
          echo "Review code for hardcoded credentials"
          grep -r -i -E "(api_key|password|secret|token)" ccw/ --include="*.py" | grep -v "test" || true
        else
          echo "✅ No obvious secrets detected"
        fi
        
    - name: 📤 Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          security-report.md
          safety-report.json
          bandit-report.json

  # Test Quality Gates
  test-quality:
    name: 🧪 Test Quality Gates
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🧪 Test Coverage Analysis
      run: |
        python tests/run_tests.py --coverage --type all
        
        # Extract coverage percentage
        COVERAGE=$(python -c "
        import re
        try:
            with open('coverage.txt') as f:
                content = f.read()
            match = re.search(r'TOTAL.*?(\d+)%', content)
            if match:
                print(match.group(1))
            else:
                print(0)
        except:
            print(0)
        ")
        
        echo "Coverage: $COVERAGE%"
        
        if [ $COVERAGE -lt ${{ env.COVERAGE_THRESHOLD }} ]; then
          echo "❌ Coverage below threshold: $COVERAGE% < ${{ env.COVERAGE_THRESHOLD }}%"
          exit 1
        else
          echo "✅ Coverage check passed: $COVERAGE%"
        fi
        
    - name: 🎯 Test Quality Metrics
      run: |
        python -c "
        import os
        import ast
        from pathlib import Path
        
        def analyze_test_quality():
            test_files = list(Path('tests').glob('**/*.py'))
            test_files = [f for f in test_files if f.name.startswith('test_')]
            
            total_tests = 0
            total_assertions = 0
            files_with_docstrings = 0
            
            for test_file in test_files:
                try:
                    with open(test_file) as f:
                        tree = ast.parse(f.read())
                    
                    file_has_docstring = False
                    file_tests = 0
                    file_assertions = 0
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                            file_tests += 1
                            
                            # Check for docstring
                            if (node.body and isinstance(node.body[0], ast.Expr) and 
                                isinstance(node.body[0].value, ast.Constant) and 
                                isinstance(node.body[0].value.value, str)):
                                file_has_docstring = True
                            
                            # Count assertions (simplified)
                            for child in ast.walk(node):
                                if (isinstance(child, ast.Call) and 
                                    isinstance(child.func, ast.Name) and 
                                    child.func.id.startswith('assert')):
                                    file_assertions += 1
                    
                    total_tests += file_tests
                    total_assertions += file_assertions
                    if file_has_docstring:
                        files_with_docstrings += 1
                        
                except Exception as e:
                    print(f'Error analyzing {test_file}: {e}')
            
            print(f'Test Quality Metrics:')
            print(f'  Total test functions: {total_tests}')
            print(f'  Total assertions: {total_assertions}')
            print(f'  Avg assertions per test: {total_assertions / total_tests if total_tests > 0 else 0:.1f}')
            print(f'  Files with docstrings: {files_with_docstrings}/{len(test_files)} ({files_with_docstrings/len(test_files)*100:.1f}%)')
            
            # Quality thresholds
            if total_assertions / total_tests < 2 if total_tests > 0 else False:
                print('⚠️ Low assertion density in tests')
            
            if files_with_docstrings / len(test_files) < 0.5:
                print('⚠️ Low test documentation coverage')
            
            print('✅ Test quality analysis completed')
        
        analyze_test_quality()
        "

  # Documentation Quality Gates
  docs-quality:
    name: 📚 Documentation Quality Gates
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pydocstyle
        
    - name: 📚 Docstring Quality Check
      run: |
        echo "## Documentation Quality Report" > docs-report.md
        
        # Check docstring coverage
        python -c "
        import ast
        import os
        from pathlib import Path
        
        def check_docstring_coverage():
            py_files = list(Path('ccw').glob('**/*.py'))
            
            total_functions = 0
            documented_functions = 0
            total_classes = 0
            documented_classes = 0
            
            for py_file in py_files:
                try:
                    with open(py_file) as f:
                        tree = ast.parse(f.read())
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            total_functions += 1
                            if (node.body and isinstance(node.body[0], ast.Expr) and 
                                isinstance(node.body[0].value, ast.Constant) and 
                                isinstance(node.body[0].value.value, str)):
                                documented_functions += 1
                        
                        elif isinstance(node, ast.ClassDef):
                            total_classes += 1
                            if (node.body and isinstance(node.body[0], ast.Expr) and 
                                isinstance(node.body[0].value, ast.Constant) and 
                                isinstance(node.body[0].value.value, str)):
                                documented_classes += 1
                
                except Exception as e:
                    print(f'Error analyzing {py_file}: {e}')
            
            func_coverage = documented_functions / total_functions * 100 if total_functions > 0 else 0
            class_coverage = documented_classes / total_classes * 100 if total_classes > 0 else 0
            
            print(f'Documentation Coverage:')
            print(f'  Functions: {documented_functions}/{total_functions} ({func_coverage:.1f}%)')
            print(f'  Classes: {documented_classes}/{total_classes} ({class_coverage:.1f}%)')
            
            if func_coverage < 70:
                print('❌ Function documentation coverage below 70%')
                exit(1)
            
            if class_coverage < 80:
                print('❌ Class documentation coverage below 80%')
                exit(1)
            
            print('✅ Documentation coverage check passed')
        
        check_docstring_coverage()
        "
        
    - name: 📖 README and Documentation Check
      run: |
        # Check for essential documentation files
        REQUIRED_DOCS=("README.md" "TESTING_FRAMEWORK_SUMMARY.md" "CODE_ANALYSIS_SUMMARY.md")
        
        for doc in "${REQUIRED_DOCS[@]}"; do
          if [ ! -f "$doc" ]; then
            echo "❌ Missing required documentation: $doc"
            exit 1
          else
            echo "✅ Found: $doc"
          fi
        done
        
        # Check README quality
        README_LINES=$(wc -l < README.md)
        if [ $README_LINES -lt 50 ]; then
          echo "⚠️ README.md seems too short ($README_LINES lines)"
        else
          echo "✅ README.md has adequate content"
        fi

  # Quality Gates Summary
  quality-summary:
    name: 📋 Quality Gates Summary
    runs-on: ubuntu-latest
    needs: [code-quality, security-quality, test-quality, docs-quality]
    if: always()
    timeout-minutes: 5
    
    steps:
    - name: 📊 Collect Quality Results
      run: |
        echo "## Quality Gates Summary" > quality-summary.md
        echo "**Timestamp**: $(date)" >> quality-summary.md
        echo "**Commit**: ${{ github.sha }}" >> quality-summary.md
        echo "" >> quality-summary.md
        echo "### Gate Results:" >> quality-summary.md
        echo "- Code Quality: ${{ needs.code-quality.result }}" >> quality-summary.md
        echo "- Security Quality: ${{ needs.security-quality.result }}" >> quality-summary.md
        echo "- Test Quality: ${{ needs.test-quality.result }}" >> quality-summary.md
        echo "- Documentation Quality: ${{ needs.docs-quality.result }}" >> quality-summary.md
        
        # Overall status
        if [[ "${{ needs.code-quality.result }}" == "success" && 
              "${{ needs.security-quality.result }}" == "success" && 
              "${{ needs.test-quality.result }}" == "success" && 
              "${{ needs.docs-quality.result }}" == "success" ]]; then
          echo "" >> quality-summary.md
          echo "🎉 **ALL QUALITY GATES PASSED**" >> quality-summary.md
          echo "✅ Code is ready for merge/deployment"
        else
          echo "" >> quality-summary.md
          echo "❌ **QUALITY GATES FAILED**" >> quality-summary.md
          echo "🚫 Code needs improvement before merge"
          exit 1
        fi
        
    - name: 📤 Upload Quality Summary
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: quality-summary
        path: quality-summary.md
        
    - name: ✅ Quality Gates Passed
      if: needs.code-quality.result == 'success' && needs.security-quality.result == 'success' && needs.test-quality.result == 'success' && needs.docs-quality.result == 'success'
      run: |
        echo "🎉 All quality gates passed!"
        echo "✅ Code Quality: PASSED"
        echo "✅ Security Quality: PASSED" 
        echo "✅ Test Quality: PASSED"
        echo "✅ Documentation Quality: PASSED"
