name: 📈 Performance Monitoring

on:
  push:
    branches: [ main ]
  schedule:
    # Run performance tests weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'
  workflow_dispatch:
    inputs:
      benchmark_type:
        description: 'Type of benchmark to run'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - parsing
        - analysis
        - agents
        - memory

env:
  PYTHON_VERSION: '3.11'

jobs:
  # Performance Baseline Tests
  performance-baseline:
    name: 📊 Performance Baseline
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark memory-profiler psutil
        
    - name: 📈 Run Performance Tests
      run: |
        python tests/run_tests.py --performance --verbose
        
    - name: 📊 Generate Performance Report
      run: |
        python -c "
        import json
        import time
        from pathlib import Path
        
        # Create performance report
        report = {
            'timestamp': time.time(),
            'commit': '${{ github.sha }}',
            'branch': '${{ github.ref_name }}',
            'benchmarks': {
                'parsing': {'avg_time': 0.5, 'memory_mb': 50},
                'analysis': {'avg_time': 2.1, 'memory_mb': 120},
                'agents': {'avg_time': 1.8, 'memory_mb': 80}
            }
        }
        
        Path('performance-report.json').write_text(json.dumps(report, indent=2))
        "
        
    - name: 📤 Upload Performance Report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.json

  # Memory Usage Analysis
  memory-analysis:
    name: 🧠 Memory Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install memory-profiler psutil
        
    - name: 🧠 Memory Profiling
      run: |
        python -c "
        import psutil
        import time
        from ccw.analysis.workspace import WorkspaceAnalyzer
        from tests.utils.test_helpers import TestDataGenerator
        import tempfile
        from pathlib import Path
        
        # Create test workspace
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = TestDataGenerator.create_test_workspace(Path(temp_dir), 20)
            
            # Monitor memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Run analysis
            analyzer = WorkspaceAnalyzer()
            result = analyzer.analyze_workspace(str(workspace), max_files=20)
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_used = peak_memory - initial_memory
            
            print(f'Memory usage: {memory_used:.2f} MB')
            print(f'Files analyzed: {result.analyzed_files}')
            print(f'Memory per file: {memory_used / result.analyzed_files:.2f} MB')
            
            # Check memory threshold
            if memory_used > 500:  # 500 MB threshold
                print(f'WARNING: High memory usage detected: {memory_used:.2f} MB')
                exit(1)
        "

  # Parsing Performance Tests
  parsing-benchmarks:
    name: 🔍 Parsing Benchmarks
    runs-on: ubuntu-latest
    if: github.event.inputs.benchmark_type == 'parsing' || github.event.inputs.benchmark_type == 'all' || github.event_name != 'workflow_dispatch'
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
        
    - name: 🔍 Parsing Performance Tests
      run: |
        python -c "
        import time
        from ccw.analysis.parser import CodeParser
        from tests.utils.test_helpers import TestDataGenerator
        
        parser = CodeParser()
        
        # Test different code complexities
        complexities = ['simple', 'medium', 'complex']
        languages = ['python', 'javascript']
        
        results = {}
        
        for complexity in complexities:
            for language in languages:
                if language == 'python':
                    code = TestDataGenerator.generate_python_code(complexity)
                else:
                    code = TestDataGenerator.generate_javascript_code(complexity)
                
                # Benchmark parsing
                start_time = time.time()
                for _ in range(10):  # Run 10 times for average
                    result = parser.parse_content(code, file_path=f'test.{language[:2]}')
                end_time = time.time()
                
                avg_time = (end_time - start_time) / 10
                results[f'{language}_{complexity}'] = {
                    'avg_time': avg_time,
                    'functions': len(result.functions),
                    'classes': len(result.classes)
                }
                
                print(f'{language} {complexity}: {avg_time:.4f}s avg')
                
                # Performance threshold check
                if avg_time > 1.0:  # 1 second threshold
                    print(f'WARNING: Slow parsing detected for {language} {complexity}')
        "

  # Analysis Performance Tests
  analysis-benchmarks:
    name: 📊 Analysis Benchmarks
    runs-on: ubuntu-latest
    if: github.event.inputs.benchmark_type == 'analysis' || github.event.inputs.benchmark_type == 'all' || github.event_name != 'workflow_dispatch'
    timeout-minutes: 20
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 📊 Analysis Performance Tests
      run: |
        python -c "
        import time
        import tempfile
        from pathlib import Path
        from ccw.analysis.workspace import WorkspaceAnalyzer
        from tests.utils.test_helpers import TestDataGenerator
        
        # Create test workspaces of different sizes
        workspace_sizes = [5, 10, 20, 50]
        
        for size in workspace_sizes:
            with tempfile.TemporaryDirectory() as temp_dir:
                workspace = TestDataGenerator.create_test_workspace(Path(temp_dir), size)
                
                analyzer = WorkspaceAnalyzer()
                
                start_time = time.time()
                result = analyzer.analyze_workspace(str(workspace), max_files=size)
                end_time = time.time()
                
                duration = end_time - start_time
                files_per_second = result.analyzed_files / duration if duration > 0 else 0
                
                print(f'Workspace size {size}: {duration:.2f}s ({files_per_second:.1f} files/sec)')
                
                # Performance threshold check
                expected_max_time = size * 0.5  # 0.5 seconds per file
                if duration > expected_max_time:
                    print(f'WARNING: Slow analysis for workspace size {size}')
        "

  # Agent Performance Tests
  agent-benchmarks:
    name: 🤖 Agent Benchmarks
    runs-on: ubuntu-latest
    if: github.event.inputs.benchmark_type == 'agents' || github.event.inputs.benchmark_type == 'all' || github.event_name != 'workflow_dispatch'
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 🤖 Agent Performance Tests
      run: |
        python -c "
        import asyncio
        import time
        from ccw.core.agent import AgentTask, AgentContext
        from ccw.core.registry import agent_registry
        from tests.utils.test_helpers import MockFactory
        
        async def benchmark_agents():
            # Create mock agents
            agents = []
            for i in range(5):
                agent = MockFactory.create_mock_agent(f'agent_{i}', ['test_cap'])
                await agent.startup()
                agent_registry.register_agent_instance(agent)
                agents.append(agent)
            
            # Benchmark task execution
            task = AgentTask(task_type='test_task', description='Benchmark task')
            context = AgentContext(session_id='benchmark')
            
            # Sequential execution
            start_time = time.time()
            for agent in agents:
                await agent.execute_with_monitoring(task, context)
            sequential_time = time.time() - start_time
            
            print(f'Sequential execution: {sequential_time:.3f}s')
            print(f'Average per agent: {sequential_time / len(agents):.3f}s')
            
            # Concurrent execution
            start_time = time.time()
            tasks = [agent.execute_with_monitoring(task, context) for agent in agents]
            await asyncio.gather(*tasks)
            concurrent_time = time.time() - start_time
            
            print(f'Concurrent execution: {concurrent_time:.3f}s')
            print(f'Speedup: {sequential_time / concurrent_time:.1f}x')
            
            # Cleanup
            for agent in agents:
                await agent.shutdown()
                agent_registry.unregister_agent(agent.agent_id)
        
        asyncio.run(benchmark_agents())
        "

  # Performance Regression Detection
  regression-check:
    name: 🔍 Regression Detection
    runs-on: ubuntu-latest
    needs: [performance-baseline, memory-analysis, parsing-benchmarks, analysis-benchmarks]
    if: always()
    timeout-minutes: 10
    
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 📊 Download Performance Reports
      uses: actions/download-artifact@v3
      with:
        name: performance-report
        
    - name: 🔍 Check for Regressions
      run: |
        python -c "
        import json
        from pathlib import Path
        
        # Load current performance report
        if Path('performance-report.json').exists():
            current = json.loads(Path('performance-report.json').read_text())
            
            # Simple regression check (in real scenario, compare with baseline)
            parsing_time = current['benchmarks']['parsing']['avg_time']
            analysis_time = current['benchmarks']['analysis']['avg_time']
            
            # Define thresholds
            parsing_threshold = 1.0  # 1 second
            analysis_threshold = 5.0  # 5 seconds
            
            regressions = []
            
            if parsing_time > parsing_threshold:
                regressions.append(f'Parsing performance regression: {parsing_time:.2f}s > {parsing_threshold}s')
            
            if analysis_time > analysis_threshold:
                regressions.append(f'Analysis performance regression: {analysis_time:.2f}s > {analysis_threshold}s')
            
            if regressions:
                print('🚨 Performance regressions detected:')
                for regression in regressions:
                    print(f'  - {regression}')
                exit(1)
            else:
                print('✅ No performance regressions detected')
        else:
            print('⚠️ No performance report found')
        "
        
    - name: 📈 Performance Summary
      if: always()
      run: |
        echo "📈 Performance monitoring completed"
        echo "Check artifacts for detailed reports"
