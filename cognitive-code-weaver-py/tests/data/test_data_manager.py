"""
Advanced Test Data Management System

Provides sophisticated test data generation, caching, versioning,
and management for comprehensive testing scenarios.
"""

import json
import hashlib
import pickle
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import tempfile
import shutil

from tests.utils.test_helpers import TestDataGenerator


class DataComplexity(Enum):
    """Test data complexity levels"""
    MINIMAL = "minimal"
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    ENTERPRISE = "enterprise"


class DataCategory(Enum):
    """Test data categories"""
    CODE_SAMPLES = "code_samples"
    WORKSPACES = "workspaces"
    PARSE_RESULTS = "parse_results"
    ANALYSIS_DATA = "analysis_data"
    AGENT_SCENARIOS = "agent_scenarios"
    PERFORMANCE_DATA = "performance_data"


@dataclass
class TestDataSpec:
    """Specification for test data generation"""
    category: DataCategory
    complexity: DataComplexity
    language: Optional[str] = None
    file_count: Optional[int] = None
    size_mb: Optional[float] = None
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}
    
    @property
    def cache_key(self) -> str:
        """Generate cache key for this data spec"""
        spec_dict = asdict(self)
        spec_str = json.dumps(spec_dict, sort_keys=True)
        return hashlib.md5(spec_str.encode()).hexdigest()


@dataclass
class TestDataItem:
    """Container for test data with metadata"""
    spec: TestDataSpec
    data: Any
    created_at: float
    size_bytes: int
    checksum: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class TestDataCache:
    """Intelligent caching system for test data"""
    
    def __init__(self, cache_dir: Optional[Path] = None, max_size_mb: float = 500):
        self.cache_dir = cache_dir or Path.cwd() / ".test_data_cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_mb = max_size_mb
        self.index_file = self.cache_dir / "index.json"
        self._load_index()
    
    def _load_index(self):
        """Load cache index"""
        if self.index_file.exists():
            with open(self.index_file) as f:
                self.index = json.load(f)
        else:
            self.index = {}
    
    def _save_index(self):
        """Save cache index"""
        with open(self.index_file, 'w') as f:
            json.dump(self.index, f, indent=2)
    
    def _cleanup_cache(self):
        """Remove old cache entries if size limit exceeded"""
        total_size = sum(entry["size_bytes"] for entry in self.index.values())
        max_size_bytes = self.max_size_mb * 1024 * 1024
        
        if total_size > max_size_bytes:
            # Sort by access time (LRU)
            sorted_entries = sorted(
                self.index.items(),
                key=lambda x: x[1].get("last_accessed", 0)
            )
            
            # Remove oldest entries
            for cache_key, entry in sorted_entries:
                cache_file = self.cache_dir / f"{cache_key}.pkl"
                if cache_file.exists():
                    cache_file.unlink()
                del self.index[cache_key]
                total_size -= entry["size_bytes"]
                
                if total_size <= max_size_bytes * 0.8:  # Leave some headroom
                    break
            
            self._save_index()
    
    def get(self, spec: TestDataSpec) -> Optional[TestDataItem]:
        """Get cached test data"""
        cache_key = spec.cache_key
        
        if cache_key not in self.index:
            return None
        
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        if not cache_file.exists():
            # Remove stale index entry
            del self.index[cache_key]
            self._save_index()
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                data_item = pickle.load(f)
            
            # Update access time
            self.index[cache_key]["last_accessed"] = time.time()
            self._save_index()
            
            return data_item
        except Exception:
            # Remove corrupted cache entry
            cache_file.unlink()
            del self.index[cache_key]
            self._save_index()
            return None
    
    def put(self, data_item: TestDataItem):
        """Store test data in cache"""
        cache_key = data_item.spec.cache_key
        cache_file = self.cache_dir / f"{cache_key}.pkl"
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(data_item, f)
            
            self.index[cache_key] = {
                "created_at": data_item.created_at,
                "last_accessed": time.time(),
                "size_bytes": data_item.size_bytes,
                "checksum": data_item.checksum,
                "spec": asdict(data_item.spec)
            }
            
            self._save_index()
            self._cleanup_cache()
            
        except Exception as e:
            # Clean up on failure
            if cache_file.exists():
                cache_file.unlink()
            raise e
    
    def clear(self):
        """Clear all cached data"""
        for cache_file in self.cache_dir.glob("*.pkl"):
            cache_file.unlink()
        self.index = {}
        self._save_index()
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_size = sum(entry["size_bytes"] for entry in self.index.values())
        return {
            "entries": len(self.index),
            "total_size_mb": total_size / 1024 / 1024,
            "max_size_mb": self.max_size_mb,
            "utilization": (total_size / (self.max_size_mb * 1024 * 1024)) * 100
        }


class TestDataManager:
    """Advanced test data management system"""
    
    def __init__(self, cache_dir: Optional[Path] = None):
        self.cache = TestDataCache(cache_dir)
        self.generators = self._initialize_generators()
    
    def _initialize_generators(self) -> Dict[DataCategory, Callable]:
        """Initialize data generators for each category"""
        return {
            DataCategory.CODE_SAMPLES: self._generate_code_samples,
            DataCategory.WORKSPACES: self._generate_workspaces,
            DataCategory.PARSE_RESULTS: self._generate_parse_results,
            DataCategory.ANALYSIS_DATA: self._generate_analysis_data,
            DataCategory.AGENT_SCENARIOS: self._generate_agent_scenarios,
            DataCategory.PERFORMANCE_DATA: self._generate_performance_data
        }
    
    def get_data(self, spec: TestDataSpec, force_regenerate: bool = False) -> TestDataItem:
        """Get test data, generating if not cached"""
        if not force_regenerate:
            cached_item = self.cache.get(spec)
            if cached_item:
                return cached_item
        
        # Generate new data
        generator = self.generators.get(spec.category)
        if not generator:
            raise ValueError(f"No generator for category: {spec.category}")
        
        data = generator(spec)
        
        # Create data item with metadata
        data_bytes = self._calculate_size(data)
        checksum = self._calculate_checksum(data)
        
        data_item = TestDataItem(
            spec=spec,
            data=data,
            created_at=time.time(),
            size_bytes=data_bytes,
            checksum=checksum,
            metadata={"generator_version": "1.0"}
        )
        
        # Cache the data
        self.cache.put(data_item)
        
        return data_item
    
    def _calculate_size(self, data: Any) -> int:
        """Calculate approximate size of data in bytes"""
        try:
            return len(pickle.dumps(data))
        except:
            return len(str(data).encode())
    
    def _calculate_checksum(self, data: Any) -> str:
        """Calculate checksum for data integrity"""
        try:
            data_bytes = pickle.dumps(data)
        except:
            data_bytes = str(data).encode()
        return hashlib.sha256(data_bytes).hexdigest()[:16]
    
    def _generate_code_samples(self, spec: TestDataSpec) -> Dict[str, str]:
        """Generate code samples"""
        language = spec.language or "python"
        complexity = spec.complexity.value
        
        samples = {}
        
        if language == "python":
            samples["main"] = TestDataGenerator.generate_python_code(complexity)
            if spec.complexity in [DataComplexity.COMPLEX, DataComplexity.ENTERPRISE]:
                samples["utils"] = TestDataGenerator.generate_python_code("medium")
                samples["tests"] = self._generate_test_code(language, complexity)
        
        elif language == "javascript":
            samples["main"] = TestDataGenerator.generate_javascript_code(complexity)
            if spec.complexity in [DataComplexity.COMPLEX, DataComplexity.ENTERPRISE]:
                samples["components"] = TestDataGenerator.generate_javascript_code("medium")
                samples["tests"] = self._generate_test_code(language, complexity)
        
        return samples
    
    def _generate_workspaces(self, spec: TestDataSpec) -> Path:
        """Generate test workspaces"""
        file_count = spec.file_count or self._get_default_file_count(spec.complexity)
        
        temp_dir = Path(tempfile.mkdtemp(prefix="test_workspace_"))
        workspace = TestDataGenerator.create_test_workspace(temp_dir, file_count)
        
        # Add complexity-specific files
        if spec.complexity == DataComplexity.ENTERPRISE:
            self._add_enterprise_files(workspace)
        
        return workspace
    
    def _generate_parse_results(self, spec: TestDataSpec) -> List[Dict[str, Any]]:
        """Generate parse results data"""
        from ccw.analysis.parser import CodeParser
        
        parser = CodeParser()
        results = []
        
        # Generate code samples and parse them
        code_spec = TestDataSpec(
            category=DataCategory.CODE_SAMPLES,
            complexity=spec.complexity,
            language=spec.language
        )
        
        code_samples = self._generate_code_samples(code_spec)
        
        for name, code in code_samples.items():
            result = parser.parse_content(code, file_path=f"{name}.py")
            results.append({
                "name": name,
                "parse_result": result,
                "metadata": {"complexity": spec.complexity.value}
            })
        
        return results
    
    def _generate_analysis_data(self, spec: TestDataSpec) -> Dict[str, Any]:
        """Generate analysis data"""
        # Generate workspace and analyze it
        workspace_spec = TestDataSpec(
            category=DataCategory.WORKSPACES,
            complexity=spec.complexity,
            file_count=spec.file_count
        )
        
        workspace = self._generate_workspaces(workspace_spec)
        
        # Simulate analysis results
        analysis_data = {
            "workspace_path": str(workspace),
            "metrics": {
                "total_files": len(list(workspace.glob("**/*.py"))),
                "total_lines": 1000 * (1 + spec.complexity.value.count('e')),  # Rough estimate
                "complexity_score": 5.0 + spec.complexity.value.count('e') * 2.5
            },
            "dependencies": self._generate_dependency_data(spec),
            "symbols": self._generate_symbol_data(spec)
        }
        
        return analysis_data
    
    def _generate_agent_scenarios(self, spec: TestDataSpec) -> List[Dict[str, Any]]:
        """Generate agent test scenarios"""
        scenarios = []
        
        complexity_scenarios = {
            DataComplexity.SIMPLE: 3,
            DataComplexity.MEDIUM: 5,
            DataComplexity.COMPLEX: 8,
            DataComplexity.ENTERPRISE: 12
        }
        
        scenario_count = complexity_scenarios.get(spec.complexity, 5)
        
        for i in range(scenario_count):
            scenario = {
                "id": f"scenario_{i}",
                "description": f"Test scenario {i} for {spec.complexity.value} complexity",
                "task_type": "code_analysis",
                "input_data": {
                    "code": TestDataGenerator.generate_python_code(spec.complexity.value),
                    "workspace_path": "/test/workspace"
                },
                "expected_output": {
                    "status": "completed",
                    "confidence": 0.8 + (i * 0.02),
                    "analysis_type": "structural"
                },
                "metadata": {
                    "complexity": spec.complexity.value,
                    "estimated_duration": 1.0 + (i * 0.5)
                }
            }
            scenarios.append(scenario)
        
        return scenarios
    
    def _generate_performance_data(self, spec: TestDataSpec) -> Dict[str, Any]:
        """Generate performance test data"""
        complexity_multiplier = {
            DataComplexity.SIMPLE: 1,
            DataComplexity.MEDIUM: 3,
            DataComplexity.COMPLEX: 8,
            DataComplexity.ENTERPRISE: 20
        }
        
        multiplier = complexity_multiplier.get(spec.complexity, 1)
        
        return {
            "parsing_benchmarks": {
                "avg_time_ms": 50 * multiplier,
                "memory_mb": 10 * multiplier,
                "files_per_second": 100 / multiplier
            },
            "analysis_benchmarks": {
                "avg_time_ms": 200 * multiplier,
                "memory_mb": 50 * multiplier,
                "complexity_calculation_ms": 30 * multiplier
            },
            "agent_benchmarks": {
                "task_execution_ms": 500 * multiplier,
                "llm_calls": 2 * multiplier,
                "memory_mb": 25 * multiplier
            },
            "thresholds": {
                "max_parsing_time_ms": 1000,
                "max_memory_mb": 500,
                "min_files_per_second": 10
            }
        }
    
    def _generate_test_code(self, language: str, complexity: str) -> str:
        """Generate test code for given language and complexity"""
        if language == "python":
            return f'''
import pytest
from unittest.mock import Mock, patch

class Test{complexity.title()}Functionality:
    """Test suite for {complexity} functionality"""
    
    def test_basic_operation(self):
        """Test basic operation"""
        assert True
    
    def test_error_handling(self):
        """Test error handling"""
        with pytest.raises(ValueError):
            raise ValueError("Test error")
    
    @pytest.mark.parametrize("input_val,expected", [
        (1, 2), (2, 4), (3, 6)
    ])
    def test_parametrized(self, input_val, expected):
        """Parametrized test"""
        assert input_val * 2 == expected
'''
        else:
            return f'''
describe('{complexity.title()} Functionality', () => {{
    test('basic operation', () => {{
        expect(true).toBe(true);
    }});
    
    test('error handling', () => {{
        expect(() => {{
            throw new Error('Test error');
        }}).toThrow('Test error');
    }});
}});
'''
    
    def _get_default_file_count(self, complexity: DataComplexity) -> int:
        """Get default file count for complexity level"""
        return {
            DataComplexity.MINIMAL: 2,
            DataComplexity.SIMPLE: 5,
            DataComplexity.MEDIUM: 15,
            DataComplexity.COMPLEX: 40,
            DataComplexity.ENTERPRISE: 100
        }.get(complexity, 10)
    
    def _add_enterprise_files(self, workspace: Path):
        """Add enterprise-specific files to workspace"""
        # Add configuration files
        (workspace / "pyproject.toml").write_text("""
[tool.poetry]
name = "enterprise-project"
version = "1.0.0"
description = "Enterprise test project"

[tool.poetry.dependencies]
python = "^3.9"
""")
        
        # Add CI configuration
        ci_dir = workspace / ".github" / "workflows"
        ci_dir.mkdir(parents=True)
        (ci_dir / "ci.yml").write_text("name: CI\non: [push, pull_request]")
        
        # Add documentation
        docs_dir = workspace / "docs"
        docs_dir.mkdir()
        (docs_dir / "README.md").write_text("# Enterprise Project Documentation")
    
    def _generate_dependency_data(self, spec: TestDataSpec) -> List[Dict[str, Any]]:
        """Generate dependency graph data"""
        file_count = spec.file_count or self._get_default_file_count(spec.complexity)
        dependencies = []
        
        for i in range(min(file_count, 20)):  # Limit for performance
            dep = {
                "source": f"module_{i}.py",
                "target": f"module_{(i + 1) % file_count}.py",
                "type": "import",
                "strength": 0.5 + (i * 0.1) % 0.5
            }
            dependencies.append(dep)
        
        return dependencies
    
    def _generate_symbol_data(self, spec: TestDataSpec) -> List[Dict[str, Any]]:
        """Generate symbol data"""
        symbols = []
        complexity_factor = len(spec.complexity.value)
        
        for i in range(complexity_factor * 5):
            symbol = {
                "name": f"symbol_{i}",
                "type": "function" if i % 2 == 0 else "class",
                "file": f"module_{i % 3}.py",
                "line": 10 + i * 5,
                "complexity": 1 + (i % 10)
            }
            symbols.append(symbol)
        
        return symbols
    
    def cleanup_temp_data(self):
        """Clean up temporary data"""
        # Clean up any temporary workspaces
        temp_dirs = []
        for cache_key, entry in self.cache.index.items():
            cached_item = self.cache.get(TestDataSpec(
                category=DataCategory(entry["spec"]["category"]),
                complexity=DataComplexity(entry["spec"]["complexity"])
            ))
            if cached_item and isinstance(cached_item.data, Path):
                if cached_item.data.name.startswith("test_workspace_"):
                    temp_dirs.append(cached_item.data)
        
        for temp_dir in temp_dirs:
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return self.cache.stats()


# Global test data manager instance
test_data_manager = TestDataManager()
