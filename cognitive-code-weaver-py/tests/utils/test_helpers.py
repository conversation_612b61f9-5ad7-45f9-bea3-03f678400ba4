"""
Test utilities and helpers for Cognitive Code Weaver tests

Provides common utilities, mock factories, assertion helpers,
and test data generators for use across all test modules.
"""

import asyncio
import json
import tempfile
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable
from unittest.mock import AsyncMock, MagicMock
from dataclasses import dataclass

from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus
from ccw.llm.providers.base import LLMResponse, LLMMessage, MessageRole
from ccw.analysis.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, LanguageType


@dataclass
class TestMetrics:
    """Test execution metrics"""
    start_time: float
    end_time: float
    duration: float
    assertions_passed: int
    assertions_failed: int
    
    @property
    def success_rate(self) -> float:
        total = self.assertions_passed + self.assertions_failed
        return self.assertions_passed / total if total > 0 else 0.0


class MockFactory:
    """Factory for creating mock objects"""
    
    @staticmethod
    def create_mock_agent(agent_id: str, capabilities: List[str], 
                         should_fail: bool = False) -> Agent:
        """Create a mock agent for testing"""
        class MockAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = agent_id
                self.test_capabilities = capabilities
                self.should_fail = should_fail
                self.executed_tasks = []
            
            def _define_capabilities(self):
                return self.test_capabilities
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                self.executed_tasks.append(task)
                
                if self.should_fail:
                    return AgentResult(
                        agent_id=self.agent_id,
                        task_id=task.id,
                        status=AgentStatus.FAILED,
                        error="Mock agent failure"
                    )
                else:
                    return AgentResult(
                        agent_id=self.agent_id,
                        task_id=task.id,
                        status=AgentStatus.COMPLETED,
                        data={"mock_result": "success", "task_type": task.task_type}
                    )
        
        return MockAgent()
    
    @staticmethod
    def create_mock_llm_response(content: str, model: str = "test-model",
                                provider: str = "test-provider") -> LLMResponse:
        """Create a mock LLM response"""
        return LLMResponse(
            content=content,
            model=model,
            provider=provider,
            usage={"total_tokens": len(content.split())},
            response_time=0.1,
            metadata={"cost": 0.001}
        )
    
    @staticmethod
    def create_mock_llm_client() -> MagicMock:
        """Create a mock LLM client"""
        mock_client = MagicMock()
        
        # Default response
        default_response = MockFactory.create_mock_llm_response(
            "This is a mock LLM response for testing purposes."
        )
        
        mock_client.generate_response = AsyncMock(return_value=default_response)
        mock_client.generate_stream = AsyncMock()
        mock_client.generate_embeddings = AsyncMock(return_value=[[0.1, 0.2, 0.3]])
        mock_client.get_available_providers = MagicMock(return_value=["test-provider"])
        mock_client.validate_providers = AsyncMock(return_value={"test-provider": True})
        mock_client.get_provider_info = MagicMock(return_value={
            "name": "test-provider",
            "model": "test-model",
            "capabilities": ["text_generation"]
        })
        mock_client.get_usage_stats = MagicMock(return_value={
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0
        })
        
        return mock_client
    
    @staticmethod
    def create_mock_parse_result(file_path: str, language: LanguageType,
                                content: str = "") -> ParseResult:
        """Create a mock parse result"""
        return ParseResult(
            file_path=file_path,
            language=language,
            content=content,
            functions=[
                {"name": "test_function", "line": 1, "parameters": ["param1"]},
                {"name": "another_function", "line": 10, "parameters": []}
            ],
            classes=[
                {"name": "TestClass", "line": 5, "inheritance": None}
            ],
            imports=["os", "sys", "json"],
            variables=[
                {"name": "test_var", "line": 15, "value": "test_value"}
            ],
            errors=[]
        )


class TestDataGenerator:
    """Generates test data for various scenarios"""
    
    @staticmethod
    def generate_python_code(complexity: str = "medium") -> str:
        """Generate Python code of varying complexity"""
        if complexity == "simple":
            return '''
def simple_function():
    """A simple function"""
    return "Hello, World!"

x = 42
'''
        elif complexity == "medium":
            return '''
import os
import sys
from typing import List, Dict

class DataProcessor:
    """A data processing class"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.cache = {}
    
    def process_data(self, data: List[str]) -> List[str]:
        """Process a list of data items"""
        results = []
        for item in data:
            if item in self.cache:
                results.append(self.cache[item])
            else:
                processed = self._process_item(item)
                self.cache[item] = processed
                results.append(processed)
        return results
    
    def _process_item(self, item: str) -> str:
        """Process a single item"""
        if not item:
            return ""
        return item.upper().strip()

# Global variables
DEFAULT_CONFIG = {"timeout": 30, "retries": 3}
processor = DataProcessor(DEFAULT_CONFIG)
'''
        else:  # complex
            return '''
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum

class ProcessingMode(Enum):
    SYNC = "sync"
    ASYNC = "async"
    BATCH = "batch"

@dataclass
class ProcessingResult:
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class BaseProcessor(ABC):
    """Abstract base processor"""
    
    def __init__(self, mode: ProcessingMode = ProcessingMode.SYNC):
        self.mode = mode
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def process(self, data: Any) -> ProcessingResult:
        """Process data"""
        pass

class AdvancedProcessor(BaseProcessor):
    """Advanced data processor with multiple processing modes"""
    
    def __init__(self, mode: ProcessingMode = ProcessingMode.ASYNC):
        super().__init__(mode)
        self.processors: Dict[str, Callable] = {}
        self.middleware: List[Callable] = []
    
    async def process(self, data: Any) -> ProcessingResult:
        """Process data with middleware and error handling"""
        try:
            # Apply middleware
            for middleware in self.middleware:
                data = await self._apply_middleware(middleware, data)
            
            # Process based on mode
            if self.mode == ProcessingMode.ASYNC:
                result = await self._async_process(data)
            elif self.mode == ProcessingMode.BATCH:
                result = await self._batch_process(data)
            else:
                result = await self._sync_process(data)
            
            return ProcessingResult(success=True, data=result)
            
        except Exception as e:
            self.logger.error(f"Processing failed: {e}")
            return ProcessingResult(success=False, error=str(e))
    
    async def _apply_middleware(self, middleware: Callable, data: Any) -> Any:
        """Apply middleware function"""
        if asyncio.iscoroutinefunction(middleware):
            return await middleware(data)
        else:
            return middleware(data)
    
    async def _async_process(self, data: Any) -> Any:
        """Asynchronous processing"""
        tasks = []
        if isinstance(data, list):
            for item in data:
                task = asyncio.create_task(self._process_single_item(item))
                tasks.append(task)
            return await asyncio.gather(*tasks)
        else:
            return await self._process_single_item(data)
    
    async def _batch_process(self, data: Any) -> Any:
        """Batch processing"""
        batch_size = 10
        if isinstance(data, list):
            results = []
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                batch_results = await self._async_process(batch)
                results.extend(batch_results)
            return results
        else:
            return await self._process_single_item(data)
    
    async def _sync_process(self, data: Any) -> Any:
        """Synchronous processing"""
        if isinstance(data, list):
            return [await self._process_single_item(item) for item in data]
        else:
            return await self._process_single_item(data)
    
    async def _process_single_item(self, item: Any) -> Any:
        """Process a single item"""
        await asyncio.sleep(0.01)  # Simulate processing time
        return str(item).upper()

# Factory function
def create_processor(mode: str = "async") -> BaseProcessor:
    """Create a processor instance"""
    processing_mode = ProcessingMode(mode)
    return AdvancedProcessor(processing_mode)
'''
    
    @staticmethod
    def generate_javascript_code(complexity: str = "medium") -> str:
        """Generate JavaScript code of varying complexity"""
        if complexity == "simple":
            return '''
function greet(name) {
    return `Hello, ${name}!`;
}

const PI = 3.14159;
'''
        elif complexity == "medium":
            return '''
import { EventEmitter } from 'events';

class DataManager extends EventEmitter {
    constructor(options = {}) {
        super();
        this.data = new Map();
        this.options = { timeout: 5000, ...options };
    }
    
    async fetchData(key) {
        if (this.data.has(key)) {
            return this.data.get(key);
        }
        
        try {
            const result = await this.loadData(key);
            this.data.set(key, result);
            this.emit('dataLoaded', key, result);
            return result;
        } catch (error) {
            this.emit('error', error);
            throw error;
        }
    }
    
    async loadData(key) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(`Data for ${key}`);
            }, 100);
        });
    }
}

const manager = new DataManager();
'''
        else:  # complex
            return '''
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { createContext, useContext } from 'react';

const DataContext = createContext();

export const DataProvider = ({ children }) => {
    const [data, setData] = useState(new Map());
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    
    const fetchData = useCallback(async (key, options = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await fetch(`/api/data/${key}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            setData(prev => new Map(prev).set(key, result));
            return result;
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);
    
    const value = useMemo(() => ({
        data,
        loading,
        error,
        fetchData
    }), [data, loading, error, fetchData]);
    
    return (
        <DataContext.Provider value={value}>
            {children}
        </DataContext.Provider>
    );
};

export const useData = () => {
    const context = useContext(DataContext);
    if (!context) {
        throw new Error('useData must be used within DataProvider');
    }
    return context;
};
'''
    
    @staticmethod
    def create_test_workspace(temp_dir: Path, file_count: int = 5) -> Path:
        """Create a test workspace with multiple files"""
        workspace = temp_dir / "test_workspace"
        workspace.mkdir()
        
        # Create Python files
        for i in range(file_count // 2):
            py_file = workspace / f"module_{i}.py"
            py_file.write_text(TestDataGenerator.generate_python_code("medium"))
        
        # Create JavaScript files
        for i in range(file_count // 2):
            js_file = workspace / f"component_{i}.js"
            js_file.write_text(TestDataGenerator.generate_javascript_code("medium"))
        
        # Create subdirectory
        subdir = workspace / "utils"
        subdir.mkdir()
        utils_file = subdir / "helpers.py"
        utils_file.write_text(TestDataGenerator.generate_python_code("simple"))
        
        return workspace


class AssertionHelpers:
    """Custom assertion helpers for testing"""
    
    @staticmethod
    def assert_agent_result_valid(result: AgentResult, expected_status: AgentStatus = None):
        """Assert that an agent result is valid"""
        assert result is not None, "Agent result should not be None"
        assert hasattr(result, 'agent_id'), "Result should have agent_id"
        assert hasattr(result, 'task_id'), "Result should have task_id"
        assert hasattr(result, 'status'), "Result should have status"
        assert isinstance(result.status, AgentStatus), "Status should be AgentStatus enum"
        
        if expected_status:
            assert result.status == expected_status, f"Expected status {expected_status}, got {result.status}"
        
        if result.status == AgentStatus.FAILED:
            assert result.error is not None, "Failed result should have error message"
        
        if result.status == AgentStatus.COMPLETED:
            assert result.data is not None, "Completed result should have data"
    
    @staticmethod
    def assert_parse_result_valid(result: ParseResult, expected_language: LanguageType = None):
        """Assert that a parse result is valid"""
        assert result is not None, "Parse result should not be None"
        assert hasattr(result, 'file_path'), "Result should have file_path"
        assert hasattr(result, 'language'), "Result should have language"
        assert hasattr(result, 'content'), "Result should have content"
        assert isinstance(result.language, LanguageType), "Language should be LanguageType enum"
        
        if expected_language:
            assert result.language == expected_language, f"Expected language {expected_language}, got {result.language}"
        
        # Check that lists are properly initialized
        assert isinstance(result.functions, list), "Functions should be a list"
        assert isinstance(result.classes, list), "Classes should be a list"
        assert isinstance(result.imports, list), "Imports should be a list"
        assert isinstance(result.variables, list), "Variables should be a list"
        assert isinstance(result.errors, list), "Errors should be a list"
    
    @staticmethod
    def assert_metrics_reasonable(metrics: Dict[str, Any]):
        """Assert that metrics values are reasonable"""
        if "lines_of_code" in metrics:
            assert metrics["lines_of_code"] >= 0, "Lines of code should be non-negative"
        
        if "complexity" in metrics:
            assert metrics["complexity"] >= 0, "Complexity should be non-negative"
        
        if "maintainability_index" in metrics:
            assert 0 <= metrics["maintainability_index"] <= 100, "Maintainability index should be 0-100"
        
        if "comment_ratio" in metrics:
            assert 0 <= metrics["comment_ratio"] <= 1, "Comment ratio should be 0-1"


class PerformanceTimer:
    """Performance timing utility for tests"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """Start timing"""
        self.start_time = time.time()
    
    def stop(self):
        """Stop timing"""
        self.end_time = time.time()
    
    @property
    def duration(self) -> float:
        """Get duration in seconds"""
        if self.start_time is None or self.end_time is None:
            return 0.0
        return self.end_time - self.start_time
    
    def assert_duration_under(self, max_seconds: float):
        """Assert that duration is under specified seconds"""
        assert self.duration < max_seconds, f"Operation took {self.duration:.2f}s, expected under {max_seconds}s"


class AsyncTestHelper:
    """Helper for async test operations"""
    
    @staticmethod
    async def wait_for_condition(condition_func: Callable[[], bool], 
                                timeout: float = 5.0, 
                                interval: float = 0.1) -> bool:
        """Wait for a condition to become true"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            await asyncio.sleep(interval)
        
        return False
    
    @staticmethod
    async def run_with_timeout(coro, timeout: float = 10.0):
        """Run coroutine with timeout"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise AssertionError(f"Operation timed out after {timeout} seconds")


# Global test utilities instance
test_utils = {
    "mock_factory": MockFactory(),
    "data_generator": TestDataGenerator(),
    "assertions": AssertionHelpers(),
    "timer": PerformanceTimer(),
    "async_helper": AsyncTestHelper()
}
