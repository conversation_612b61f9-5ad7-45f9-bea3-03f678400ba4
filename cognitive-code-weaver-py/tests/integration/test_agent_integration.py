"""
Integration tests for Agent system

Tests agent interactions, message bus communication, registry integration,
and end-to-end agent workflows.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from ccw.core.agent import Agent, Agent<PERSON><PERSON>, Agent<PERSON>ontext, AgentR<PERSON>ult, AgentStatus
from ccw.core.registry import agent_registry
from ccw.core.message_bus import message_bus, Message, MessageType
from ccw.agents.master_agent import MasterAgent
from ccw.agents.cognitive_agent import CognitiveAgent


class TestAgentRegistryIntegration:
    """Test agent integration with registry"""
    
    @pytest.mark.asyncio
    async def test_agent_registration_and_discovery(self, initialized_system):
        """Test agent registration and discovery through registry"""
        # Create test agent
        class TestIntegrationAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "integration_test_agent"
            
            def _define_capabilities(self):
                return ["integration_test", "test_capability"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"integration_test": "success"}
                )
        
        # Register agent
        agent = TestIntegrationAgent()
        agent_registry.register_agent_instance(agent)
        
        # Test discovery
        found_agent = agent_registry.get_agent("integration_test_agent")
        assert found_agent is agent
        
        # Test capability-based discovery
        capable_agents = agent_registry.get_agents_by_capability("integration_test")
        assert len(capable_agents) == 1
        assert capable_agents[0] is agent
        
        # Test agent listing
        agents_info = agent_registry.list_agents()
        agent_ids = [info["agent_id"] for info in agents_info]
        assert "integration_test_agent" in agent_ids
        
        # Cleanup
        agent_registry.unregister_agent("integration_test_agent")
    
    @pytest.mark.asyncio
    async def test_agent_lifecycle_through_registry(self, initialized_system):
        """Test agent lifecycle management through registry"""
        class LifecycleTestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "lifecycle_test_agent"
            
            def _define_capabilities(self):
                return ["lifecycle_test"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        # Register agent
        agent = LifecycleTestAgent()
        agent_registry.register_agent_instance(agent)
        
        # Test startup through registry
        await agent_registry.startup_all()
        assert agent.is_running is True
        
        # Test shutdown through registry
        await agent_registry.shutdown_all()
        assert agent.is_running is False
        
        # Cleanup
        agent_registry.unregister_agent("lifecycle_test_agent")


class TestAgentMessageBusIntegration:
    """Test agent integration with message bus"""
    
    @pytest.mark.asyncio
    async def test_agent_message_publishing(self, initialized_system):
        """Test agent publishing messages to message bus"""
        messages_received = []
        
        # Create message handler
        async def test_handler(message: Message):
            messages_received.append(message)
        
        # Subscribe to messages
        message_bus.subscribe(MessageType.AGENT_STATUS, test_handler)
        
        # Create and start agent
        class MessageTestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "message_test_agent"
            
            def _define_capabilities(self):
                return ["message_test"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                # Agent should publish status messages during execution
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"message_test": "success"}
                )
        
        agent = MessageTestAgent()
        await agent.startup()
        
        # Execute task (should generate messages)
        task = AgentTask(
            task_type="message_test",
            description="Test message publishing"
        )
        context = AgentContext(session_id="test_session")
        
        await agent.execute_with_monitoring(task, context)
        
        # Give message bus time to process
        await asyncio.sleep(0.1)
        
        # Verify messages were published
        assert len(messages_received) > 0
        
        # Cleanup
        await agent.shutdown()
        message_bus.unsubscribe(MessageType.AGENT_STATUS, test_handler)
    
    @pytest.mark.asyncio
    async def test_agent_message_consumption(self, initialized_system):
        """Test agent consuming messages from message bus"""
        # This would test agents that listen to messages
        # For now, we'll test the basic message flow
        
        message_data = {"test": "data"}
        message = Message(
            type=MessageType.TASK_CREATED,
            data=message_data,
            source="test_source"
        )
        
        # Publish message
        await message_bus.publish(message)
        
        # Give message bus time to process
        await asyncio.sleep(0.1)
        
        # In a real scenario, agents would react to this message
        # For now, we just verify the message was published successfully


class TestMasterAgentIntegration:
    """Test Master Agent integration"""
    
    @pytest.mark.asyncio
    async def test_master_agent_task_delegation(self, initialized_system, mock_llm_client):
        """Test Master Agent delegating tasks to sub-agents"""
        # Create a mock sub-agent
        class MockSubAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "mock_sub_agent"
                self.executed_tasks = []
            
            def _define_capabilities(self):
                return ["code_analysis", "test_capability"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                self.executed_tasks.append(task)
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"sub_agent_result": "success"}
                )
        
        # Register sub-agent
        sub_agent = MockSubAgent()
        agent_registry.register_agent_instance(sub_agent)
        await sub_agent.startup()
        
        # Create master agent with mocked LLM
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            # Create task for master agent
            task = AgentTask(
                task_type="code_analysis",
                description="Analyze code structure",
                data={"code": "def test(): pass"}
            )
            context = AgentContext(
                session_id="test_session",
                workspace_path="/test/workspace"
            )
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Verify master agent completed successfully
            assert result.status == AgentStatus.COMPLETED
            
            # Verify sub-agent was called
            assert len(sub_agent.executed_tasks) > 0
            
            # Cleanup
            await master_agent.shutdown()
            await sub_agent.shutdown()
            agent_registry.unregister_agent("mock_sub_agent")
    
    @pytest.mark.asyncio
    async def test_master_agent_error_handling(self, initialized_system, mock_llm_client):
        """Test Master Agent error handling when sub-agents fail"""
        # Create a failing sub-agent
        class FailingSubAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "failing_sub_agent"
            
            def _define_capabilities(self):
                return ["failing_capability"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                raise ValueError("Sub-agent intentional failure")
        
        # Register failing sub-agent
        failing_agent = FailingSubAgent()
        agent_registry.register_agent_instance(failing_agent)
        await failing_agent.startup()
        
        # Create master agent
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            # Create task that would be delegated to failing agent
            task = AgentTask(
                task_type="failing_task",
                description="Task that will fail",
                data={"test": "data"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Master agent should handle the failure gracefully
            # (exact behavior depends on implementation)
            assert result is not None
            
            # Cleanup
            await master_agent.shutdown()
            await failing_agent.shutdown()
            agent_registry.unregister_agent("failing_sub_agent")


class TestCognitiveAgentIntegration:
    """Test Cognitive Agent integration"""
    
    @pytest.mark.asyncio
    async def test_cognitive_agent_with_llm(self, initialized_system, mock_llm_client):
        """Test Cognitive Agent integration with LLM"""
        with patch('ccw.agents.cognitive_agent.llm_client', mock_llm_client):
            cognitive_agent = CognitiveAgent()
            await cognitive_agent.startup()
            
            # Create cognitive task
            task = AgentTask(
                task_type="cognitive_analysis",
                description="Analyze user intent",
                data={"user_query": "What does this function do?"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await cognitive_agent.execute_with_monitoring(task, context)
            
            # Verify cognitive agent completed successfully
            assert result.status == AgentStatus.COMPLETED
            assert result.data is not None
            
            # Verify LLM was called
            mock_llm_client.generate_response.assert_called()
            
            # Cleanup
            await cognitive_agent.shutdown()


class TestEndToEndWorkflow:
    """Test end-to-end agent workflows"""
    
    @pytest.mark.asyncio
    async def test_complete_analysis_workflow(self, initialized_system, mock_llm_client, sample_workspace):
        """Test complete code analysis workflow"""
        # Create analysis agents
        class AnalysisAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "analysis_agent"
            
            def _define_capabilities(self):
                return ["workspace_analysis", "code_analysis"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                # Simulate analysis
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={
                        "analysis_result": {
                            "files_analyzed": 5,
                            "functions_found": 10,
                            "classes_found": 3,
                            "complexity_score": 7.5
                        }
                    }
                )
        
        # Register analysis agent
        analysis_agent = AnalysisAgent()
        agent_registry.register_agent_instance(analysis_agent)
        await analysis_agent.startup()
        
        # Create master agent
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            # Create workspace analysis task
            task = AgentTask(
                task_type="workspace_analysis",
                description="Analyze entire workspace",
                data={"workspace_path": str(sample_workspace)}
            )
            context = AgentContext(
                session_id="workflow_test",
                workspace_path=str(sample_workspace)
            )
            
            # Execute complete workflow
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Verify workflow completed successfully
            assert result.status == AgentStatus.COMPLETED
            assert "analysis_result" in result.data or result.data is not None
            
            # Cleanup
            await master_agent.shutdown()
            await analysis_agent.shutdown()
            agent_registry.unregister_agent("analysis_agent")
    
    @pytest.mark.asyncio
    async def test_multi_agent_collaboration(self, initialized_system, mock_llm_client):
        """Test multiple agents collaborating on a task"""
        collaboration_data = {}
        
        # Create collaborating agents
        class Agent1(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "collaborator_1"
            
            def _define_capabilities(self):
                return ["step_1", "preprocessing"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                collaboration_data["step_1"] = "completed"
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"step_1_result": "preprocessed_data"}
                )
        
        class Agent2(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "collaborator_2"
            
            def _define_capabilities(self):
                return ["step_2", "processing"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                # Check if step 1 was completed
                assert "step_1" in collaboration_data
                collaboration_data["step_2"] = "completed"
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"step_2_result": "processed_data"}
                )
        
        # Register agents
        agent1 = Agent1()
        agent2 = Agent2()
        agent_registry.register_agent_instance(agent1)
        agent_registry.register_agent_instance(agent2)
        await agent1.startup()
        await agent2.startup()
        
        # Create tasks for collaboration
        task1 = AgentTask(
            task_type="preprocessing",
            description="Preprocess data"
        )
        task2 = AgentTask(
            task_type="processing",
            description="Process data"
        )
        context = AgentContext(session_id="collaboration_test")
        
        # Execute tasks in sequence
        result1 = await agent1.execute_with_monitoring(task1, context)
        result2 = await agent2.execute_with_monitoring(task2, context)
        
        # Verify collaboration
        assert result1.status == AgentStatus.COMPLETED
        assert result2.status == AgentStatus.COMPLETED
        assert collaboration_data["step_1"] == "completed"
        assert collaboration_data["step_2"] == "completed"
        
        # Cleanup
        await agent1.shutdown()
        await agent2.shutdown()
        agent_registry.unregister_agent("collaborator_1")
        agent_registry.unregister_agent("collaborator_2")
