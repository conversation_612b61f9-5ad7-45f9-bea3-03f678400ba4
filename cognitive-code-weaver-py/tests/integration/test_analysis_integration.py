"""
Integration tests for Code Analysis system

Tests integration between parser, AST analyzer, dependency graph,
metrics calculator, and workspace analyzer.
"""

import pytest
from pathlib import Path

from ccw.analysis.parser import CodeParser
from ccw.analysis.ast_analyzer import ASTAnalyzer
from ccw.analysis.dependency_graph import <PERSON>penden<PERSON><PERSON><PERSON>y<PERSON>
from ccw.analysis.metrics import ComplexityAnalyzer
from ccw.analysis.symbols import SymbolExtractor
from ccw.analysis.workspace import WorkspaceAnalyzer


class TestParserToASTIntegration:
    """Test integration between parser and AST analyzer"""
    
    @pytest.mark.asyncio
    async def test_parser_to_ast_pipeline(self, sample_python_code):
        """Test complete parser to AST analysis pipeline"""
        # Parse code
        parser = CodeParser()
        parse_result = parser.parse_content(
            sample_python_code,
            file_path="test.py"
        )
        
        # Analyze AST
        ast_analyzer = ASTAnalyzer()
        ast_analysis = ast_analyzer.analyze(parse_result)
        
        # Verify integration
        assert ast_analysis["file_path"] == "test.py"
        assert ast_analysis["language"] == "python"
        assert len(ast_analysis["functions"]) > 0
        assert len(ast_analysis["classes"]) > 0
        
        # Verify AST analysis enhances parser results
        parser_functions = len(parse_result.functions)
        ast_functions = len(ast_analysis["functions"])
        
        # AST analysis should provide at least as much detail
        assert ast_functions >= parser_functions
        
        # Check complexity metrics are calculated
        assert "complexity_metrics" in ast_analysis
        complexity = ast_analysis["complexity_metrics"]
        assert "cyclomatic_complexity" in complexity
        assert complexity["cyclomatic_complexity"] > 0


class TestParserToDependencyIntegration:
    """Test integration between parser and dependency analyzer"""
    
    @pytest.mark.asyncio
    async def test_parser_to_dependency_pipeline(self, sample_workspace):
        """Test parser to dependency analysis pipeline"""
        parser = CodeParser()
        dependency_analyzer = DependencyAnalyzer()
        
        # Parse multiple files
        parse_results = []
        for py_file in sample_workspace.glob("**/*.py"):
            result = parser.parse_file(str(py_file))
            if not result.errors:
                parse_results.append(result)
        
        assert len(parse_results) > 0
        
        # Build dependency graph
        dependency_graph = dependency_analyzer.analyze_workspace(parse_results)
        
        # Verify dependency graph
        assert len(dependency_graph.nodes) > 0
        assert len(dependency_graph.edges) >= 0
        
        # Check for file nodes
        file_nodes = [
            node for node in dependency_graph.nodes.values()
            if node.node_type == "file"
        ]
        assert len(file_nodes) == len(parse_results)
        
        # Check coupling metrics
        metrics = dependency_graph.calculate_coupling_metrics()
        assert "total_nodes" in metrics
        assert "total_edges" in metrics
        assert metrics["total_nodes"] == len(dependency_graph.nodes)


class TestParserToMetricsIntegration:
    """Test integration between parser and metrics calculator"""
    
    @pytest.mark.asyncio
    async def test_parser_to_metrics_pipeline(self, sample_python_code):
        """Test parser to metrics calculation pipeline"""
        # Parse code
        parser = CodeParser()
        parse_result = parser.parse_content(
            sample_python_code,
            file_path="test.py"
        )
        
        # Calculate metrics
        complexity_analyzer = ComplexityAnalyzer()
        metrics = complexity_analyzer.analyze(parse_result)
        
        # Verify metrics integration
        assert metrics.file_path == "test.py"
        assert metrics.language.value == "python"
        
        # Check size metrics
        assert metrics.size.lines_of_code > 0
        assert metrics.size.functions_count == len(parse_result.functions)
        assert metrics.size.classes_count == len(parse_result.classes)
        
        # Check complexity metrics
        assert metrics.complexity.cyclomatic_complexity > 0
        
        # Check quality metrics
        assert 0 <= metrics.quality.comment_ratio <= 1
        assert 0 <= metrics.maintainability_index <= 100
        assert 0 <= metrics.technical_debt_ratio <= 1


class TestParserToSymbolsIntegration:
    """Test integration between parser and symbol extractor"""
    
    @pytest.mark.asyncio
    async def test_parser_to_symbols_pipeline(self, sample_python_code):
        """Test parser to symbol extraction pipeline"""
        # Parse code
        parser = CodeParser()
        parse_result = parser.parse_content(
            sample_python_code,
            file_path="test.py"
        )
        
        # Extract symbols
        symbol_extractor = SymbolExtractor()
        symbols = symbol_extractor.extract(parse_result)
        
        # Verify symbol extraction
        assert len(symbols) > 0
        
        # Check symbol types
        symbol_types = {symbol.symbol_type.value for symbol in symbols}
        assert "function" in symbol_types
        assert "class" in symbol_types
        
        # Verify symbols match parser results
        function_symbols = [s for s in symbols if s.symbol_type.value == "function"]
        class_symbols = [s for s in symbols if s.symbol_type.value == "class"]
        
        # Should have at least as many symbols as parser found
        assert len(function_symbols) >= len(parse_result.functions)
        assert len(class_symbols) >= len(parse_result.classes)
        
        # Check symbol details
        for symbol in symbols:
            assert symbol.file_path == "test.py"
            assert symbol.line_number > 0
            assert symbol.name is not None


class TestWorkspaceAnalyzerIntegration:
    """Test workspace analyzer integration with all components"""
    
    @pytest.mark.asyncio
    async def test_workspace_analyzer_complete_pipeline(self, sample_workspace):
        """Test complete workspace analysis pipeline"""
        workspace_analyzer = WorkspaceAnalyzer()
        
        # Analyze workspace
        result = workspace_analyzer.analyze_workspace(
            str(sample_workspace),
            max_files=10
        )
        
        # Verify complete analysis
        assert result.workspace_path == str(sample_workspace)
        assert result.total_files > 0
        assert result.analyzed_files > 0
        
        # Check parse results
        assert len(result.parse_results) > 0
        for parse_result in result.parse_results:
            assert parse_result.language.value in ["python", "javascript"]
            assert not parse_result.errors
        
        # Check AST analyses
        assert len(result.ast_analyses) > 0
        
        # Check code metrics
        assert len(result.code_metrics) > 0
        for metrics in result.code_metrics:
            assert metrics.size.lines_of_code > 0
            assert metrics.complexity.cyclomatic_complexity >= 0
        
        # Check symbols
        assert len(result.symbols) > 0
        
        # Check dependency graph
        assert result.dependency_graph is not None
        assert len(result.dependency_graph.nodes) > 0
        
        # Check workspace metrics
        assert result.workspace_metrics is not None
        workspace_metrics = result.workspace_metrics
        assert "total_lines_of_code" in workspace_metrics
        assert "total_functions" in workspace_metrics
        assert "average_complexity" in workspace_metrics
        
        # Check quality report
        assert result.quality_report is not None
        quality_report = result.quality_report
        assert "overall_grade" in quality_report
        assert quality_report["overall_grade"] in ["A", "B", "C", "D", "F"]
    
    @pytest.mark.asyncio
    async def test_workspace_analyzer_single_file(self, temp_dir, sample_python_code):
        """Test workspace analyzer on single file"""
        # Create test file
        test_file = temp_dir / "single_test.py"
        test_file.write_text(sample_python_code)
        
        workspace_analyzer = WorkspaceAnalyzer()
        
        # Analyze single file
        result = workspace_analyzer.analyze_file(str(test_file))
        
        # Verify single file analysis
        assert result["file_path"] == str(test_file)
        assert result["language"] == "python"
        assert "errors" not in result or len(result["errors"]) == 0
        
        # Check all analysis components
        assert "parse_result" in result
        assert "ast_analysis" in result
        assert "symbols" in result
        assert "metrics" in result
        assert "dependency_graph" in result
        
        # Verify parse result
        parse_result = result["parse_result"]
        assert len(parse_result["functions"]) > 0
        assert len(parse_result["classes"]) > 0
        
        # Verify metrics
        metrics = result["metrics"]
        assert metrics["size"]["lines_of_code"] > 0
        assert metrics["complexity"]["cyclomatic_complexity"] > 0
        
        # Verify symbols
        symbols = result["symbols"]
        assert len(symbols) > 0


class TestCrossComponentDataFlow:
    """Test data flow between analysis components"""
    
    @pytest.mark.asyncio
    async def test_symbol_references_across_files(self, sample_workspace):
        """Test symbol reference tracking across multiple files"""
        parser = CodeParser()
        symbol_extractor = SymbolExtractor()
        
        # Parse all files
        parse_results = []
        for py_file in sample_workspace.glob("**/*.py"):
            result = parser.parse_file(str(py_file))
            if not result.errors:
                parse_results.append(result)
        
        # Extract symbols from all files
        all_symbols = []
        for parse_result in parse_results:
            symbols = symbol_extractor.extract(parse_result)
            all_symbols.extend(symbols)
        
        # Find cross-references
        symbols_with_refs = symbol_extractor.find_symbol_references(
            all_symbols, parse_results
        )
        
        # Verify cross-reference tracking
        assert len(symbols_with_refs) == len(all_symbols)
        
        # Check for symbols with references
        symbols_with_references = [
            s for s in symbols_with_refs
            if len(s.references) > 0
        ]
        
        # Should find some cross-references in a multi-file workspace
        # (exact number depends on the sample workspace structure)
        assert len(symbols_with_references) >= 0
    
    @pytest.mark.asyncio
    async def test_metrics_aggregation(self, sample_workspace):
        """Test metrics aggregation across workspace"""
        parser = CodeParser()
        complexity_analyzer = ComplexityAnalyzer()
        
        # Parse and analyze all files
        file_metrics = []
        for py_file in sample_workspace.glob("**/*.py"):
            parse_result = parser.parse_file(str(py_file))
            if not parse_result.errors:
                metrics = complexity_analyzer.analyze(parse_result)
                file_metrics.append(metrics)
        
        # Calculate workspace metrics
        workspace_metrics = complexity_analyzer.calculate_workspace_metrics(file_metrics)
        
        # Verify aggregation
        assert "total_files" in workspace_metrics
        assert "total_lines_of_code" in workspace_metrics
        assert "average_complexity" in workspace_metrics
        
        # Check calculations
        total_loc = sum(m.size.lines_of_code for m in file_metrics)
        assert workspace_metrics["total_lines_of_code"] == total_loc
        
        if file_metrics:
            avg_complexity = sum(m.complexity.cyclomatic_complexity for m in file_metrics) / len(file_metrics)
            assert abs(workspace_metrics["average_complexity"] - avg_complexity) < 0.01


class TestErrorHandlingIntegration:
    """Test error handling across integrated components"""
    
    @pytest.mark.asyncio
    async def test_parser_error_propagation(self, temp_dir):
        """Test error propagation from parser through analysis pipeline"""
        # Create file with syntax error
        invalid_file = temp_dir / "invalid.py"
        invalid_file.write_text("def invalid_function(\n    # Missing closing paren")
        
        workspace_analyzer = WorkspaceAnalyzer()
        
        # Analyze file with error
        result = workspace_analyzer.analyze_file(str(invalid_file))
        
        # Should handle error gracefully
        assert result["file_path"] == str(invalid_file)
        # May have errors or reduced functionality
        
        # Verify system doesn't crash
        assert "errors" in result or len(result.get("errors", [])) >= 0
    
    @pytest.mark.asyncio
    async def test_workspace_partial_failure_handling(self, temp_dir, sample_python_code):
        """Test workspace analysis with some file failures"""
        # Create mix of valid and invalid files
        valid_file = temp_dir / "valid.py"
        valid_file.write_text(sample_python_code)
        
        invalid_file = temp_dir / "invalid.py"
        invalid_file.write_text("invalid python syntax {{{")
        
        binary_file = temp_dir / "binary.bin"
        binary_file.write_bytes(b'\x00\x01\x02\x03')
        
        workspace_analyzer = WorkspaceAnalyzer()
        
        # Analyze workspace with mixed files
        result = workspace_analyzer.analyze_workspace(str(temp_dir))
        
        # Should handle partial failures gracefully
        assert result.total_files >= 2  # At least valid and invalid files
        assert result.analyzed_files >= 1  # At least the valid file
        assert result.skipped_files >= 0  # Some files may be skipped
        
        # Should have some successful results
        assert len(result.parse_results) >= 1
        
        # May have errors but shouldn't crash
        assert isinstance(result.errors, list)
