#!/usr/bin/env python3
"""
Comprehensive test runner for Cognitive Code Weaver

Provides a unified interface for running all tests with various options,
reporting, and performance analysis.
"""

import argparse
import asyncio
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn


console = Console()


class TestRunner:
    """Comprehensive test runner with reporting and analysis"""
    
    def __init__(self):
        self.console = console
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_tests(self, test_type: str = "all", verbose: bool = False, 
                 coverage: bool = False, parallel: bool = False,
                 markers: Optional[List[str]] = None) -> int:
        """
        Run tests with specified options
        
        Args:
            test_type: Type of tests to run (unit, integration, agent, all)
            verbose: Enable verbose output
            coverage: Enable coverage reporting
            parallel: Run tests in parallel
            markers: Specific test markers to run
            
        Returns:
            Exit code (0 for success, non-zero for failure)
        """
        self.start_time = time.time()
        
        try:
            # Display test run header
            self._display_header(test_type, verbose, coverage, parallel)
            
            # Build pytest arguments
            pytest_args = self._build_pytest_args(
                test_type, verbose, coverage, parallel, markers
            )
            
            # Run tests
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=self.console
            ) as progress:
                
                task = progress.add_task("Running tests...", total=None)
                
                # Execute pytest
                exit_code = pytest.main(pytest_args)
                
                progress.update(task, description="Tests completed!")
            
            self.end_time = time.time()
            
            # Display results summary
            self._display_summary(exit_code)
            
            return exit_code
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Tests interrupted by user[/yellow]")
            return 1
        except Exception as e:
            self.console.print(f"\n[red]Test execution failed: {e}[/red]")
            return 1
    
    def _display_header(self, test_type: str, verbose: bool, coverage: bool, parallel: bool):
        """Display test run header"""
        title = "🧠 Cognitive Code Weaver - Test Suite"
        
        options = []
        if test_type != "all":
            options.append(f"Type: {test_type}")
        if verbose:
            options.append("Verbose")
        if coverage:
            options.append("Coverage")
        if parallel:
            options.append("Parallel")
        
        subtitle = f"Options: {', '.join(options)}" if options else "Running all tests"
        
        panel = Panel(
            f"{title}\n{subtitle}",
            border_style="blue",
            padding=(1, 2)
        )
        self.console.print(panel)
    
    def _build_pytest_args(self, test_type: str, verbose: bool, coverage: bool,
                          parallel: bool, markers: Optional[List[str]]) -> List[str]:
        """Build pytest command line arguments"""
        args = []
        
        # Test directories based on type
        if test_type == "unit":
            args.append("tests/unit")
        elif test_type == "integration":
            args.append("tests/integration")
        elif test_type == "agent":
            args.append("tests/agent")
        else:  # all
            args.append("tests/")
        
        # Verbose output
        if verbose:
            args.extend(["-v", "-s"])
        
        # Coverage
        if coverage:
            args.extend([
                "--cov=ccw",
                "--cov-report=html",
                "--cov-report=term-missing",
                "--cov-fail-under=80"
            ])
        
        # Parallel execution
        if parallel:
            args.extend(["-n", "auto"])
        
        # Markers
        if markers:
            for marker in markers:
                args.extend(["-m", marker])
        
        # Additional options
        args.extend([
            "--tb=short",  # Short traceback format
            "--strict-markers",  # Strict marker checking
            "--disable-warnings"  # Disable warnings for cleaner output
        ])
        
        return args
    
    def _display_summary(self, exit_code: int):
        """Display test results summary"""
        duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        if exit_code == 0:
            status = "[green]✓ PASSED[/green]"
            border_style = "green"
        else:
            status = "[red]✗ FAILED[/red]"
            border_style = "red"
        
        summary = f"""
Test Execution Summary

Status: {status}
Duration: {duration:.2f} seconds
Exit Code: {exit_code}
"""
        
        panel = Panel(summary.strip(), title="Results", border_style=border_style)
        self.console.print(panel)
    
    def run_specific_tests(self, test_files: List[str], verbose: bool = False) -> int:
        """Run specific test files"""
        self.console.print(f"[cyan]Running specific tests: {', '.join(test_files)}[/cyan]")
        
        pytest_args = test_files.copy()
        if verbose:
            pytest_args.extend(["-v", "-s"])
        
        return pytest.main(pytest_args)
    
    def run_performance_tests(self) -> int:
        """Run performance-focused tests"""
        self.console.print("[cyan]Running performance tests...[/cyan]")
        
        pytest_args = [
            "tests/",
            "-m", "slow",
            "-v",
            "--durations=10"  # Show 10 slowest tests
        ]
        
        return pytest.main(pytest_args)
    
    def validate_test_environment(self) -> bool:
        """Validate test environment setup"""
        self.console.print("[cyan]Validating test environment...[/cyan]")
        
        checks = [
            ("Python version", self._check_python_version),
            ("Required packages", self._check_packages),
            ("Test directories", self._check_test_directories),
            ("Configuration files", self._check_config_files)
        ]
        
        table = Table(title="Environment Validation")
        table.add_column("Check", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Details")
        
        all_passed = True
        
        for check_name, check_func in checks:
            try:
                result = check_func()
                if result["passed"]:
                    status = "✓ PASS"
                    style = "green"
                else:
                    status = "✗ FAIL"
                    style = "red"
                    all_passed = False
                
                table.add_row(
                    check_name,
                    f"[{style}]{status}[/{style}]",
                    result["details"]
                )
                
            except Exception as e:
                table.add_row(
                    check_name,
                    "[red]✗ ERROR[/red]",
                    str(e)
                )
                all_passed = False
        
        self.console.print(table)
        
        if all_passed:
            self.console.print("[green]✓ Environment validation passed[/green]")
        else:
            self.console.print("[red]✗ Environment validation failed[/red]")
        
        return all_passed
    
    def _check_python_version(self) -> Dict[str, any]:
        """Check Python version"""
        version = sys.version_info
        required_major, required_minor = 3, 8
        
        passed = version.major >= required_major and version.minor >= required_minor
        details = f"Python {version.major}.{version.minor}.{version.micro}"
        
        return {"passed": passed, "details": details}
    
    def _check_packages(self) -> Dict[str, any]:
        """Check required packages"""
        required_packages = [
            "pytest", "pytest-asyncio", "pytest-cov", "rich", "pydantic", "typer"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        passed = len(missing_packages) == 0
        details = "All packages available" if passed else f"Missing: {', '.join(missing_packages)}"
        
        return {"passed": passed, "details": details}
    
    def _check_test_directories(self) -> Dict[str, any]:
        """Check test directory structure"""
        test_dirs = [
            "tests/unit",
            "tests/integration", 
            "tests/agent",
            "tests/utils"
        ]
        
        missing_dirs = []
        for test_dir in test_dirs:
            if not Path(test_dir).exists():
                missing_dirs.append(test_dir)
        
        passed = len(missing_dirs) == 0
        details = "All directories exist" if passed else f"Missing: {', '.join(missing_dirs)}"
        
        return {"passed": passed, "details": details}
    
    def _check_config_files(self) -> Dict[str, any]:
        """Check configuration files"""
        config_files = [
            "tests/conftest.py",
            "pytest.ini"
        ]
        
        existing_files = []
        for config_file in config_files:
            if Path(config_file).exists():
                existing_files.append(config_file)
        
        passed = len(existing_files) > 0
        details = f"Found: {', '.join(existing_files)}" if existing_files else "No config files found"
        
        return {"passed": passed, "details": details}


def main():
    """Main entry point for test runner"""
    parser = argparse.ArgumentParser(
        description="Cognitive Code Weaver Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/run_tests.py                    # Run all tests
  python tests/run_tests.py --type unit        # Run only unit tests
  python tests/run_tests.py --coverage         # Run with coverage
  python tests/run_tests.py --parallel         # Run tests in parallel
  python tests/run_tests.py --markers slow     # Run only slow tests
  python tests/run_tests.py --validate         # Validate environment
        """
    )
    
    parser.add_argument(
        "--type", "-t",
        choices=["unit", "integration", "agent", "all"],
        default="all",
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Enable coverage reporting"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="Run tests in parallel"
    )
    
    parser.add_argument(
        "--markers", "-m",
        nargs="+",
        help="Run tests with specific markers"
    )
    
    parser.add_argument(
        "--files", "-f",
        nargs="+",
        help="Run specific test files"
    )
    
    parser.add_argument(
        "--performance",
        action="store_true",
        help="Run performance tests"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate test environment"
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner()
    
    # Handle different modes
    if args.validate:
        success = runner.validate_test_environment()
        return 0 if success else 1
    
    elif args.performance:
        return runner.run_performance_tests()
    
    elif args.files:
        return runner.run_specific_tests(args.files, args.verbose)
    
    else:
        return runner.run_tests(
            test_type=args.type,
            verbose=args.verbose,
            coverage=args.coverage,
            parallel=args.parallel,
            markers=args.markers
        )


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
