"""
Testing Framework for Cognitive Code Weaver

This package provides comprehensive testing infrastructure including:
- Unit tests for individual components
- Integration tests for system interactions
- Agent behavior tests for AI components
- Mock utilities and test fixtures
- Performance and load testing
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path for testing
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
TEST_CONFIG = {
    "test_data_dir": project_root / "tests" / "test_data",
    "temp_dir": project_root / "tests" / "temp",
    "fixtures_dir": project_root / "tests" / "fixtures",
    "mock_responses_dir": project_root / "tests" / "mock_responses"
}

# Ensure test directories exist
for dir_path in TEST_CONFIG.values():
    dir_path.mkdir(parents=True, exist_ok=True)

__version__ = "1.0.0"
