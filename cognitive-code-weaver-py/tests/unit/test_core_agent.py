"""
Unit tests for core Agent functionality

Tests the base Agent class, task execution, lifecycle management,
and error handling.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from ccw.core.agent import (
    Agent, AgentTask, AgentContext, AgentResult, AgentStatus, TaskPriority,
    create_agent_task, create_agent_context
)


class TestAgentTask:
    """Test AgentTask functionality"""
    
    def test_create_agent_task(self):
        """Test agent task creation"""
        task = create_agent_task(
            task_type="test_task",
            description="Test description",
            data={"key": "value"},
            priority=TaskPriority.HIGH
        )
        
        assert task.task_type == "test_task"
        assert task.description == "Test description"
        assert task.data == {"key": "value"}
        assert task.priority == TaskPriority.HIGH
        assert task.id is not None
        assert isinstance(task.created_at, datetime)
    
    def test_task_defaults(self):
        """Test task creation with defaults"""
        task = create_agent_task("simple_task")
        
        assert task.task_type == "simple_task"
        assert task.description == ""
        assert task.data == {}
        assert task.priority == TaskPriority.MEDIUM
    
    def test_task_to_dict(self):
        """Test task serialization"""
        task = create_agent_task(
            task_type="test_task",
            description="Test",
            data={"test": True}
        )
        
        task_dict = task.to_dict()
        
        assert task_dict["task_type"] == "test_task"
        assert task_dict["description"] == "Test"
        assert task_dict["data"] == {"test": True}
        assert "id" in task_dict
        assert "created_at" in task_dict


class TestAgentContext:
    """Test AgentContext functionality"""
    
    def test_create_agent_context(self):
        """Test agent context creation"""
        context = create_agent_context(
            session_id="test_session",
            user_query="Test query",
            workspace_path="/test/path",
            metadata={"user": "test_user"}
        )
        
        assert context.session_id == "test_session"
        assert context.user_query == "Test query"
        assert context.workspace_path == "/test/path"
        assert context.metadata == {"user": "test_user"}
        assert isinstance(context.created_at, datetime)
    
    def test_context_defaults(self):
        """Test context creation with defaults"""
        context = create_agent_context("session_123")
        
        assert context.session_id == "session_123"
        assert context.user_query == ""
        assert context.workspace_path is None
        assert context.metadata == {}
    
    def test_context_to_dict(self):
        """Test context serialization"""
        context = create_agent_context(
            session_id="test_session",
            user_query="Test query"
        )
        
        context_dict = context.to_dict()
        
        assert context_dict["session_id"] == "test_session"
        assert context_dict["user_query"] == "Test query"
        assert "created_at" in context_dict


class TestAgentResult:
    """Test AgentResult functionality"""
    
    def test_agent_result_creation(self):
        """Test agent result creation"""
        result = AgentResult(
            agent_id="test_agent",
            task_id="task_123",
            status=AgentStatus.COMPLETED,
            data={"result": "success"},
            confidence=0.95,
            error="No error"
        )
        
        assert result.agent_id == "test_agent"
        assert result.task_id == "task_123"
        assert result.status == AgentStatus.COMPLETED
        assert result.data == {"result": "success"}
        assert result.confidence == 0.95
        assert result.error == "No error"
        assert isinstance(result.completed_at, datetime)
    
    def test_result_defaults(self):
        """Test result creation with defaults"""
        result = AgentResult(
            agent_id="test_agent",
            task_id="task_123",
            status=AgentStatus.COMPLETED
        )
        
        assert result.data == {}
        assert result.confidence == 1.0
        assert result.error is None
    
    def test_result_to_dict(self):
        """Test result serialization"""
        result = AgentResult(
            agent_id="test_agent",
            task_id="task_123",
            status=AgentStatus.COMPLETED,
            data={"test": True}
        )
        
        result_dict = result.to_dict()
        
        assert result_dict["agent_id"] == "test_agent"
        assert result_dict["task_id"] == "task_123"
        assert result_dict["status"] == "completed"
        assert result_dict["data"] == {"test": True}
        assert "completed_at" in result_dict


class TestAgent:
    """Test base Agent functionality"""
    
    def test_agent_initialization(self, mock_agent):
        """Test agent initialization"""
        assert mock_agent.agent_id == "mock_agent"
        assert mock_agent.is_running is False
        assert mock_agent.metrics["tasks_completed"] == 0
        assert mock_agent.capabilities == ["mock_capability", "test_capability"]
    
    @pytest.mark.asyncio
    async def test_agent_startup_shutdown(self, mock_agent):
        """Test agent startup and shutdown"""
        # Test startup
        await mock_agent.startup()
        assert mock_agent.is_running is True
        
        # Test shutdown
        await mock_agent.shutdown()
        assert mock_agent.is_running is False
    
    @pytest.mark.asyncio
    async def test_agent_execute(self, mock_agent, agent_task, agent_context):
        """Test agent task execution"""
        await mock_agent.startup()
        
        result = await mock_agent.execute(agent_task, agent_context)
        
        assert result.status == AgentStatus.COMPLETED
        assert result.agent_id == "mock_agent"
        assert result.task_id == agent_task.id
        assert result.data["mock_result"] == "success"
        assert mock_agent.call_count == 1
        
        await mock_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_execute_with_monitoring(self, mock_agent, agent_task, agent_context):
        """Test agent execution with monitoring"""
        await mock_agent.startup()
        
        result = await mock_agent.execute_with_monitoring(agent_task, agent_context)
        
        assert result.status == AgentStatus.COMPLETED
        assert mock_agent.metrics["tasks_completed"] == 1
        assert mock_agent.metrics["total_execution_time"] > 0
        
        await mock_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_execute_not_running(self, mock_agent, agent_task, agent_context):
        """Test agent execution when not running"""
        # Don't start the agent
        result = await mock_agent.execute(agent_task, agent_context)
        
        assert result.status == AgentStatus.FAILED
        assert "not running" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_agent_supports_capability(self, mock_agent):
        """Test capability checking"""
        assert mock_agent.supports_capability("mock_capability") is True
        assert mock_agent.supports_capability("test_capability") is True
        assert mock_agent.supports_capability("unknown_capability") is False
    
    def test_agent_get_info(self, mock_agent):
        """Test agent info retrieval"""
        info = mock_agent.get_info()
        
        assert info["agent_id"] == "mock_agent"
        assert info["is_running"] is False
        assert info["capabilities"] == ["mock_capability", "test_capability"]
        assert "metrics" in info
    
    def test_agent_get_metrics(self, mock_agent):
        """Test agent metrics retrieval"""
        metrics = mock_agent.get_metrics()
        
        assert "tasks_completed" in metrics
        assert "tasks_failed" in metrics
        assert "total_execution_time" in metrics
        assert "average_execution_time" in metrics


class TestAgentErrorHandling:
    """Test agent error handling"""
    
    @pytest.mark.asyncio
    async def test_agent_execute_exception(self, agent_task, agent_context):
        """Test agent execution with exception"""
        class FailingAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "failing_agent"
            
            def _define_capabilities(self):
                return ["fail_capability"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                raise ValueError("Test exception")
        
        agent = FailingAgent()
        await agent.startup()
        
        result = await agent.execute_with_monitoring(agent_task, agent_context)
        
        assert result.status == AgentStatus.FAILED
        assert "Test exception" in result.error
        assert agent.metrics["tasks_failed"] == 1
        
        await agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_timeout_handling(self, agent_task, agent_context):
        """Test agent timeout handling"""
        class SlowAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "slow_agent"
                self.execution_timeout = 0.1  # Very short timeout
            
            def _define_capabilities(self):
                return ["slow_capability"]
            
            async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
                await asyncio.sleep(1.0)  # Sleep longer than timeout
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent = SlowAgent()
        await agent.startup()
        
        result = await agent.execute_with_monitoring(agent_task, agent_context)
        
        assert result.status == AgentStatus.FAILED
        assert "timeout" in result.error.lower()
        
        await agent.shutdown()


class TestAgentLifecycle:
    """Test agent lifecycle management"""
    
    @pytest.mark.asyncio
    async def test_agent_multiple_startups(self, mock_agent):
        """Test multiple agent startups"""
        await mock_agent.startup()
        assert mock_agent.is_running is True
        
        # Second startup should be idempotent
        await mock_agent.startup()
        assert mock_agent.is_running is True
        
        await mock_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_multiple_shutdowns(self, mock_agent):
        """Test multiple agent shutdowns"""
        await mock_agent.startup()
        await mock_agent.shutdown()
        assert mock_agent.is_running is False
        
        # Second shutdown should be idempotent
        await mock_agent.shutdown()
        assert mock_agent.is_running is False
    
    @pytest.mark.asyncio
    async def test_agent_context_manager(self, mock_agent):
        """Test agent as context manager"""
        async with mock_agent:
            assert mock_agent.is_running is True
        
        assert mock_agent.is_running is False


class TestAgentMetrics:
    """Test agent metrics tracking"""
    
    @pytest.mark.asyncio
    async def test_metrics_tracking(self, mock_agent, agent_task, agent_context):
        """Test metrics are properly tracked"""
        await mock_agent.startup()
        
        # Execute multiple tasks
        for i in range(3):
            await mock_agent.execute_with_monitoring(agent_task, agent_context)
        
        metrics = mock_agent.get_metrics()
        
        assert metrics["tasks_completed"] == 3
        assert metrics["tasks_failed"] == 0
        assert metrics["total_execution_time"] > 0
        assert metrics["average_execution_time"] > 0
        
        await mock_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_metrics_reset(self, mock_agent, agent_task, agent_context):
        """Test metrics reset functionality"""
        await mock_agent.startup()
        
        # Execute a task
        await mock_agent.execute_with_monitoring(agent_task, agent_context)
        assert mock_agent.metrics["tasks_completed"] == 1
        
        # Reset metrics
        mock_agent.reset_metrics()
        assert mock_agent.metrics["tasks_completed"] == 0
        assert mock_agent.metrics["total_execution_time"] == 0
        
        await mock_agent.shutdown()
