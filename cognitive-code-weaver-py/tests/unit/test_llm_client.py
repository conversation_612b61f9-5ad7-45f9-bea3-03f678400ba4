"""
Unit tests for LLM Client functionality

Tests LLM client operations, provider management, caching,
fallback mechanisms, and error handling.
"""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import asyncio

from ccw.llm.client import LLMClient, LLMClientConfig
from ccw.llm.providers.base import (
    LLMMessage, LLMResponse, LLMConfig, MessageRole,
    create_message, create_user_message, create_system_message,
    LLMProviderError, LLMRateLimitError, LLMAuthenticationError
)


class TestLLMMessage:
    """Test LLM message functionality"""
    
    def test_create_user_message(self):
        """Test creating user message"""
        message = create_user_message("Hello, world!")
        
        assert message.role == MessageRole.USER
        assert message.content == "Hello, world!"
        assert message.name is None
        assert message.function_call is None
    
    def test_create_system_message(self):
        """Test creating system message"""
        message = create_system_message("You are a helpful assistant.")
        
        assert message.role == MessageRole.SYSTEM
        assert message.content == "You are a helpful assistant."
    
    def test_create_message_with_name(self):
        """Test creating message with name"""
        message = create_message(
            MessageRole.ASSISTANT,
            "Hello!",
            name="assistant_bot"
        )
        
        assert message.role == MessageRole.ASSISTANT
        assert message.content == "Hello!"
        assert message.name == "assistant_bot"
    
    def test_message_to_dict(self):
        """Test message serialization"""
        message = create_user_message("Test message")
        message_dict = message.to_dict()
        
        assert message_dict["role"] == "user"
        assert message_dict["content"] == "Test message"
        assert "name" in message_dict
        assert "function_call" in message_dict


class TestLLMResponse:
    """Test LLM response functionality"""
    
    def test_response_creation(self):
        """Test LLM response creation"""
        response = LLMResponse(
            content="Test response",
            model="test-model",
            provider="test-provider",
            usage={"total_tokens": 100},
            response_time=1.5
        )
        
        assert response.content == "Test response"
        assert response.model == "test-model"
        assert response.provider == "test-provider"
        assert response.usage["total_tokens"] == 100
        assert response.response_time == 1.5
    
    def test_response_to_dict(self):
        """Test response serialization"""
        response = LLMResponse(
            content="Test response",
            model="test-model",
            provider="test-provider"
        )
        
        response_dict = response.to_dict()
        
        assert response_dict["content"] == "Test response"
        assert response_dict["model"] == "test-model"
        assert response_dict["provider"] == "test-provider"


class TestLLMClientConfig:
    """Test LLM client configuration"""
    
    def test_default_config(self):
        """Test default configuration"""
        config = LLMClientConfig()
        
        assert config.primary_provider == "openai"
        assert config.fallback_providers == []
        assert config.enable_caching is True
        assert config.cache_ttl == 3600
        assert config.max_retries == 3
        assert config.enable_fallback is True
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = LLMClientConfig(
            primary_provider="anthropic",
            fallback_providers=["openai", "local"],
            enable_caching=False,
            cache_ttl=7200,
            max_retries=5
        )
        
        assert config.primary_provider == "anthropic"
        assert config.fallback_providers == ["openai", "local"]
        assert config.enable_caching is False
        assert config.cache_ttl == 7200
        assert config.max_retries == 5


class TestLLMClient:
    """Test LLM client functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client_config = LLMClientConfig(
            primary_provider="mock",
            fallback_providers=["mock_fallback"],
            enable_caching=True
        )
    
    @patch('ccw.llm.client.config')
    def test_client_initialization(self, mock_config):
        """Test LLM client initialization"""
        mock_config.get_section.return_value = {
            "provider": "mock",
            "model": "test-model",
            "api_key": "test-key"
        }
        
        with patch('ccw.llm.client.LLMClient._initialize_providers'):
            client = LLMClient(self.client_config)
            
            assert client.client_config.primary_provider == "mock"
            assert client.providers == {}
            assert client.response_cache == {}
            assert "total_requests" in client.usage_stats
    
    @pytest.mark.asyncio
    async def test_generate_response_success(self, mock_llm_client):
        """Test successful response generation"""
        messages = [create_user_message("Hello")]
        
        response = await mock_llm_client.generate_response(messages)
        
        assert response.content == "Test response from mock LLM"
        assert response.model == "test-model"
        assert response.provider == "mock"
        
        # Verify the mock was called
        mock_llm_client.generate_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_response_with_string(self, mock_llm_client):
        """Test response generation with string input"""
        response = await mock_llm_client.generate_response("Hello, world!")
        
        assert response.content == "Test response from mock LLM"
        mock_llm_client.generate_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_stream(self, mock_llm_client):
        """Test streaming response generation"""
        # Mock streaming response
        async def mock_stream():
            for chunk in ["Hello", " ", "world", "!"]:
                yield chunk
        
        mock_llm_client.generate_stream.return_value = mock_stream()
        
        chunks = []
        async for chunk in mock_llm_client.generate_stream("Test prompt"):
            chunks.append(chunk)
        
        assert chunks == ["Hello", " ", "world", "!"]
        mock_llm_client.generate_stream.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_embeddings(self, mock_llm_client):
        """Test embeddings generation"""
        texts = ["Hello", "World"]
        expected_embeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
        
        mock_llm_client.generate_embeddings.return_value = expected_embeddings
        
        embeddings = await mock_llm_client.generate_embeddings(texts)
        
        assert embeddings == expected_embeddings
        mock_llm_client.generate_embeddings.assert_called_once_with(texts)
    
    def test_get_available_providers(self, mock_llm_client):
        """Test getting available providers"""
        providers = mock_llm_client.get_available_providers()
        
        assert providers == ["mock"]
        mock_llm_client.get_available_providers.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_validate_providers(self, mock_llm_client):
        """Test provider validation"""
        validation_results = await mock_llm_client.validate_providers()
        
        assert validation_results == {"mock": True}
        mock_llm_client.validate_providers.assert_called_once()
    
    def test_get_provider_info(self, mock_llm_client):
        """Test getting provider information"""
        mock_info = {
            "name": "mock",
            "model": "test-model",
            "capabilities": ["text_generation"],
            "metrics": {"requests": 0}
        }
        mock_llm_client.get_provider_info.return_value = mock_info
        
        info = mock_llm_client.get_provider_info("mock")
        
        assert info == mock_info
        mock_llm_client.get_provider_info.assert_called_once_with("mock")
    
    def test_get_usage_stats(self, mock_llm_client):
        """Test getting usage statistics"""
        mock_stats = {
            "total_requests": 10,
            "successful_requests": 9,
            "failed_requests": 1,
            "total_tokens": 1000,
            "total_cost": 0.05
        }
        mock_llm_client.get_usage_stats.return_value = mock_stats
        
        stats = mock_llm_client.get_usage_stats()
        
        assert stats == mock_stats
        mock_llm_client.get_usage_stats.assert_called_once()


class TestLLMClientCaching:
    """Test LLM client caching functionality"""
    
    @pytest.mark.asyncio
    async def test_cache_hit(self):
        """Test cache hit scenario"""
        # This would require a more complex setup with actual client
        # For now, we'll test the cache key generation logic
        pass
    
    @pytest.mark.asyncio
    async def test_cache_miss(self):
        """Test cache miss scenario"""
        pass
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self):
        """Test cache expiration"""
        pass


class TestLLMClientFallback:
    """Test LLM client fallback functionality"""
    
    @pytest.mark.asyncio
    async def test_fallback_on_provider_failure(self):
        """Test fallback when primary provider fails"""
        # Mock primary provider failure and fallback success
        pass
    
    @pytest.mark.asyncio
    async def test_fallback_disabled(self):
        """Test behavior when fallback is disabled"""
        pass
    
    @pytest.mark.asyncio
    async def test_all_providers_fail(self):
        """Test behavior when all providers fail"""
        pass


class TestLLMClientErrorHandling:
    """Test LLM client error handling"""
    
    @pytest.mark.asyncio
    async def test_rate_limit_error(self):
        """Test rate limit error handling"""
        mock_client = MagicMock()
        mock_client.generate_response = AsyncMock(
            side_effect=LLMRateLimitError("Rate limited", "test-provider")
        )
        
        with pytest.raises(LLMRateLimitError):
            await mock_client.generate_response("test")
    
    @pytest.mark.asyncio
    async def test_authentication_error(self):
        """Test authentication error handling"""
        mock_client = MagicMock()
        mock_client.generate_response = AsyncMock(
            side_effect=LLMAuthenticationError("Invalid API key", "test-provider")
        )
        
        with pytest.raises(LLMAuthenticationError):
            await mock_client.generate_response("test")
    
    @pytest.mark.asyncio
    async def test_provider_error(self):
        """Test general provider error handling"""
        mock_client = MagicMock()
        mock_client.generate_response = AsyncMock(
            side_effect=LLMProviderError("Provider error", "test-provider")
        )
        
        with pytest.raises(LLMProviderError):
            await mock_client.generate_response("test")
    
    @pytest.mark.asyncio
    async def test_timeout_error(self):
        """Test timeout error handling"""
        mock_client = MagicMock()
        mock_client.generate_response = AsyncMock(
            side_effect=asyncio.TimeoutError("Request timed out")
        )
        
        with pytest.raises(asyncio.TimeoutError):
            await mock_client.generate_response("test")


class TestLLMClientMetrics:
    """Test LLM client metrics tracking"""
    
    def test_metrics_initialization(self):
        """Test metrics are properly initialized"""
        with patch('ccw.llm.client.config'), \
             patch('ccw.llm.client.LLMClient._initialize_providers'):
            client = LLMClient()
            
            assert client.usage_stats["total_requests"] == 0
            assert client.usage_stats["successful_requests"] == 0
            assert client.usage_stats["failed_requests"] == 0
            assert client.usage_stats["total_tokens"] == 0
            assert client.usage_stats["total_cost"] == 0.0
            assert isinstance(client.usage_stats["provider_usage"], dict)
    
    @pytest.mark.asyncio
    async def test_metrics_tracking_success(self):
        """Test metrics tracking on successful request"""
        # This would require integration with actual client
        pass
    
    @pytest.mark.asyncio
    async def test_metrics_tracking_failure(self):
        """Test metrics tracking on failed request"""
        # This would require integration with actual client
        pass


class TestLLMClientConfiguration:
    """Test LLM client configuration handling"""
    
    @patch('ccw.llm.client.config')
    def test_config_loading(self, mock_config):
        """Test configuration loading"""
        mock_config.get_section.return_value = {
            "provider": "openai",
            "model": "gpt-4",
            "api_key": "test-key",
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        with patch('ccw.llm.client.LLMClient._initialize_providers'):
            client = LLMClient()
            # Test that configuration is properly loaded
            # This would require access to internal configuration
    
    def test_invalid_configuration(self):
        """Test handling of invalid configuration"""
        # Test various invalid configuration scenarios
        pass
