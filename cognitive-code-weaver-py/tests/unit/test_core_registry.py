"""
Unit tests for Agent Registry functionality

Tests agent registration, discovery, lifecycle management,
and dependency resolution.
"""

import pytest
from unittest.mock import MagicMock

from ccw.core.registry import Agent<PERSON>eg<PERSON>ry, register_agent, agent_registry
from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus


class TestAgentRegistry:
    """Test AgentRegistry functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.registry = AgentRegistry()
    
    def test_registry_initialization(self):
        """Test registry initialization"""
        assert len(self.registry.agents) == 0
        assert len(self.registry.capabilities) == 0
    
    def test_register_agent_class(self):
        """Test registering agent class"""
        @register_agent("test_agent", capabilities=["test_cap"])
        class TestAgent(Agent):
            def _define_capabilities(self):
                return ["test_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id="test_agent",
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        # Register with our test registry
        self.registry.register_agent("test_agent", TestAgent, ["test_cap"])
        
        assert "test_agent" in self.registry.agents
        assert "test_cap" in self.registry.capabilities
        assert self.registry.capabilities["test_cap"] == ["test_agent"]
    
    def test_register_agent_instance(self):
        """Test registering agent instance"""
        class TestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "instance_agent"
            
            def _define_capabilities(self):
                return ["instance_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent_instance = TestAgent()
        self.registry.register_agent_instance(agent_instance)
        
        assert "instance_agent" in self.registry.agents
        assert "instance_cap" in self.registry.capabilities
        assert isinstance(self.registry.agents["instance_agent"], TestAgent)
    
    def test_get_agent(self):
        """Test getting agent by ID"""
        class TestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "get_test_agent"
            
            def _define_capabilities(self):
                return ["get_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent = TestAgent()
        self.registry.register_agent_instance(agent)
        
        retrieved_agent = self.registry.get_agent("get_test_agent")
        assert retrieved_agent is agent
        
        # Test non-existent agent
        assert self.registry.get_agent("non_existent") is None
    
    def test_get_agents_by_capability(self):
        """Test getting agents by capability"""
        class Agent1(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "agent1"
            
            def _define_capabilities(self):
                return ["cap1", "cap2"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        class Agent2(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "agent2"
            
            def _define_capabilities(self):
                return ["cap2", "cap3"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent1 = Agent1()
        agent2 = Agent2()
        
        self.registry.register_agent_instance(agent1)
        self.registry.register_agent_instance(agent2)
        
        # Test single capability
        cap1_agents = self.registry.get_agents_by_capability("cap1")
        assert len(cap1_agents) == 1
        assert cap1_agents[0].agent_id == "agent1"
        
        # Test shared capability
        cap2_agents = self.registry.get_agents_by_capability("cap2")
        assert len(cap2_agents) == 2
        agent_ids = [agent.agent_id for agent in cap2_agents]
        assert "agent1" in agent_ids
        assert "agent2" in agent_ids
        
        # Test non-existent capability
        no_agents = self.registry.get_agents_by_capability("non_existent")
        assert len(no_agents) == 0
    
    def test_list_agents(self):
        """Test listing all agents"""
        class TestAgent(Agent):
            def __init__(self, agent_id):
                super().__init__()
                self.agent_id = agent_id
            
            def _define_capabilities(self):
                return ["test_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent1 = TestAgent("agent1")
        agent2 = TestAgent("agent2")
        
        self.registry.register_agent_instance(agent1)
        self.registry.register_agent_instance(agent2)
        
        agents_info = self.registry.list_agents()
        
        assert len(agents_info) == 2
        agent_ids = [info["agent_id"] for info in agents_info]
        assert "agent1" in agent_ids
        assert "agent2" in agent_ids
        
        # Check info structure
        for info in agents_info:
            assert "agent_id" in info
            assert "capabilities" in info
            assert "is_running" in info
    
    def test_list_capabilities(self):
        """Test listing all capabilities"""
        class TestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "cap_test_agent"
            
            def _define_capabilities(self):
                return ["cap1", "cap2", "cap3"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent = TestAgent()
        self.registry.register_agent_instance(agent)
        
        capabilities = self.registry.list_capabilities()
        
        assert "cap1" in capabilities
        assert "cap2" in capabilities
        assert "cap3" in capabilities
        assert capabilities["cap1"] == ["cap_test_agent"]
    
    def test_unregister_agent(self):
        """Test unregistering an agent"""
        class TestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "unregister_test"
            
            def _define_capabilities(self):
                return ["unregister_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent = TestAgent()
        self.registry.register_agent_instance(agent)
        
        # Verify registration
        assert "unregister_test" in self.registry.agents
        assert "unregister_cap" in self.registry.capabilities
        
        # Unregister
        self.registry.unregister_agent("unregister_test")
        
        # Verify unregistration
        assert "unregister_test" not in self.registry.agents
        assert "unregister_cap" not in self.registry.capabilities
    
    def test_clear_registry(self):
        """Test clearing the registry"""
        class TestAgent(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "clear_test"
            
            def _define_capabilities(self):
                return ["clear_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent = TestAgent()
        self.registry.register_agent_instance(agent)
        
        # Verify registration
        assert len(self.registry.agents) == 1
        assert len(self.registry.capabilities) == 1
        
        # Clear registry
        self.registry.clear()
        
        # Verify clearing
        assert len(self.registry.agents) == 0
        assert len(self.registry.capabilities) == 0
    
    @pytest.mark.asyncio
    async def test_startup_shutdown_all(self):
        """Test starting up and shutting down all agents"""
        class TestAgent(Agent):
            def __init__(self, agent_id):
                super().__init__()
                self.agent_id = agent_id
            
            def _define_capabilities(self):
                return ["startup_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent1 = TestAgent("startup_agent1")
        agent2 = TestAgent("startup_agent2")
        
        self.registry.register_agent_instance(agent1)
        self.registry.register_agent_instance(agent2)
        
        # Test startup all
        await self.registry.startup_all()
        
        assert agent1.is_running is True
        assert agent2.is_running is True
        
        # Test shutdown all
        await self.registry.shutdown_all()
        
        assert agent1.is_running is False
        assert agent2.is_running is False


class TestRegisterDecorator:
    """Test the register_agent decorator"""
    
    def test_register_decorator_basic(self):
        """Test basic decorator usage"""
        @register_agent("decorated_agent", capabilities=["decorated_cap"])
        class DecoratedAgent(Agent):
            def _define_capabilities(self):
                return ["decorated_cap"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id="decorated_agent",
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        # Check that the agent was registered in the global registry
        # Note: This test depends on the global registry state
        # In a real test, we might want to use a separate registry
        assert hasattr(DecoratedAgent, '_ccw_agent_id')
        assert hasattr(DecoratedAgent, '_ccw_capabilities')
        assert DecoratedAgent._ccw_agent_id == "decorated_agent"
        assert DecoratedAgent._ccw_capabilities == ["decorated_cap"]
    
    def test_register_decorator_no_capabilities(self):
        """Test decorator without capabilities"""
        @register_agent("no_cap_agent")
        class NoCapsAgent(Agent):
            def _define_capabilities(self):
                return []
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id="no_cap_agent",
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        assert NoCapsAgent._ccw_agent_id == "no_cap_agent"
        assert NoCapsAgent._ccw_capabilities == []


class TestRegistryErrorHandling:
    """Test registry error handling"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.registry = AgentRegistry()
    
    def test_register_duplicate_agent(self):
        """Test registering duplicate agent IDs"""
        class TestAgent1(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "duplicate_id"
            
            def _define_capabilities(self):
                return ["cap1"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        class TestAgent2(Agent):
            def __init__(self):
                super().__init__()
                self.agent_id = "duplicate_id"
            
            def _define_capabilities(self):
                return ["cap2"]
            
            async def execute(self, task, context):
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED
                )
        
        agent1 = TestAgent1()
        agent2 = TestAgent2()
        
        # Register first agent
        self.registry.register_agent_instance(agent1)
        
        # Registering second agent with same ID should raise an error
        with pytest.raises(ValueError, match="already registered"):
            self.registry.register_agent_instance(agent2)
    
    def test_unregister_nonexistent_agent(self):
        """Test unregistering non-existent agent"""
        # Should not raise an error
        self.registry.unregister_agent("non_existent_agent")
        
        # Registry should still be empty
        assert len(self.registry.agents) == 0
