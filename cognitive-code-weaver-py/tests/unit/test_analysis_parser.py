"""
Unit tests for Code Analysis Parser functionality

Tests multi-language parsing, language detection, symbol extraction,
and error handling in the code parser.
"""

import pytest
from pathlib import Path

from ccw.analysis.parser import (
    CodeParser, LanguageType, ParseResult, TreeSitterParser,
    LanguageParser
)


class TestLanguageType:
    """Test LanguageType enum"""
    
    def test_language_values(self):
        """Test language type values"""
        assert LanguageType.PYTHON.value == "python"
        assert LanguageType.JAVASCRIPT.value == "javascript"
        assert LanguageType.TYPESCRIPT.value == "typescript"
        assert LanguageType.JAVA.value == "java"
        assert LanguageType.C.value == "c"
        assert LanguageType.CPP.value == "cpp"
        assert LanguageType.UNKNOWN.value == "unknown"


class TestParseResult:
    """Test ParseResult functionality"""
    
    def test_parse_result_creation(self):
        """Test parse result creation"""
        result = ParseResult(
            file_path="/test/file.py",
            language=LanguageType.PYTHON,
            content="def test(): pass"
        )
        
        assert result.file_path == "/test/file.py"
        assert result.language == LanguageType.PYTHON
        assert result.content == "def test(): pass"
        assert result.functions == []
        assert result.classes == []
        assert result.imports == []
        assert result.variables == []
        assert result.errors == []
    
    def test_parse_result_with_data(self):
        """Test parse result with extracted data"""
        result = ParseResult(
            file_path="/test/file.py",
            language=LanguageType.PYTHON,
            content="def test(): pass",
            functions=[{"name": "test", "line": 1}],
            classes=[{"name": "TestClass", "line": 5}],
            imports=["os", "sys"],
            variables=[{"name": "var", "line": 10}],
            errors=["Syntax error on line 15"]
        )
        
        assert len(result.functions) == 1
        assert result.functions[0]["name"] == "test"
        assert len(result.classes) == 1
        assert result.classes[0]["name"] == "TestClass"
        assert len(result.imports) == 2
        assert "os" in result.imports
        assert len(result.variables) == 1
        assert len(result.errors) == 1


class TestCodeParser:
    """Test CodeParser functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.parser = CodeParser()
    
    def test_parser_initialization(self):
        """Test parser initialization"""
        assert isinstance(self.parser.parsers, dict)
        assert len(self.parser.parsers) > 0
        
        # Check that some expected parsers are initialized
        expected_languages = [
            LanguageType.PYTHON,
            LanguageType.JAVASCRIPT,
            LanguageType.JAVA
        ]
        
        for lang in expected_languages:
            assert lang in self.parser.parsers
    
    def test_detect_language_by_extension(self):
        """Test language detection by file extension"""
        test_cases = [
            ("test.py", LanguageType.PYTHON),
            ("test.js", LanguageType.JAVASCRIPT),
            ("test.ts", LanguageType.TYPESCRIPT),
            ("test.java", LanguageType.JAVA),
            ("test.c", LanguageType.C),
            ("test.cpp", LanguageType.CPP),
            ("test.unknown", LanguageType.UNKNOWN)
        ]
        
        for filename, expected_lang in test_cases:
            detected_lang = self.parser.detect_language(filename)
            assert detected_lang == expected_lang
    
    def test_detect_language_by_content(self):
        """Test language detection by content"""
        test_cases = [
            ("def function(): pass", LanguageType.PYTHON),
            ("function test() { return true; }", LanguageType.JAVASCRIPT),
            ("public class Test { }", LanguageType.JAVA),
            ("import os\nprint('hello')", LanguageType.PYTHON)
        ]
        
        for content, expected_lang in test_cases:
            detected_lang = self.parser.detect_language("unknown.txt", content)
            assert detected_lang == expected_lang
    
    def test_parse_content_python(self, sample_python_code):
        """Test parsing Python content"""
        result = self.parser.parse_content(
            sample_python_code,
            LanguageType.PYTHON,
            "test.py"
        )
        
        assert result.language == LanguageType.PYTHON
        assert result.file_path == "test.py"
        assert len(result.functions) > 0
        assert len(result.classes) > 0
        assert len(result.imports) > 0
        
        # Check specific functions
        function_names = [func["name"] for func in result.functions]
        assert "fibonacci" in function_names
        assert "add" in function_names
        assert "multiply" in function_names
        
        # Check specific classes
        class_names = [cls["name"] for cls in result.classes]
        assert "Calculator" in class_names
        
        # Check imports
        assert "math" in result.imports
        assert "collections" in result.imports
    
    def test_parse_content_javascript(self, sample_javascript_code):
        """Test parsing JavaScript content"""
        result = self.parser.parse_content(
            sample_javascript_code,
            LanguageType.JAVASCRIPT,
            "test.js"
        )
        
        assert result.language == LanguageType.JAVASCRIPT
        assert result.file_path == "test.js"
        assert len(result.functions) > 0
        assert len(result.classes) > 0
        assert len(result.imports) > 0
        
        # Check specific functions
        function_names = [func["name"] for func in result.functions]
        assert "calculateArea" in function_names
        assert "greet" in function_names
        
        # Check specific classes
        class_names = [cls["name"] for cls in result.classes]
        assert "Rectangle" in class_names
    
    def test_parse_file(self, temp_dir, sample_python_code):
        """Test parsing a file"""
        # Create test file
        test_file = temp_dir / "test_parse.py"
        test_file.write_text(sample_python_code)
        
        result = self.parser.parse_file(str(test_file))
        
        assert result.language == LanguageType.PYTHON
        assert result.file_path == str(test_file)
        assert len(result.functions) > 0
        assert len(result.classes) > 0
        assert len(result.errors) == 0
    
    def test_parse_nonexistent_file(self):
        """Test parsing non-existent file"""
        result = self.parser.parse_file("/nonexistent/file.py")
        
        assert result.language == LanguageType.UNKNOWN
        assert result.content == ""
        assert len(result.errors) > 0
        assert "Failed to parse file" in result.errors[0]
    
    def test_get_supported_languages(self):
        """Test getting supported languages"""
        languages = self.parser.get_supported_languages()
        
        assert isinstance(languages, list)
        assert len(languages) > 0
        assert LanguageType.PYTHON in languages
        assert LanguageType.JAVASCRIPT in languages
    
    def test_get_supported_extensions(self):
        """Test getting supported file extensions"""
        extensions = self.parser.get_supported_extensions()
        
        assert isinstance(extensions, list)
        assert len(extensions) > 0
        assert ".py" in extensions
        assert ".js" in extensions
        assert ".java" in extensions


class TestTreeSitterParser:
    """Test TreeSitterParser functionality"""
    
    def test_parser_initialization(self):
        """Test TreeSitter parser initialization"""
        parser = TreeSitterParser(LanguageType.PYTHON)
        
        assert parser.language == LanguageType.PYTHON
        # Note: TreeSitter might not be available in test environment
        # so we just test that initialization doesn't crash
    
    def test_get_file_extensions(self):
        """Test getting file extensions for language"""
        parser = TreeSitterParser(LanguageType.PYTHON)
        extensions = parser.get_file_extensions()
        
        assert ".py" in extensions
        assert ".pyw" in extensions
    
    def test_extract_symbols_python(self, sample_python_code):
        """Test symbol extraction from Python code"""
        parser = TreeSitterParser(LanguageType.PYTHON)
        symbols = parser.extract_symbols(sample_python_code)
        
        assert isinstance(symbols, list)
        # Symbols should include functions and classes
        symbol_names = [symbol["name"] for symbol in symbols]
        assert "fibonacci" in symbol_names
        assert "Calculator" in symbol_names
    
    def test_extract_imports_python(self, sample_python_code):
        """Test import extraction from Python code"""
        parser = TreeSitterParser(LanguageType.PYTHON)
        imports = parser.extract_imports(sample_python_code)
        
        assert isinstance(imports, list)
        assert "math" in imports
        assert "collections" in imports
    
    def test_extract_symbols_javascript(self, sample_javascript_code):
        """Test symbol extraction from JavaScript code"""
        parser = TreeSitterParser(LanguageType.JAVASCRIPT)
        symbols = parser.extract_symbols(sample_javascript_code)
        
        assert isinstance(symbols, list)
        symbol_names = [symbol["name"] for symbol in symbols]
        assert "calculateArea" in symbol_names
        assert "Rectangle" in symbol_names
    
    def test_extract_imports_javascript(self, sample_javascript_code):
        """Test import extraction from JavaScript code"""
        parser = TreeSitterParser(LanguageType.JAVASCRIPT)
        imports = parser.extract_imports(sample_javascript_code)
        
        assert isinstance(imports, list)
        # Should find both ES6 imports and require statements
        assert any("react" in imp for imp in imports)
        assert any("express" in imp for imp in imports)


class TestParserErrorHandling:
    """Test parser error handling"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.parser = CodeParser()
    
    def test_parse_invalid_python_syntax(self):
        """Test parsing Python code with syntax errors"""
        invalid_code = """
def invalid_function(
    # Missing closing parenthesis and colon
    print("This is invalid")
"""
        
        result = self.parser.parse_content(
            invalid_code,
            LanguageType.PYTHON,
            "invalid.py"
        )
        
        # Should still return a result, possibly with errors
        assert result.language == LanguageType.PYTHON
        assert result.file_path == "invalid.py"
        # Errors might be captured depending on parser implementation
    
    def test_parse_empty_content(self):
        """Test parsing empty content"""
        result = self.parser.parse_content("", LanguageType.PYTHON, "empty.py")
        
        assert result.language == LanguageType.PYTHON
        assert result.content == ""
        assert len(result.functions) == 0
        assert len(result.classes) == 0
    
    def test_parse_unsupported_language(self):
        """Test parsing unsupported language"""
        result = self.parser.parse_content(
            "some content",
            LanguageType.UNKNOWN,
            "unknown.txt"
        )
        
        assert result.language == LanguageType.UNKNOWN
        # Should handle gracefully, possibly with errors
    
    def test_parse_binary_file(self, temp_dir):
        """Test parsing binary file"""
        # Create a binary file
        binary_file = temp_dir / "binary.bin"
        binary_file.write_bytes(b'\x00\x01\x02\x03\x04\x05')
        
        result = self.parser.parse_file(str(binary_file))
        
        # Should handle gracefully
        assert result.file_path == str(binary_file)
        # Might have errors or be detected as unknown language


class TestParserPerformance:
    """Test parser performance characteristics"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.parser = CodeParser()
    
    def test_parse_large_file(self):
        """Test parsing a large file"""
        # Generate large Python code
        large_code = "\n".join([
            f"def function_{i}():\n    return {i}"
            for i in range(1000)
        ])
        
        result = self.parser.parse_content(
            large_code,
            LanguageType.PYTHON,
            "large.py"
        )
        
        assert result.language == LanguageType.PYTHON
        assert len(result.functions) == 1000
        # Should complete in reasonable time
    
    @pytest.mark.slow
    def test_parse_multiple_files_performance(self, temp_dir, sample_python_code):
        """Test parsing multiple files for performance"""
        import time
        
        # Create multiple test files
        files = []
        for i in range(10):
            test_file = temp_dir / f"test_{i}.py"
            test_file.write_text(sample_python_code)
            files.append(str(test_file))
        
        # Time the parsing
        start_time = time.time()
        
        results = []
        for file_path in files:
            result = self.parser.parse_file(file_path)
            results.append(result)
        
        end_time = time.time()
        
        # Verify results
        assert len(results) == 10
        for result in results:
            assert result.language == LanguageType.PYTHON
            assert len(result.functions) > 0
        
        # Performance check (should complete in reasonable time)
        total_time = end_time - start_time
        assert total_time < 10.0  # Should complete within 10 seconds
