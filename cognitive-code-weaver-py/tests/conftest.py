"""
Pytest configuration and shared fixtures for Cognitive Code Weaver tests

Provides common test fixtures, configuration, and utilities used across
all test modules.
"""

import asyncio
import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock
from typing import Dict, Any, Generator

# Import CCW modules
from ccw.core.config import init_config, config
from ccw.core.registry import agent_registry
from ccw.core.message_bus import message_bus
from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus
from ccw.llm.client import LLMClient
from ccw.analysis.workspace import WorkspaceAnalyzer


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def test_config():
    """Provide test configuration"""
    return {
        "core": {
            "log_level": "DEBUG",
            "max_workers": 2
        },
        "llm": {
            "provider": "mock",
            "model": "test-model",
            "api_key": "test-key",
            "timeout": 10.0
        },
        "agents": {
            "master_agent": {
                "enabled": True,
                "max_retries": 2
            },
            "cognitive_agent": {
                "enabled": True,
                "analysis_depth": "medium"
            }
        }
    }


@pytest.fixture
async def initialized_system(test_config, temp_dir):
    """Initialize the CCW system for testing"""
    # Create test config file
    config_file = temp_dir / "test_config.yaml"
    import yaml
    with open(config_file, 'w') as f:
        yaml.dump(test_config, f)
    
    # Initialize system
    init_config(config_file=str(config_file))
    
    # Start message bus
    await message_bus.start()
    
    yield
    
    # Cleanup
    await message_bus.stop()
    agent_registry.clear()


@pytest.fixture
def mock_llm_client():
    """Mock LLM client for testing"""
    mock_client = MagicMock(spec=LLMClient)
    
    # Mock response
    mock_response = MagicMock()
    mock_response.content = "Test response from mock LLM"
    mock_response.model = "test-model"
    mock_response.provider = "mock"
    mock_response.usage = {"total_tokens": 100}
    mock_response.response_time = 0.5
    mock_response.metadata = {"cost": 0.001}
    
    mock_client.generate_response = AsyncMock(return_value=mock_response)
    mock_client.generate_stream = AsyncMock()
    mock_client.get_available_providers = MagicMock(return_value=["mock"])
    mock_client.validate_providers = AsyncMock(return_value={"mock": True})
    
    return mock_client


@pytest.fixture
def sample_python_code():
    """Sample Python code for testing"""
    return '''
def fibonacci(n):
    """Calculate fibonacci number recursively"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    """A simple calculator class"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """Add two numbers"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers"""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result

# Global variable
PI = 3.14159

# Import statements
import math
from collections import defaultdict
'''


@pytest.fixture
def sample_javascript_code():
    """Sample JavaScript code for testing"""
    return '''
function calculateArea(radius) {
    return Math.PI * radius * radius;
}

const greet = (name) => {
    return `Hello, ${name}!`;
};

class Rectangle {
    constructor(width, height) {
        this.width = width;
        this.height = height;
    }
    
    getArea() {
        return this.width * this.height;
    }
    
    getPerimeter() {
        return 2 * (this.width + this.height);
    }
}

// Import statements
import { Component } from 'react';
const express = require('express');

// Global variables
const API_URL = 'https://api.example.com';
let globalCounter = 0;
'''


@pytest.fixture
def sample_workspace(temp_dir, sample_python_code, sample_javascript_code):
    """Create a sample workspace with multiple files"""
    workspace_dir = temp_dir / "sample_workspace"
    workspace_dir.mkdir()
    
    # Python files
    (workspace_dir / "main.py").write_text(sample_python_code)
    (workspace_dir / "utils.py").write_text('''
def helper_function(data):
    """Helper function for data processing"""
    return [item * 2 for item in data if item > 0]

def format_output(result):
    """Format output for display"""
    return f"Result: {result}"
''')
    
    # JavaScript files
    (workspace_dir / "app.js").write_text(sample_javascript_code)
    (workspace_dir / "helpers.js").write_text('''
function validateInput(input) {
    return input !== null && input !== undefined;
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}
''')
    
    # Create subdirectory
    subdir = workspace_dir / "modules"
    subdir.mkdir()
    (subdir / "module1.py").write_text('''
class DataProcessor:
    def process(self, data):
        return data.upper()
''')
    
    return workspace_dir


@pytest.fixture
def mock_agent():
    """Create a mock agent for testing"""
    class MockAgent(Agent):
        def __init__(self):
            super().__init__()
            self.agent_id = "mock_agent"
            self.call_count = 0
            self.last_task = None
            self.last_context = None
        
        def _define_capabilities(self):
            return ["mock_capability", "test_capability"]
        
        async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
            self.call_count += 1
            self.last_task = task
            self.last_context = context
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={"mock_result": "success", "call_count": self.call_count},
                confidence=0.9
            )
    
    return MockAgent()


@pytest.fixture
def agent_task():
    """Create a sample agent task"""
    return AgentTask(
        task_type="test_task",
        description="Test task for unit testing",
        data={"test_param": "test_value"},
        priority=1
    )


@pytest.fixture
def agent_context():
    """Create a sample agent context"""
    return AgentContext(
        session_id="test_session",
        user_query="Test query",
        workspace_path="/test/workspace",
        metadata={"test_meta": "test_value"}
    )


@pytest.fixture
def mock_workspace_analyzer():
    """Mock workspace analyzer for testing"""
    mock_analyzer = MagicMock(spec=WorkspaceAnalyzer)
    
    # Mock analysis result
    mock_result = MagicMock()
    mock_result.workspace_path = "/test/workspace"
    mock_result.total_files = 10
    mock_result.analyzed_files = 8
    mock_result.skipped_files = 2
    mock_result.errors = []
    mock_result.workspace_metrics = {
        "total_lines_of_code": 1000,
        "average_complexity": 5.2,
        "total_functions": 25
    }
    mock_result.quality_report = {
        "overall_grade": "B",
        "average_maintainability": 75.0
    }
    
    mock_analyzer.analyze_workspace = MagicMock(return_value=mock_result)
    mock_analyzer.analyze_file = MagicMock(return_value={
        "file_path": "/test/file.py",
        "language": "python",
        "metrics": {"complexity": 3}
    })
    
    return mock_analyzer


@pytest.fixture
def mock_parse_result():
    """Mock parse result for testing"""
    from ccw.analysis.parser import ParseResult, LanguageType
    
    return ParseResult(
        file_path="/test/file.py",
        language=LanguageType.PYTHON,
        content="def test(): pass",
        functions=[{"name": "test", "line": 1}],
        classes=[],
        imports=[],
        variables=[],
        errors=[]
    )


# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def create_test_file(directory: Path, filename: str, content: str) -> Path:
        """Create a test file with given content"""
        file_path = directory / filename
        file_path.write_text(content)
        return file_path
    
    @staticmethod
    def assert_agent_result(result: AgentResult, expected_status: AgentStatus = AgentStatus.COMPLETED):
        """Assert agent result properties"""
        assert result is not None
        assert result.status == expected_status
        assert result.agent_id is not None
        assert result.task_id is not None
    
    @staticmethod
    async def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1):
        """Wait for a condition to become true"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            await asyncio.sleep(interval)
        
        return False


@pytest.fixture
def test_utils():
    """Provide test utilities"""
    return TestUtils


# Pytest configuration
def pytest_configure(config):
    """Configure pytest"""
    # Add custom markers
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "agent: Agent behavior tests")
    config.addinivalue_line("markers", "llm: LLM integration tests")
    config.addinivalue_line("markers", "analysis: Code analysis tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    # Add markers based on test file location
    for item in items:
        # Add unit marker for unit tests
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker for integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add agent marker for agent tests
        if "agent" in str(item.fspath):
            item.add_marker(pytest.mark.agent)
        
        # Add slow marker for tests that might be slow
        if any(keyword in item.name.lower() for keyword in ["workspace", "large", "performance"]):
            item.add_marker(pytest.mark.slow)
