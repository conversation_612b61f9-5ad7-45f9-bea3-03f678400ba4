"""
Comprehensive Test Reporting System

Provides detailed test reporting, metrics collection, trend analysis,
and integration with external reporting tools.
"""

import json
import time
import sqlite3
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import xml.etree.ElementTree as ET

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn


class TestStatus(Enum):
    """Test execution status"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class ReportFormat(Enum):
    """Report output formats"""
    JSON = "json"
    HTML = "html"
    XML = "xml"
    MARKDOWN = "markdown"
    CONSOLE = "console"


@dataclass
class TestResult:
    """Individual test result"""
    test_id: str
    test_name: str
    test_file: str
    status: TestStatus
    duration: float
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    assertions: int = 0
    coverage: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class TestSuiteResult:
    """Test suite execution result"""
    suite_name: str
    start_time: float
    end_time: float
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    error_tests: int
    total_duration: float
    coverage_percentage: float
    test_results: List[TestResult]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100
    
    @property
    def average_duration(self) -> float:
        """Calculate average test duration"""
        if self.total_tests == 0:
            return 0.0
        return self.total_duration / self.total_tests


class TestMetricsCollector:
    """Collects and aggregates test metrics"""
    
    def __init__(self, db_path: Optional[Path] = None):
        self.db_path = db_path or Path.cwd() / "test_metrics.db"
        self._init_database()
    
    def _init_database(self):
        """Initialize metrics database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS test_runs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    suite_name TEXT,
                    total_tests INTEGER,
                    passed_tests INTEGER,
                    failed_tests INTEGER,
                    skipped_tests INTEGER,
                    total_duration REAL,
                    coverage_percentage REAL,
                    commit_hash TEXT,
                    branch_name TEXT,
                    metadata TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS test_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id INTEGER,
                    test_id TEXT,
                    test_name TEXT,
                    test_file TEXT,
                    status TEXT,
                    duration REAL,
                    error_message TEXT,
                    assertions INTEGER,
                    coverage REAL,
                    FOREIGN KEY (run_id) REFERENCES test_runs (id)
                )
            """)
    
    def record_test_run(self, suite_result: TestSuiteResult, 
                       commit_hash: str = "", branch_name: str = "") -> int:
        """Record a test run in the database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO test_runs (
                    timestamp, suite_name, total_tests, passed_tests, 
                    failed_tests, skipped_tests, total_duration, 
                    coverage_percentage, commit_hash, branch_name, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                suite_result.start_time,
                suite_result.suite_name,
                suite_result.total_tests,
                suite_result.passed_tests,
                suite_result.failed_tests,
                suite_result.skipped_tests,
                suite_result.total_duration,
                suite_result.coverage_percentage,
                commit_hash,
                branch_name,
                json.dumps(suite_result.metadata)
            ))
            
            run_id = cursor.lastrowid
            
            # Record individual test results
            for test_result in suite_result.test_results:
                conn.execute("""
                    INSERT INTO test_results (
                        run_id, test_id, test_name, test_file, status,
                        duration, error_message, assertions, coverage
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    run_id,
                    test_result.test_id,
                    test_result.test_name,
                    test_result.test_file,
                    test_result.status.value,
                    test_result.duration,
                    test_result.error_message,
                    test_result.assertions,
                    test_result.coverage
                ))
            
            return run_id
    
    def get_test_trends(self, days: int = 30) -> Dict[str, Any]:
        """Get test trends over specified days"""
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        
        with sqlite3.connect(self.db_path) as conn:
            # Get daily aggregates
            daily_stats = conn.execute("""
                SELECT 
                    DATE(timestamp, 'unixepoch') as date,
                    AVG(coverage_percentage) as avg_coverage,
                    AVG(total_duration) as avg_duration,
                    AVG(CAST(passed_tests AS FLOAT) / total_tests * 100) as success_rate,
                    COUNT(*) as runs_count
                FROM test_runs 
                WHERE timestamp > ?
                GROUP BY DATE(timestamp, 'unixepoch')
                ORDER BY date
            """, (cutoff_time,)).fetchall()
            
            # Get failure trends
            failure_trends = conn.execute("""
                SELECT 
                    test_name,
                    COUNT(*) as failure_count,
                    AVG(duration) as avg_duration
                FROM test_results tr
                JOIN test_runs run ON tr.run_id = run.id
                WHERE run.timestamp > ? AND tr.status = 'failed'
                GROUP BY test_name
                ORDER BY failure_count DESC
                LIMIT 10
            """, (cutoff_time,)).fetchall()
            
            return {
                "daily_stats": [
                    {
                        "date": row[0],
                        "avg_coverage": row[1],
                        "avg_duration": row[2],
                        "success_rate": row[3],
                        "runs_count": row[4]
                    }
                    for row in daily_stats
                ],
                "top_failures": [
                    {
                        "test_name": row[0],
                        "failure_count": row[1],
                        "avg_duration": row[2]
                    }
                    for row in failure_trends
                ]
            }
    
    def get_performance_regression(self, test_name: str, days: int = 7) -> Dict[str, Any]:
        """Detect performance regression for specific test"""
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        
        with sqlite3.connect(self.db_path) as conn:
            results = conn.execute("""
                SELECT duration, run.timestamp
                FROM test_results tr
                JOIN test_runs run ON tr.run_id = run.id
                WHERE tr.test_name = ? AND run.timestamp > ?
                ORDER BY run.timestamp
            """, (test_name, cutoff_time)).fetchall()
            
            if len(results) < 2:
                return {"regression_detected": False, "reason": "insufficient_data"}
            
            durations = [row[0] for row in results]
            recent_avg = sum(durations[-3:]) / min(3, len(durations))
            baseline_avg = sum(durations[:-3]) / max(1, len(durations) - 3)
            
            regression_threshold = 1.5  # 50% increase
            regression_detected = recent_avg > baseline_avg * regression_threshold
            
            return {
                "regression_detected": regression_detected,
                "baseline_avg": baseline_avg,
                "recent_avg": recent_avg,
                "regression_factor": recent_avg / baseline_avg if baseline_avg > 0 else 0,
                "data_points": len(results)
            }


class TestReporter:
    """Comprehensive test reporting system"""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or Path.cwd() / "test_reports"
        self.output_dir.mkdir(exist_ok=True)
        self.console = Console()
        self.metrics_collector = TestMetricsCollector()
    
    def generate_report(self, suite_result: TestSuiteResult, 
                       format: ReportFormat = ReportFormat.HTML,
                       include_trends: bool = True) -> Path:
        """Generate comprehensive test report"""
        timestamp = datetime.fromtimestamp(suite_result.start_time)
        
        if format == ReportFormat.JSON:
            return self._generate_json_report(suite_result, timestamp)
        elif format == ReportFormat.HTML:
            return self._generate_html_report(suite_result, timestamp, include_trends)
        elif format == ReportFormat.XML:
            return self._generate_xml_report(suite_result, timestamp)
        elif format == ReportFormat.MARKDOWN:
            return self._generate_markdown_report(suite_result, timestamp, include_trends)
        elif format == ReportFormat.CONSOLE:
            self._generate_console_report(suite_result, include_trends)
            return None
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def _generate_json_report(self, suite_result: TestSuiteResult, timestamp: datetime) -> Path:
        """Generate JSON report"""
        report_data = {
            "timestamp": timestamp.isoformat(),
            "suite_result": asdict(suite_result),
            "summary": {
                "success_rate": suite_result.success_rate,
                "average_duration": suite_result.average_duration,
                "total_assertions": sum(tr.assertions for tr in suite_result.test_results)
            }
        }
        
        report_file = self.output_dir / f"test_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        return report_file
    
    def _generate_html_report(self, suite_result: TestSuiteResult, 
                             timestamp: datetime, include_trends: bool) -> Path:
        """Generate HTML report"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Test Report - {suite_result.suite_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .skipped {{ color: #ffc107; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .status-passed {{ background-color: #d4edda; }}
        .status-failed {{ background-color: #f8d7da; }}
        .status-skipped {{ background-color: #fff3cd; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Report: {suite_result.suite_name}</h1>
        <p>Generated: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Duration: {suite_result.total_duration:.2f} seconds</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 24px;">{suite_result.total_tests}</div>
        </div>
        <div class="metric">
            <h3 class="passed">Passed</h3>
            <div style="font-size: 24px;">{suite_result.passed_tests}</div>
        </div>
        <div class="metric">
            <h3 class="failed">Failed</h3>
            <div style="font-size: 24px;">{suite_result.failed_tests}</div>
        </div>
        <div class="metric">
            <h3 class="skipped">Skipped</h3>
            <div style="font-size: 24px;">{suite_result.skipped_tests}</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div style="font-size: 24px;">{suite_result.success_rate:.1f}%</div>
        </div>
        <div class="metric">
            <h3>Coverage</h3>
            <div style="font-size: 24px;">{suite_result.coverage_percentage:.1f}%</div>
        </div>
    </div>
    
    <h2>Test Results</h2>
    <table>
        <thead>
            <tr>
                <th>Test Name</th>
                <th>File</th>
                <th>Status</th>
                <th>Duration (s)</th>
                <th>Assertions</th>
                <th>Coverage (%)</th>
            </tr>
        </thead>
        <tbody>
"""
        
        for test_result in suite_result.test_results:
            status_class = f"status-{test_result.status.value}"
            html_content += f"""
            <tr class="{status_class}">
                <td>{test_result.test_name}</td>
                <td>{test_result.test_file}</td>
                <td>{test_result.status.value.upper()}</td>
                <td>{test_result.duration:.3f}</td>
                <td>{test_result.assertions}</td>
                <td>{test_result.coverage:.1f}</td>
            </tr>
"""
        
        html_content += """
        </tbody>
    </table>
</body>
</html>
"""
        
        report_file = self.output_dir / f"test_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.html"
        with open(report_file, 'w') as f:
            f.write(html_content)
        
        return report_file
    
    def _generate_xml_report(self, suite_result: TestSuiteResult, timestamp: datetime) -> Path:
        """Generate XML report (JUnit format)"""
        root = ET.Element("testsuite")
        root.set("name", suite_result.suite_name)
        root.set("tests", str(suite_result.total_tests))
        root.set("failures", str(suite_result.failed_tests))
        root.set("errors", str(suite_result.error_tests))
        root.set("skipped", str(suite_result.skipped_tests))
        root.set("time", str(suite_result.total_duration))
        root.set("timestamp", timestamp.isoformat())
        
        for test_result in suite_result.test_results:
            testcase = ET.SubElement(root, "testcase")
            testcase.set("name", test_result.test_name)
            testcase.set("classname", test_result.test_file)
            testcase.set("time", str(test_result.duration))
            
            if test_result.status == TestStatus.FAILED:
                failure = ET.SubElement(testcase, "failure")
                failure.set("message", test_result.error_message or "Test failed")
                if test_result.stack_trace:
                    failure.text = test_result.stack_trace
            elif test_result.status == TestStatus.ERROR:
                error = ET.SubElement(testcase, "error")
                error.set("message", test_result.error_message or "Test error")
                if test_result.stack_trace:
                    error.text = test_result.stack_trace
            elif test_result.status == TestStatus.SKIPPED:
                ET.SubElement(testcase, "skipped")
        
        report_file = self.output_dir / f"test_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.xml"
        tree = ET.ElementTree(root)
        tree.write(report_file, encoding='utf-8', xml_declaration=True)
        
        return report_file
    
    def _generate_markdown_report(self, suite_result: TestSuiteResult, 
                                 timestamp: datetime, include_trends: bool) -> Path:
        """Generate Markdown report"""
        content = f"""# Test Report: {suite_result.suite_name}

**Generated:** {timestamp.strftime('%Y-%m-%d %H:%M:%S')}  
**Duration:** {suite_result.total_duration:.2f} seconds

## Summary

| Metric | Value |
|--------|-------|
| Total Tests | {suite_result.total_tests} |
| Passed | {suite_result.passed_tests} |
| Failed | {suite_result.failed_tests} |
| Skipped | {suite_result.skipped_tests} |
| Success Rate | {suite_result.success_rate:.1f}% |
| Coverage | {suite_result.coverage_percentage:.1f}% |

## Test Results

| Test Name | File | Status | Duration (s) | Assertions |
|-----------|------|--------|--------------|------------|
"""
        
        for test_result in suite_result.test_results:
            status_emoji = {
                TestStatus.PASSED: "✅",
                TestStatus.FAILED: "❌", 
                TestStatus.SKIPPED: "⏭️",
                TestStatus.ERROR: "💥"
            }.get(test_result.status, "❓")
            
            content += f"| {test_result.test_name} | {test_result.test_file} | {status_emoji} {test_result.status.value} | {test_result.duration:.3f} | {test_result.assertions} |\n"
        
        if include_trends:
            trends = self.metrics_collector.get_test_trends(7)
            if trends["daily_stats"]:
                content += "\n## Recent Trends (7 days)\n\n"
                content += "| Date | Success Rate | Avg Duration | Coverage |\n"
                content += "|------|--------------|--------------|----------|\n"
                for stat in trends["daily_stats"][-7:]:
                    content += f"| {stat['date']} | {stat['success_rate']:.1f}% | {stat['avg_duration']:.2f}s | {stat['avg_coverage']:.1f}% |\n"
        
        report_file = self.output_dir / f"test_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w') as f:
            f.write(content)
        
        return report_file
    
    def _generate_console_report(self, suite_result: TestSuiteResult, include_trends: bool):
        """Generate console report"""
        # Summary panel
        summary_text = f"""
Total Tests: {suite_result.total_tests}
Passed: [green]{suite_result.passed_tests}[/green]
Failed: [red]{suite_result.failed_tests}[/red]
Skipped: [yellow]{suite_result.skipped_tests}[/yellow]
Success Rate: {suite_result.success_rate:.1f}%
Coverage: {suite_result.coverage_percentage:.1f}%
Duration: {suite_result.total_duration:.2f}s
"""
        
        panel = Panel(summary_text, title=f"Test Report: {suite_result.suite_name}", border_style="blue")
        self.console.print(panel)
        
        # Failed tests table
        if suite_result.failed_tests > 0:
            failed_table = Table(title="Failed Tests")
            failed_table.add_column("Test Name", style="red")
            failed_table.add_column("File")
            failed_table.add_column("Error")
            
            for test_result in suite_result.test_results:
                if test_result.status == TestStatus.FAILED:
                    error_msg = test_result.error_message or "No error message"
                    if len(error_msg) > 50:
                        error_msg = error_msg[:47] + "..."
                    failed_table.add_row(test_result.test_name, test_result.test_file, error_msg)
            
            self.console.print(failed_table)
        
        # Trends
        if include_trends:
            trends = self.metrics_collector.get_test_trends(7)
            if trends["daily_stats"]:
                trends_table = Table(title="Recent Trends (7 days)")
                trends_table.add_column("Date")
                trends_table.add_column("Success Rate")
                trends_table.add_column("Avg Duration")
                trends_table.add_column("Coverage")
                
                for stat in trends["daily_stats"][-7:]:
                    trends_table.add_row(
                        stat['date'],
                        f"{stat['success_rate']:.1f}%",
                        f"{stat['avg_duration']:.2f}s",
                        f"{stat['avg_coverage']:.1f}%"
                    )
                
                self.console.print(trends_table)


# Global test reporter instance
test_reporter = TestReporter()
