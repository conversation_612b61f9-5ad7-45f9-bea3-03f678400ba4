"""
Agent behavior tests for Master Agent

Tests Master Agent decision making, task delegation, error handling,
and interaction patterns with LLM and sub-agents.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import json

from ccw.core.agent import Agent<PERSON><PERSON>, Agent<PERSON><PERSON>x<PERSON>, AgentResult, AgentStatus
from ccw.core.registry import agent_registry
from ccw.agents.master_agent import MasterAgent


class TestMasterAgentDecisionMaking:
    """Test Master Agent decision making capabilities"""
    
    @pytest.mark.asyncio
    async def test_intent_analysis_with_llm(self, mock_llm_client):
        """Test Master Agent intent analysis using LLM"""
        # Mock LLM response for intent analysis
        mock_response = MagicMock()
        mock_response.content = json.dumps({
            "query_type": "code_analysis",
            "requires_code_reading": True,
            "requires_reasoning": True,
            "complexity": "medium",
            "confidence": 0.9,
            "suggested_approach": "Parse and analyze code structure"
        })
        mock_llm_client.generate_response.return_value = mock_response
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            # Test intent analysis
            task = AgentTask(
                task_type="code_analysis",
                description="Analyze this Python function",
                data={"code": "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Verify LLM was called for intent analysis
            mock_llm_client.generate_response.assert_called()
            
            # Verify task completed
            assert result.status == AgentStatus.COMPLETED
            
            await master_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_fallback_intent_analysis(self, mock_llm_client):
        """Test Master Agent fallback when LLM intent analysis fails"""
        # Mock LLM to fail
        mock_llm_client.generate_response.side_effect = Exception("LLM unavailable")
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="code_analysis",
                description="Explain how this function works",
                data={"code": "def test(): pass"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task (should use fallback analysis)
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Should still complete using fallback
            assert result.status in [AgentStatus.COMPLETED, AgentStatus.FAILED]
            
            await master_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_task_delegation_decision(self, mock_llm_client):
        """Test Master Agent task delegation decisions"""
        # Create mock sub-agents
        class MockAnalysisAgent:
            def __init__(self):
                self.agent_id = "analysis_agent"
                self.capabilities = ["code_analysis", "ast_analysis"]
                self.is_running = True
                self.executed_tasks = []
            
            def supports_capability(self, capability):
                return capability in self.capabilities
            
            async def execute_with_monitoring(self, task, context):
                self.executed_tasks.append(task)
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={"analysis": "completed"}
                )
        
        # Register mock agent
        mock_agent = MockAnalysisAgent()
        agent_registry.agents["analysis_agent"] = mock_agent
        agent_registry.capabilities["code_analysis"] = ["analysis_agent"]
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="code_analysis",
                description="Analyze code structure",
                data={"code": "class Test: pass"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Verify task was delegated
            assert len(mock_agent.executed_tasks) > 0
            assert result.status == AgentStatus.COMPLETED
            
            await master_agent.shutdown()
        
        # Cleanup
        agent_registry.unregister_agent("analysis_agent")


class TestMasterAgentErrorHandling:
    """Test Master Agent error handling behaviors"""
    
    @pytest.mark.asyncio
    async def test_sub_agent_failure_handling(self, mock_llm_client):
        """Test Master Agent handling sub-agent failures"""
        # Create failing sub-agent
        class FailingAgent:
            def __init__(self):
                self.agent_id = "failing_agent"
                self.capabilities = ["failing_task"]
                self.is_running = True
            
            def supports_capability(self, capability):
                return capability in self.capabilities
            
            async def execute_with_monitoring(self, task, context):
                raise ValueError("Sub-agent failure")
        
        # Register failing agent
        failing_agent = FailingAgent()
        agent_registry.agents["failing_agent"] = failing_agent
        agent_registry.capabilities["failing_task"] = ["failing_agent"]
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="failing_task",
                description="Task that will fail",
                data={"test": "data"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Master agent should handle failure gracefully
            assert result.status == AgentStatus.FAILED
            assert "failure" in result.error.lower() or "error" in result.error.lower()
            
            await master_agent.shutdown()
        
        # Cleanup
        agent_registry.unregister_agent("failing_agent")
    
    @pytest.mark.asyncio
    async def test_no_capable_agent_handling(self, mock_llm_client):
        """Test Master Agent handling when no capable agent exists"""
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="nonexistent_capability",
                description="Task requiring non-existent capability",
                data={"test": "data"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Should handle gracefully
            assert result.status == AgentStatus.FAILED
            assert "no agent" in result.error.lower() or "capability" in result.error.lower()
            
            await master_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_llm_failure_resilience(self):
        """Test Master Agent resilience to LLM failures"""
        # Mock LLM client that always fails
        mock_llm_client = MagicMock()
        mock_llm_client.generate_response = AsyncMock(side_effect=Exception("LLM service down"))
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="simple_task",
                description="Simple task",
                data={"test": "data"}
            )
            context = AgentContext(session_id="test_session")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Should still attempt to complete using fallback mechanisms
            assert result is not None
            assert result.status in [AgentStatus.COMPLETED, AgentStatus.FAILED]
            
            await master_agent.shutdown()


class TestMasterAgentLLMInteraction:
    """Test Master Agent interaction patterns with LLM"""
    
    @pytest.mark.asyncio
    async def test_llm_query_construction(self, mock_llm_client):
        """Test Master Agent LLM query construction"""
        # Track LLM calls
        llm_calls = []
        
        def track_llm_call(*args, **kwargs):
            llm_calls.append((args, kwargs))
            mock_response = MagicMock()
            mock_response.content = json.dumps({
                "query_type": "general",
                "confidence": 0.8
            })
            return mock_response
        
        mock_llm_client.generate_response = AsyncMock(side_effect=track_llm_call)
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="analysis",
                description="Analyze this complex code structure",
                data={"code": "complex code here"}
            )
            context = AgentContext(
                session_id="test_session",
                user_query="What does this code do?",
                workspace_path="/test/workspace"
            )
            
            # Execute task
            await master_agent.execute_with_monitoring(task, context)
            
            # Verify LLM was called with appropriate context
            assert len(llm_calls) > 0
            
            # Check that context information was included
            # (exact format depends on implementation)
            
            await master_agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_llm_response_parsing(self, mock_llm_client):
        """Test Master Agent LLM response parsing"""
        # Test various LLM response formats
        test_responses = [
            # Valid JSON response
            json.dumps({
                "query_type": "code_analysis",
                "confidence": 0.9,
                "requires_reasoning": True
            }),
            # Invalid JSON (should fallback)
            "This is not JSON",
            # Partial JSON
            '{"query_type": "analysis"'
        ]
        
        for response_content in test_responses:
            mock_response = MagicMock()
            mock_response.content = response_content
            mock_llm_client.generate_response.return_value = mock_response
            
            with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
                master_agent = MasterAgent()
                await master_agent.startup()
                
                task = AgentTask(
                    task_type="test",
                    description="Test response parsing"
                )
                context = AgentContext(session_id="test_session")
                
                # Should handle all response formats gracefully
                result = await master_agent.execute_with_monitoring(task, context)
                assert result is not None
                
                await master_agent.shutdown()


class TestMasterAgentWorkflowOrchestration:
    """Test Master Agent workflow orchestration"""
    
    @pytest.mark.asyncio
    async def test_multi_step_workflow(self, mock_llm_client):
        """Test Master Agent orchestrating multi-step workflows"""
        # Create agents for different workflow steps
        workflow_steps = []
        
        class WorkflowAgent:
            def __init__(self, agent_id, capabilities):
                self.agent_id = agent_id
                self.capabilities = capabilities
                self.is_running = True
                self.executed_tasks = []
            
            def supports_capability(self, capability):
                return capability in self.capabilities
            
            async def execute_with_monitoring(self, task, context):
                self.executed_tasks.append(task)
                workflow_steps.append(self.agent_id)
                return AgentResult(
                    agent_id=self.agent_id,
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={f"{self.agent_id}_result": "completed"}
                )
        
        # Register workflow agents
        agents = [
            WorkflowAgent("parser_agent", ["parsing"]),
            WorkflowAgent("analyzer_agent", ["analysis"]),
            WorkflowAgent("reporter_agent", ["reporting"])
        ]
        
        for agent in agents:
            agent_registry.agents[agent.agent_id] = agent
            for capability in agent.capabilities:
                if capability not in agent_registry.capabilities:
                    agent_registry.capabilities[capability] = []
                agent_registry.capabilities[capability].append(agent.agent_id)
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            # Create complex task requiring multiple steps
            task = AgentTask(
                task_type="complex_analysis",
                description="Perform complete code analysis workflow",
                data={"workspace_path": "/test/workspace"}
            )
            context = AgentContext(session_id="workflow_test")
            
            # Execute workflow
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Verify workflow execution
            assert result.status == AgentStatus.COMPLETED
            
            # Check that multiple agents were involved
            total_executions = sum(len(agent.executed_tasks) for agent in agents)
            assert total_executions > 0
            
            await master_agent.shutdown()
        
        # Cleanup
        for agent in agents:
            agent_registry.unregister_agent(agent.agent_id)
    
    @pytest.mark.asyncio
    async def test_adaptive_workflow_adjustment(self, mock_llm_client):
        """Test Master Agent adapting workflow based on intermediate results"""
        # This would test the Master Agent's ability to adjust its approach
        # based on results from sub-agents
        
        execution_log = []
        
        class AdaptiveAgent:
            def __init__(self, agent_id, should_fail=False):
                self.agent_id = agent_id
                self.capabilities = ["adaptive_task"]
                self.is_running = True
                self.should_fail = should_fail
            
            def supports_capability(self, capability):
                return capability in self.capabilities
            
            async def execute_with_monitoring(self, task, context):
                execution_log.append(f"{self.agent_id}_executed")
                
                if self.should_fail:
                    return AgentResult(
                        agent_id=self.agent_id,
                        task_id=task.id,
                        status=AgentStatus.FAILED,
                        error="Simulated failure"
                    )
                else:
                    return AgentResult(
                        agent_id=self.agent_id,
                        task_id=task.id,
                        status=AgentStatus.COMPLETED,
                        data={"result": "success"}
                    )
        
        # Register adaptive agents
        primary_agent = AdaptiveAgent("primary_agent", should_fail=True)
        fallback_agent = AdaptiveAgent("fallback_agent", should_fail=False)
        
        agent_registry.agents["primary_agent"] = primary_agent
        agent_registry.agents["fallback_agent"] = fallback_agent
        agent_registry.capabilities["adaptive_task"] = ["primary_agent", "fallback_agent"]
        
        with patch('ccw.agents.master_agent.llm_client', mock_llm_client):
            master_agent = MasterAgent()
            await master_agent.startup()
            
            task = AgentTask(
                task_type="adaptive_task",
                description="Task requiring adaptive handling"
            )
            context = AgentContext(session_id="adaptive_test")
            
            # Execute task
            result = await master_agent.execute_with_monitoring(task, context)
            
            # Master agent should adapt to failure and try alternatives
            # (exact behavior depends on implementation)
            assert result is not None
            
            await master_agent.shutdown()
        
        # Cleanup
        agent_registry.unregister_agent("primary_agent")
        agent_registry.unregister_agent("fallback_agent")
