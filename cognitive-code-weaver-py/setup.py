"""
Setup script for Cognitive Code Weaver Python Edition
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="cognitive-code-weaver",
    version="0.1.0",
    author="Cognitive Code Weaver Team",
    author_email="<EMAIL>",
    description="A highly modular, AI-powered code analysis and reasoning system",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/cognitive-code-weaver/cognitive-code-weaver-py",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "pre-commit>=3.3.0",
        ],
        "jupyter": [
            "jupyter>=1.0.0",
            "ipykernel>=6.25.0",
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
        ],
        "ml": [
            "torch-geometric>=2.3.0",
            "dgl>=1.1.0",
            "optuna>=3.3.0",
        ],
        "all": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "pre-commit>=3.3.0",
            "jupyter>=1.0.0",
            "ipykernel>=6.25.0",
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "torch-geometric>=2.3.0",
            "dgl>=1.1.0",
            "optuna>=3.3.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ccw=ccw.cli.main:main",
            "cognitive-code-weaver=ccw.cli.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "ccw": [
            "config/*.yaml",
            "config/*.json",
            "templates/*.txt",
            "templates/*.md",
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/cognitive-code-weaver/cognitive-code-weaver-py/issues",
        "Source": "https://github.com/cognitive-code-weaver/cognitive-code-weaver-py",
        "Documentation": "https://cognitive-code-weaver.readthedocs.io/",
    },
    keywords=[
        "ai",
        "code-analysis", 
        "reasoning",
        "multi-agent",
        "llm",
        "cognitive",
        "code-understanding",
        "software-engineering",
        "automation",
        "knowledge-graph"
    ],
    zip_safe=False,
)
