#!/usr/bin/env python3
"""
CLI Test Script for Cognitive Code Weaver

This script tests the CLI functionality without requiring full system initialization.
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cli_imports():
    """Test that CLI modules can be imported"""
    print("🧪 Testing CLI imports...")
    
    try:
        from ccw.cli.main import app
        print("✅ Main CLI app imported successfully")
        
        from ccw.cli.config import CLIConfig, console
        print("✅ CLI config imported successfully")
        
        from ccw.cli.utils import format_output, create_progress_bar
        print("✅ CLI utils imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_cli_config():
    """Test CLI configuration"""
    print("\n🧪 Testing CLI configuration...")
    
    try:
        from ccw.cli.config import CLIConfig
        
        # Test default config
        config = CLIConfig()
        print(f"✅ Default config created: verbose={config.verbose}")
        
        # Test environment config
        os.environ["CCW_VERBOSE"] = "true"
        os.environ["CCW_OUTPUT_FORMAT"] = "json"
        
        env_config = CLIConfig.from_env()
        print(f"✅ Environment config: verbose={env_config.verbose}, format={env_config.output_format}")
        
        # Clean up environment
        del os.environ["CCW_VERBOSE"]
        del os.environ["CCW_OUTPUT_FORMAT"]
        
        return True
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False


def test_cli_utils():
    """Test CLI utilities"""
    print("\n🧪 Testing CLI utilities...")
    
    try:
        from ccw.cli.utils import format_output, create_table, format_file_size
        
        # Test output formatting
        test_data = {"name": "test", "value": 42, "items": [1, 2, 3]}
        
        json_output = format_output(test_data, "json")
        print(f"✅ JSON formatting: {len(json_output)} characters")
        
        yaml_output = format_output(test_data, "yaml")
        print(f"✅ YAML formatting: {len(yaml_output)} characters")
        
        text_output = format_output(test_data, "text")
        print(f"✅ Text formatting: {len(text_output)} characters")
        
        # Test table creation
        table = create_table("Test Table", ["Column 1", "Column 2"])
        print("✅ Table creation successful")
        
        # Test file size formatting
        size_str = format_file_size(1024 * 1024)
        print(f"✅ File size formatting: {size_str}")
        
        return True
    except Exception as e:
        print(f"❌ Utils test failed: {e}")
        return False


def test_cli_help():
    """Test CLI help functionality"""
    print("\n🧪 Testing CLI help...")
    
    try:
        from ccw.cli.main import app
        from typer.testing import CliRunner
        
        runner = CliRunner()
        
        # Test main help
        result = runner.invoke(app, ["--help"])
        if result.exit_code == 0:
            print("✅ Main help command works")
        else:
            print(f"❌ Main help failed: {result.output}")
            return False
        
        # Test command help
        commands = ["analyze", "ask", "status", "agents", "llm", "interactive"]
        for cmd in commands:
            result = runner.invoke(app, [cmd, "--help"])
            if result.exit_code == 0:
                print(f"✅ Help for '{cmd}' command works")
            else:
                print(f"❌ Help for '{cmd}' failed")
        
        return True
    except Exception as e:
        print(f"❌ Help test failed: {e}")
        return False


def test_cli_version():
    """Test CLI version functionality"""
    print("\n🧪 Testing CLI version...")
    
    try:
        from ccw.cli.config import print_version
        
        # This should not raise an exception
        print_version()
        print("✅ Version display works")
        
        return True
    except Exception as e:
        print(f"❌ Version test failed: {e}")
        return False


def test_file_operations():
    """Test file operations"""
    print("\n🧪 Testing file operations...")
    
    try:
        from ccw.cli.utils import save_output
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            temp_path = f.name
        
        # Test saving output
        test_content = "This is a test file content"
        success = save_output(test_content, temp_path)
        
        if success and os.path.exists(temp_path):
            with open(temp_path, 'r') as f:
                saved_content = f.read()
            
            if saved_content == test_content:
                print("✅ File save/load works")
                result = True
            else:
                print("❌ File content mismatch")
                result = False
        else:
            print("❌ File save failed")
            result = False
        
        # Clean up
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        
        return result
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False


def test_cli_commands_structure():
    """Test CLI commands structure"""
    print("\n🧪 Testing CLI commands structure...")
    
    try:
        # Test that command modules exist
        commands_dir = project_root / "ccw" / "cli" / "commands"
        
        if commands_dir.exists():
            print("✅ Commands directory exists")
        else:
            print("❌ Commands directory missing")
            return False
        
        # Test that __init__.py exists
        init_file = commands_dir / "__init__.py"
        if init_file.exists():
            print("✅ Commands __init__.py exists")
        else:
            print("❌ Commands __init__.py missing")
            return False
        
        # Test that analysis.py exists
        analysis_file = commands_dir / "analysis.py"
        if analysis_file.exists():
            print("✅ Analysis commands file exists")
        else:
            print("❌ Analysis commands file missing")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Commands structure test failed: {e}")
        return False


def run_all_tests():
    """Run all CLI tests"""
    print("🚀 Starting Cognitive Code Weaver CLI Tests\n")
    
    tests = [
        ("CLI Imports", test_cli_imports),
        ("CLI Configuration", test_cli_config),
        ("CLI Utilities", test_cli_utils),
        ("CLI Help", test_cli_help),
        ("CLI Version", test_cli_version),
        ("File Operations", test_file_operations),
        ("Commands Structure", test_cli_commands_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}\n")
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! CLI is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False


def main():
    """Main test function"""
    success = run_all_tests()
    
    if success:
        print("\n🎯 Next steps:")
        print("1. Install the package: pip install -e .")
        print("2. Test the CLI: ccw --help")
        print("3. Initialize config: ccw init")
        print("4. Try analysis: ccw analyze ./src")
        print("5. Start interactive mode: ccw interactive")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
