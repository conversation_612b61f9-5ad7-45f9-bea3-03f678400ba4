# 🎮 Cognitive Code Weaver - Interactive System Diagrams

## 🌐 **Interactive Master System Overview**

```mermaid
graph TB
    subgraph "🧠 Cognitive Code Weaver - Interactive System Explorer"
        subgraph "🎯 Presentation Layer [Click to Expand]"
            PL1[Web UI]
            PL2[REST API]
            PL3[GraphQL API]
            PL4[CLI Interface]
            PL5[WebSocket API]
            
            click PL1 "#web-ui-details"
            click PL2 "#rest-api-details"
            click PL3 "#graphql-api-details"
            click PL4 "#cli-interface-details"
            click PL5 "#websocket-api-details"
        end
        
        subgraph "🤖 Agent Layer [Click to Explore]"
            AL1[Master Agent]
            AL2[Cognitive Agent]
            AL3[Planner Agent]
            AL4[Code Reader Agent]
            AL5[Reasoner Agent]
            AL6[Bug Detector Agent]
            AL7[Knowledge Graph Agent]
            AL8[Analysis Agent]
            
            click AL1 "#master-agent-functions"
            click AL2 "#cognitive-agent-functions"
            click AL3 "#planner-agent-functions"
            click AL4 "#code-reader-functions"
            click AL5 "#reasoner-agent-functions"
            click AL6 "#bug-detector-functions"
            click AL7 "#knowledge-graph-functions"
            click AL8 "#analysis-agent-functions"
        end
        
        subgraph "🧠 Core Intelligence [Expandable]"
            CI1[LLM Client]
            CI2[Prompt Manager]
            CI3[Response Processor]
            CI4[Context Manager]
            CI5[Memory System]
            
            click CI1 "#llm-client-architecture"
            click CI2 "#prompt-management-system"
            click CI3 "#response-processing-pipeline"
            click CI4 "#context-management-system"
            click CI5 "#memory-system-architecture"
        end
        
        subgraph "🔬 Analysis Engine [Drill Down]"
            AE1[Workspace Analyzer]
            AE2[Code Parser]
            AE3[Symbol Extractor]
            AE4[Dependency Analyzer]
            AE5[Metrics Calculator]
            AE6[Pattern Detector]
            
            click AE1 "#workspace-analysis-pipeline"
            click AE2 "#code-parsing-system"
            click AE3 "#symbol-extraction-process"
            click AE4 "#dependency-analysis-engine"
            click AE5 "#metrics-calculation-system"
            click AE6 "#pattern-detection-algorithms"
        end
        
        subgraph "🧩 Knowledge System [Interactive]"
            KS1[Knowledge Graph]
            KS2[Semantic Analyzer]
            KS3[Concept Mapper]
            KS4[Relationship Extractor]
            KS5[Query Engine]
            
            click KS1 "#knowledge-graph-architecture"
            click KS2 "#semantic-analysis-pipeline"
            click KS3 "#concept-mapping-system"
            click KS4 "#relationship-extraction-engine"
            click KS5 "#query-engine-architecture"
        end
        
        subgraph "💾 Storage Layer [Configuration]"
            SL1[(Vector Database)]
            SL2[(Graph Database)]
            SL3[(Cache System)]
            SL4[(File System)]
            SL5[(Configuration)]
            
            click SL1 "#vector-database-config"
            click SL2 "#graph-database-config"
            click SL3 "#cache-system-config"
            click SL4 "#file-system-config"
            click SL5 "#configuration-management"
        end
        
        subgraph "🔧 Infrastructure [Monitoring]"
            IF1[Message Bus]
            IF2[Task Manager]
            IF3[Event System]
            IF4[Configuration Manager]
            IF5[Logger]
            IF6[Monitor]
            
            click IF1 "#message-bus-architecture"
            click IF2 "#task-management-system"
            click IF3 "#event-system-architecture"
            click IF4 "#configuration-management-system"
            click IF5 "#logging-system"
            click IF6 "#monitoring-dashboard"
        end
    end
    
    %% Interactive Flow Connections
    PL1 --> AL1
    PL2 --> AL2
    PL3 --> AL3
    PL4 --> AL4
    PL5 --> AL5
    
    AL1 -.->|orchestrates| AL2
    AL2 -.->|plans_with| AL3
    AL3 -.->|reads_via| AL4
    AL4 -.->|reasons_with| AL5
    AL5 -.->|detects_via| AL6
    AL6 -.->|queries| AL7
    AL7 -.->|analyzes_with| AL8
    
    AL1 --> CI1
    AL2 --> CI2
    AL3 --> CI3
    AL4 --> CI4
    AL5 --> CI5
    
    AL4 --> AE1
    AL8 --> AE2
    AE1 --> AE3
    AE2 --> AE4
    AE3 --> AE5
    AE4 --> AE6
    
    AL7 --> KS1
    KS1 --> KS2
    KS2 --> KS3
    KS3 --> KS4
    KS4 --> KS5
    
    CI5 --> SL1
    KS1 --> SL2
    AL1 --> SL3
    AE1 --> SL4
    IF4 --> SL5
    
    AL1 --> IF1
    AL2 --> IF2
    AL3 --> IF3
    AL4 --> IF4
    AL5 --> IF5
    AL6 --> IF6
    
    %% Interactive styling with hover effects
    classDef presentationLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,cursor:pointer
    classDef agentLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px,cursor:pointer
    classDef coreIntelligence fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,cursor:pointer
    classDef analysisEngine fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,cursor:pointer
    classDef knowledgeSystem fill:#ffebee,stroke:#d32f2f,stroke-width:2px,cursor:pointer
    classDef storageLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px,cursor:pointer
    classDef infrastructure fill:#fff8e1,stroke:#ff8f00,stroke-width:2px,cursor:pointer
    
    class PL1,PL2,PL3,PL4,PL5 presentationLayer
    class AL1,AL2,AL3,AL4,AL5,AL6,AL7,AL8 agentLayer
    class CI1,CI2,CI3,CI4,CI5 coreIntelligence
    class AE1,AE2,AE3,AE4,AE5,AE6 analysisEngine
    class KS1,KS2,KS3,KS4,KS5 knowledgeSystem
    class SL1,SL2,SL3,SL4,SL5 storageLayer
    class IF1,IF2,IF3,IF4,IF5,IF6 infrastructure
```

## 🔄 **Interactive Agent Workflow**

```mermaid
graph TD
    subgraph "🔄 Interactive Agent Workflow - Multi-Level Explorer"
        subgraph "Level 1: Request Processing [Click to Drill Down]"
            L1_INPUT[User Request]
            L1_ROUTE[Request Router]
            L1_VALIDATE[Request Validator]
            L1_DISPATCH[Task Dispatcher]
            
            click L1_INPUT "#level2-input-processing"
            click L1_ROUTE "#level2-routing-logic"
            click L1_VALIDATE "#level2-validation-rules"
            click L1_DISPATCH "#level2-dispatch-strategy"
        end
        
        subgraph "Level 2: Agent Coordination [Expandable]"
            L2_MASTER{Master Agent}
            L2_SELECT[Agent Selection]
            L2_COORDINATE[Coordination Logic]
            L2_MONITOR[Progress Monitoring]
            
            click L2_MASTER "#level3-master-agent-details"
            click L2_SELECT "#level3-agent-selection-algorithm"
            click L2_COORDINATE "#level3-coordination-patterns"
            click L2_MONITOR "#level3-monitoring-system"
        end
        
        subgraph "Level 3: Specialized Processing [Function Details]"
            L3_COGNITIVE[Cognitive Processing]
            L3_ANALYSIS[Code Analysis]
            L3_PLANNING[Task Planning]
            L3_REASONING[Logical Reasoning]
            
            click L3_COGNITIVE "#function-cognitive-processing"
            click L3_ANALYSIS "#function-code-analysis"
            click L3_PLANNING "#function-task-planning"
            click L3_REASONING "#function-logical-reasoning"
        end
        
        subgraph "Level 4: Knowledge Integration [Data Flow]"
            L4_EXTRACT[Knowledge Extraction]
            L4_INTEGRATE[Data Integration]
            L4_SYNTHESIZE[Synthesis Engine]
            L4_VALIDATE[Result Validation]
            
            click L4_EXTRACT "#dataflow-knowledge-extraction"
            click L4_INTEGRATE "#dataflow-data-integration"
            click L4_SYNTHESIZE "#dataflow-synthesis-process"
            click L4_VALIDATE "#dataflow-result-validation"
        end
        
        subgraph "Level 5: Output Generation [Visualization Options]"
            L5_FORMAT[Response Formatting]
            L5_VISUALIZE[Visualization Generation]
            L5_EXPORT[Export Processing]
            L5_DELIVER[Delivery System]
            
            click L5_FORMAT "#output-response-formatting"
            click L5_VISUALIZE "#output-visualization-engine"
            click L5_EXPORT "#output-export-options"
            click L5_DELIVER "#output-delivery-channels"
        end
    end
    
    %% Level 1 Flow
    L1_INPUT --> L1_ROUTE
    L1_ROUTE --> L1_VALIDATE
    L1_VALIDATE --> L1_DISPATCH
    
    %% Level 1 to Level 2
    L1_DISPATCH --> L2_MASTER
    L2_MASTER --> L2_SELECT
    L2_SELECT --> L2_COORDINATE
    L2_COORDINATE --> L2_MONITOR
    
    %% Level 2 to Level 3
    L2_SELECT --> L3_COGNITIVE
    L2_SELECT --> L3_ANALYSIS
    L2_SELECT --> L3_PLANNING
    L2_SELECT --> L3_REASONING
    
    %% Level 3 to Level 4
    L3_COGNITIVE --> L4_EXTRACT
    L3_ANALYSIS --> L4_INTEGRATE
    L3_PLANNING --> L4_SYNTHESIZE
    L3_REASONING --> L4_VALIDATE
    
    %% Level 4 to Level 5
    L4_EXTRACT --> L5_FORMAT
    L4_INTEGRATE --> L5_VISUALIZE
    L4_SYNTHESIZE --> L5_EXPORT
    L4_VALIDATE --> L5_DELIVER
    
    %% Cross-level connections
    L2_MONITOR -.->|feedback| L1_ROUTE
    L4_VALIDATE -.->|quality_check| L2_COORDINATE
    L5_DELIVER -.->|completion| L1_INPUT
    
    %% Interactive styling with progressive disclosure
    classDef level1 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,cursor:pointer
    classDef level2 fill:#fff3e0,stroke:#f57c00,stroke-width:3px,cursor:pointer
    classDef level3 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,cursor:pointer
    classDef level4 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,cursor:pointer
    classDef level5 fill:#ffebee,stroke:#d32f2f,stroke-width:3px,cursor:pointer
    
    class L1_INPUT,L1_ROUTE,L1_VALIDATE,L1_DISPATCH level1
    class L2_MASTER,L2_SELECT,L2_COORDINATE,L2_MONITOR level2
    class L3_COGNITIVE,L3_ANALYSIS,L3_PLANNING,L3_REASONING level3
    class L4_EXTRACT,L4_INTEGRATE,L4_SYNTHESIZE,L4_VALIDATE level4
    class L5_FORMAT,L5_VISUALIZE,L5_EXPORT,L5_DELIVER level5
```

## 🧠 **Interactive LLM Integration Flow**

```mermaid
graph LR
    subgraph "🧠 Interactive LLM Integration - Provider Explorer"
        subgraph "📝 Input Processing [Expandable]"
            IP1[Context Builder]
            IP2[Prompt Template]
            IP3[Token Optimizer]
            IP4[Request Formatter]
            
            click IP1 "#context-building-strategies"
            click IP2 "#prompt-template-library"
            click IP3 "#token-optimization-algorithms"
            click IP4 "#request-formatting-options"
        end
        
        subgraph "🔌 Provider Selection [Interactive]"
            PS1{OpenAI Provider}
            PS2{Anthropic Provider}
            PS3{Local Model}
            PS4{Azure OpenAI}
            PS5{Custom Provider}
            
            click PS1 "#openai-configuration"
            click PS2 "#anthropic-configuration"
            click PS3 "#local-model-setup"
            click PS4 "#azure-openai-setup"
            click PS5 "#custom-provider-integration"
        end
        
        subgraph "⚡ Processing Pipeline [Performance Metrics]"
            PP1[Rate Limiting]
            PP2[Load Balancing]
            PP3[Error Handling]
            PP4[Response Caching]
            PP5[Quality Assessment]
            
            click PP1 "#rate-limiting-dashboard"
            click PP2 "#load-balancing-metrics"
            click PP3 "#error-handling-strategies"
            click PP4 "#caching-performance"
            click PP5 "#quality-assessment-criteria"
        end
        
        subgraph "🔄 Response Processing [Validation]"
            RP1[Content Extraction]
            RP2[Format Validation]
            RP3[Quality Scoring]
            RP4[Result Integration]
            
            click RP1 "#content-extraction-methods"
            click RP2 "#format-validation-rules"
            click RP3 "#quality-scoring-algorithms"
            click RP4 "#result-integration-patterns"
        end
        
        subgraph "📊 Monitoring & Analytics [Dashboard]"
            MA1[Usage Tracking]
            MA2[Cost Analysis]
            MA3[Performance Metrics]
            MA4[Error Analytics]
            MA5[Optimization Insights]
            
            click MA1 "#usage-tracking-dashboard"
            click MA2 "#cost-analysis-reports"
            click MA3 "#performance-monitoring"
            click MA4 "#error-analytics-dashboard"
            click MA5 "#optimization-recommendations"
        end
    end
    
    %% Input Processing Flow
    IP1 --> IP2
    IP2 --> IP3
    IP3 --> IP4
    
    %% Input to Provider Selection
    IP4 --> PS1
    IP4 --> PS2
    IP4 --> PS3
    IP4 --> PS4
    IP4 --> PS5
    
    %% Provider to Processing Pipeline
    PS1 --> PP1
    PS2 --> PP2
    PS3 --> PP3
    PS4 --> PP4
    PS5 --> PP5
    
    %% Processing Pipeline Flow
    PP1 -.->|limits| PP2
    PP2 -.->|balances| PP3
    PP3 -.->|handles| PP4
    PP4 -.->|caches| PP5
    
    %% Processing to Response
    PP1 --> RP1
    PP2 --> RP2
    PP3 --> RP3
    PP4 --> RP4
    
    %% Response Processing Flow
    RP1 --> RP2
    RP2 --> RP3
    RP3 --> RP4
    
    %% Monitoring Integration
    PP1 --> MA1
    PP2 --> MA2
    PP3 --> MA3
    PP4 --> MA4
    PP5 --> MA5
    
    %% Feedback Loops
    MA5 -.->|optimizes| IP3
    MA3 -.->|improves| PP2
    MA4 -.->|enhances| PP3
    
    %% Interactive styling
    classDef inputProcessing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,cursor:pointer
    classDef providerSelection fill:#fff3e0,stroke:#f57c00,stroke-width:2px,cursor:pointer
    classDef processingPipeline fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,cursor:pointer
    classDef responseProcessing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,cursor:pointer
    classDef monitoringAnalytics fill:#ffebee,stroke:#d32f2f,stroke-width:2px,cursor:pointer
    
    class IP1,IP2,IP3,IP4 inputProcessing
    class PS1,PS2,PS3,PS4,PS5 providerSelection
    class PP1,PP2,PP3,PP4,PP5 processingPipeline
    class RP1,RP2,RP3,RP4 responseProcessing
    class MA1,MA2,MA3,MA4,MA5 monitoringAnalytics
```

## 🔬 **Interactive Analysis Pipeline**

```mermaid
graph TD
    subgraph "🔬 Interactive Code Analysis Pipeline - Deep Dive Explorer"
        subgraph "📁 Source Processing [File Explorer]"
            SP1[File Scanner]
            SP2[Language Detector]
            SP3[Syntax Validator]
            SP4[Metadata Extractor]

            click SP1 "#file-scanning-algorithms"
            click SP2 "#language-detection-rules"
            click SP3 "#syntax-validation-engine"
            click SP4 "#metadata-extraction-process"
        end

        subgraph "🔍 Parsing Engine [AST Explorer]"
            PE1[Lexical Analyzer]
            PE2[Syntax Parser]
            PE3[AST Builder]
            PE4[Symbol Table Generator]
            PE5[Error Reporter]

            click PE1 "#lexical-analysis-details"
            click PE2 "#syntax-parsing-algorithms"
            click PE3 "#ast-building-process"
            click PE4 "#symbol-table-construction"
            click PE5 "#error-reporting-system"
        end

        subgraph "🏗️ Structure Analysis [Hierarchy Viewer]"
            SA1[Class Hierarchy Analyzer]
            SA2[Function Dependency Mapper]
            SA3[Variable Scope Tracker]
            SA4[Import Relationship Builder]
            SA5[Module Dependency Analyzer]

            click SA1 "#class-hierarchy-visualization"
            click SA2 "#function-dependency-graph"
            click SA3 "#variable-scope-analysis"
            click SA4 "#import-relationship-network"
            click SA5 "#module-dependency-tree"
        end

        subgraph "🕸️ Relationship Engine [Network Explorer]"
            RE1[Call Graph Builder]
            RE2[Data Flow Analyzer]
            RE3[Control Flow Mapper]
            RE4[Usage Pattern Detector]
            RE5[Coupling Analyzer]

            click RE1 "#call-graph-visualization"
            click RE2 "#data-flow-diagrams"
            click RE3 "#control-flow-charts"
            click RE4 "#usage-pattern-analysis"
            click RE5 "#coupling-metrics-dashboard"
        end

        subgraph "📊 Metrics Engine [Dashboard]"
            ME1[Complexity Calculator]
            ME2[Quality Assessor]
            ME3[Maintainability Scorer]
            ME4[Performance Analyzer]
            ME5[Technical Debt Evaluator]

            click ME1 "#complexity-metrics-dashboard"
            click ME2 "#quality-assessment-reports"
            click ME3 "#maintainability-scoring"
            click ME4 "#performance-analysis-charts"
            click ME5 "#technical-debt-visualization"
        end

        subgraph "🧠 Intelligence Layer [AI Insights]"
            IL1[Pattern Recognition]
            IL2[Anomaly Detection]
            IL3[Insight Generation]
            IL4[Recommendation Engine]
            IL5[Learning System]

            click IL1 "#pattern-recognition-algorithms"
            click IL2 "#anomaly-detection-system"
            click IL3 "#insight-generation-engine"
            click IL4 "#recommendation-algorithms"
            click IL5 "#learning-system-dashboard"
        end
    end

    %% Source Processing Flow
    SP1 --> SP2
    SP2 --> SP3
    SP3 --> SP4

    %% Source to Parsing
    SP4 --> PE1
    PE1 --> PE2
    PE2 --> PE3
    PE3 --> PE4
    PE4 --> PE5

    %% Parsing to Structure
    PE3 --> SA1
    PE4 --> SA2
    PE5 --> SA3
    PE1 --> SA4
    PE2 --> SA5

    %% Structure to Relationship
    SA1 --> RE1
    SA2 --> RE2
    SA3 --> RE3
    SA4 --> RE4
    SA5 --> RE5

    %% Relationship to Metrics
    RE1 --> ME1
    RE2 --> ME2
    RE3 --> ME3
    RE4 --> ME4
    RE5 --> ME5

    %% Metrics to Intelligence
    ME1 --> IL1
    ME2 --> IL2
    ME3 --> IL3
    ME4 --> IL4
    ME5 --> IL5

    %% Cross-layer intelligence
    IL1 -.->|enhances| SA1
    IL2 -.->|improves| RE1
    IL3 -.->|optimizes| ME1
    IL4 -.->|guides| SP1
    IL5 -.->|learns_from| PE1

    %% Interactive styling with drill-down capability
    classDef sourceProcessing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,cursor:pointer
    classDef parsingEngine fill:#fff3e0,stroke:#f57c00,stroke-width:2px,cursor:pointer
    classDef structureAnalysis fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,cursor:pointer
    classDef relationshipEngine fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,cursor:pointer
    classDef metricsEngine fill:#ffebee,stroke:#d32f2f,stroke-width:2px,cursor:pointer
    classDef intelligenceLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px,cursor:pointer

    class SP1,SP2,SP3,SP4 sourceProcessing
    class PE1,PE2,PE3,PE4,PE5 parsingEngine
    class SA1,SA2,SA3,SA4,SA5 structureAnalysis
    class RE1,RE2,RE3,RE4,RE5 relationshipEngine
    class ME1,ME2,ME3,ME4,ME5 metricsEngine
    class IL1,IL2,IL3,IL4,IL5 intelligenceLayer
```

## 🌐 **Interactive API & UI Architecture**

```mermaid
graph TB
    subgraph "🌐 Interactive API & UI Architecture - Full Stack Explorer"
        subgraph "🎨 Frontend Layer [Component Explorer]"
            FL1[React Dashboard]
            FL2[Component Library]
            FL3[State Management]
            FL4[Real-time Updates]
            FL5[Visualization Components]

            click FL1 "#react-dashboard-architecture"
            click FL2 "#component-library-catalog"
            click FL3 "#state-management-patterns"
            click FL4 "#real-time-update-system"
            click FL5 "#visualization-component-gallery"
        end

        subgraph "🔌 API Gateway [Routing Explorer]"
            AG1[Request Router]
            AG2[Authentication Layer]
            AG3[Rate Limiting]
            AG4[Request Validation]
            AG5[Response Formatting]

            click AG1 "#routing-configuration"
            click AG2 "#authentication-strategies"
            click AG3 "#rate-limiting-policies"
            click AG4 "#validation-schemas"
            click AG5 "#response-formatting-rules"
        end

        subgraph "🚀 Backend Services [Service Explorer]"
            BS1[FastAPI Server]
            BS2[Endpoint Handlers]
            BS3[Business Logic]
            BS4[Data Access Layer]
            BS5[External Integrations]

            click BS1 "#fastapi-server-configuration"
            click BS2 "#endpoint-handler-catalog"
            click BS3 "#business-logic-modules"
            click BS4 "#data-access-patterns"
            click BS5 "#external-integration-configs"
        end

        subgraph "📡 Real-time Layer [WebSocket Explorer]"
            RL1[WebSocket Manager]
            RL2[Connection Handler]
            RL3[Message Router]
            RL4[Event Broadcaster]
            RL5[Subscription Manager]

            click RL1 "#websocket-management-system"
            click RL2 "#connection-handling-strategies"
            click RL3 "#message-routing-logic"
            click RL4 "#event-broadcasting-patterns"
            click RL5 "#subscription-management-dashboard"
        end

        subgraph "🔄 Data Layer [Transformation Explorer]"
            DL1[Data Transformers]
            DL2[Serializers]
            DL3[Validators]
            DL4[Formatters]
            DL5[Export Generators]

            click DL1 "#data-transformation-pipelines"
            click DL2 "#serialization-strategies"
            click DL3 "#validation-rule-engine"
            click DL4 "#formatting-templates"
            click DL5 "#export-generation-system"
        end

        subgraph "🛡️ Security Layer [Security Dashboard]"
            SL1[CORS Configuration]
            SL2[Security Headers]
            SL3[Input Sanitization]
            SL4[Access Control]
            SL5[Audit Logging]

            click SL1 "#cors-configuration-manager"
            click SL2 "#security-headers-policy"
            click SL3 "#input-sanitization-rules"
            click SL4 "#access-control-matrix"
            click SL5 "#audit-logging-dashboard"
        end
    end

    %% Frontend Layer Flow
    FL1 --> FL2
    FL2 --> FL3
    FL3 --> FL4
    FL4 --> FL5

    %% Frontend to API Gateway
    FL1 --> AG1
    FL4 --> AG2
    FL5 --> AG3

    %% API Gateway Flow
    AG1 --> AG2
    AG2 --> AG3
    AG3 --> AG4
    AG4 --> AG5

    %% API Gateway to Backend
    AG1 --> BS1
    AG4 --> BS2
    AG5 --> BS3

    %% Backend Services Flow
    BS1 --> BS2
    BS2 --> BS3
    BS3 --> BS4
    BS4 --> BS5

    %% Real-time Layer Integration
    FL4 --> RL1
    RL1 --> RL2
    RL2 --> RL3
    RL3 --> RL4
    RL4 --> RL5

    %% Data Layer Integration
    BS3 --> DL1
    DL1 --> DL2
    DL2 --> DL3
    DL3 --> DL4
    DL4 --> DL5

    %% Security Layer Integration
    AG2 --> SL1
    AG3 --> SL2
    AG4 --> SL3
    BS1 --> SL4
    BS2 --> SL5

    %% Cross-layer connections
    RL4 -.->|updates| FL4
    DL5 -.->|exports| FL5
    SL5 -.->|audits| AG1

    %% Interactive styling
    classDef frontendLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,cursor:pointer
    classDef apiGateway fill:#fff3e0,stroke:#f57c00,stroke-width:2px,cursor:pointer
    classDef backendServices fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,cursor:pointer
    classDef realtimeLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,cursor:pointer
    classDef dataLayer fill:#ffebee,stroke:#d32f2f,stroke-width:2px,cursor:pointer
    classDef securityLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px,cursor:pointer

    class FL1,FL2,FL3,FL4,FL5 frontendLayer
    class AG1,AG2,AG3,AG4,AG5 apiGateway
    class BS1,BS2,BS3,BS4,BS5 backendServices
    class RL1,RL2,RL3,RL4,RL5 realtimeLayer
    class DL1,DL2,DL3,DL4,DL5 dataLayer
    class SL1,SL2,SL3,SL4,SL5 securityLayer
```
