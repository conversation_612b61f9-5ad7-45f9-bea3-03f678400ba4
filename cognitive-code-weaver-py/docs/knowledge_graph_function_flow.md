# 🔄 Knowledge Graph System - Function Flow Graphs

## 📊 **Master Function Flow - Complete System**

```mermaid
graph TD
    subgraph "Knowledge Graph System - Function Flow"
        subgraph "Entry Points"
            EP1[create_knowledge_graph]
            EP2[KnowledgeGraphAgent.execute_task]
            EP3[GraphQueryEngine.execute_query]
            EP4[SemanticAnalyzer.analyze_semantics]
        end
        
        subgraph "Core Functions"
            CF1[KnowledgeGraph.initialize]
            CF2[KnowledgeGraphBuilder.build_from_analysis]
            CF3[ConceptExtractor.extract_concepts]
            CF4[RelationshipExtractor.extract_relationships]
            CF5[ConceptMapper.map_concepts]
            CF6[GraphDatabase.create_node]
            CF7[GraphDatabase.create_relationship]
            CF8[QueryEngine._execute_semantic_search]
        end
        
        subgraph "Analysis Functions"
            AF1[_extract_from_names]
            AF2[_extract_from_comments]
            AF3[_extract_from_docstrings]
            AF4[_extract_from_patterns]
            AF5[_analyze_name_semantics]
            AF6[_extract_structural_relationships]
            AF7[_extract_dependency_relationships]
            AF8[_extract_semantic_relationships]
        end
        
        subgraph "Database Functions"
            DF1[Neo4jDatabase.connect]
            DF2[Neo4jDatabase.create_node]
            DF3[Neo4jDatabase.create_relationship]
            DF4[Neo4jDatabase.query]
            DF5[MemoryGraphDatabase.create_node]
            DF6[MemoryGraphDatabase.get_node]
            DF7[MemoryGraphDatabase.get_relationships]
        end
        
        subgraph "Query Functions"
            QF1[_execute_semantic_search]
            QF2[_execute_relationship_query]
            QF3[_execute_path_finding]
            QF4[_execute_concept_exploration]
            QF5[_execute_natural_language_query]
            QF6[_parse_natural_language_query]
            QF7[_calculate_semantic_similarity]
        end
        
        subgraph "Mapping Functions"
            MF1[_map_single_concept]
            MF2[_determine_hierarchy_level]
            MF3[_calculate_abstraction_level]
            MF4[_build_concept_relationships]
            MF5[_create_taxonomy]
            MF6[_find_concept_clusters]
            MF7[_suggest_concept_hierarchy]
        end
        
        subgraph "LLM Functions"
            LF1[_enhance_with_llm]
            LF2[_create_semantic_analysis_prompt]
            LF3[_parse_llm_response]
            LF4[_enhance_with_llm_mapping]
            LF5[_create_concept_mapping_prompt]
            LF6[_apply_llm_mapping_suggestions]
        end
    end
    
    %% Entry Point Flows
    EP1 --> CF1
    EP2 --> CF2
    EP2 --> CF3
    EP2 --> QF1
    EP3 --> QF1
    EP3 --> QF2
    EP3 --> QF3
    EP3 --> QF4
    EP3 --> QF5
    EP4 --> CF3
    EP4 --> CF4
    
    %% Core Function Flows
    CF1 --> DF1
    CF2 --> CF3
    CF2 --> CF4
    CF2 --> CF5
    CF2 --> CF6
    CF2 --> CF7
    CF3 --> AF1
    CF3 --> AF2
    CF3 --> AF3
    CF3 --> AF4
    CF4 --> AF6
    CF4 --> AF7
    CF4 --> AF8
    CF5 --> MF1
    CF5 --> MF4
    CF6 --> DF2
    CF6 --> DF5
    CF7 --> DF3
    CF8 --> QF7
    
    %% Analysis Function Flows
    AF1 --> AF5
    AF2 --> LF1
    AF3 --> LF1
    AF4 --> AF5
    AF6 --> AF5
    AF7 --> DF4
    AF8 --> QF7
    
    %% Database Function Flows
    DF1 --> DF2
    DF2 --> DF3
    DF3 --> DF4
    DF5 --> DF6
    DF6 --> DF7
    
    %% Query Function Flows
    QF1 --> QF7
    QF2 --> DF4
    QF3 --> DF6
    QF4 --> DF7
    QF5 --> QF6
    QF5 --> LF1
    QF6 --> LF2
    QF7 --> MF1
    
    %% Mapping Function Flows
    MF1 --> MF2
    MF1 --> MF3
    MF2 --> MF3
    MF3 --> MF4
    MF4 --> MF5
    MF5 --> MF6
    MF6 --> MF7
    MF7 --> LF4
    
    %% LLM Function Flows
    LF1 --> LF2
    LF2 --> LF3
    LF3 --> CF3
    LF4 --> LF5
    LF5 --> LF6
    LF6 --> MF4
    
    %% Styling
    classDef entryPoint fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    classDef coreFunc fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef analysisFunc fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef dbFunc fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef queryFunc fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff
    classDef mappingFunc fill:#00bcd4,stroke:#00838f,stroke-width:2px,color:#fff
    classDef llmFunc fill:#8bc34a,stroke:#558b2f,stroke-width:2px,color:#fff
    
    class EP1,EP2,EP3,EP4 entryPoint
    class CF1,CF2,CF3,CF4,CF5,CF6,CF7,CF8 coreFunc
    class AF1,AF2,AF3,AF4,AF5,AF6,AF7,AF8 analysisFunc
    class DF1,DF2,DF3,DF4,DF5,DF6,DF7 dbFunc
    class QF1,QF2,QF3,QF4,QF5,QF6,QF7 queryFunc
    class MF1,MF2,MF3,MF4,MF5,MF6,MF7 mappingFunc
    class LF1,LF2,LF3,LF4,LF5,LF6 llmFunc
```

## 🧠 **Semantic Analysis Function Flow**

```mermaid
graph TD
    subgraph "Semantic Analysis Deep Dive"
        START([analyze_semantics])
        
        subgraph "Concept Extraction Chain"
            EXTRACT[extract_concepts]
            FROM_NAMES[_extract_from_names]
            FROM_COMMENTS[_extract_from_comments]
            FROM_DOCS[_extract_from_docstrings]
            FROM_PATTERNS[_extract_from_patterns]
        end
        
        subgraph "Name Analysis Chain"
            ANALYZE_NAME[_analyze_name_semantics]
            SPLIT_ID[_split_identifier]
            CHECK_DOMAIN[check_domain_patterns]
            CHECK_TECH[check_technical_patterns]
            CREATE_CONCEPT[create_semantic_concept]
        end
        
        subgraph "Pattern Analysis Chain"
            ANALYZE_CLASS[_analyze_class_patterns]
            ANALYZE_FUNC[_analyze_function_patterns]
            DETECT_DESIGN[detect_design_patterns]
            DETECT_ARCH[detect_architecture_patterns]
            EXTRACT_ALGO[extract_algorithm_patterns]
        end
        
        subgraph "LLM Enhancement Chain"
            ENHANCE_LLM[_enhance_with_llm]
            CREATE_PROMPT[_create_semantic_analysis_prompt]
            CALL_LLM[call_llm_api]
            PARSE_RESPONSE[_parse_llm_response]
            MERGE_CONCEPTS[merge_enhanced_concepts]
        end
        
        subgraph "Relationship Chain"
            EXTRACT_RELS[_extract_relationships]
            ANALYZE_COOCCUR[_analyze_concept_relationship]
            DETERMINE_TYPE[_determine_relationship_type]
            CREATE_REL[create_semantic_relationship]
        end
        
        subgraph "Finalization Chain"
            MERGE_SIMILAR[_merge_similar_concepts]
            VALIDATE[validate_concepts]
            CALCULATE_CONF[calculate_confidence]
            RETURN_RESULTS[return_analysis_results]
        end
        
        END([Return Concepts & Relationships])
    end
    
    %% Main Flow
    START --> EXTRACT
    
    %% Extraction Flow
    EXTRACT --> FROM_NAMES
    EXTRACT --> FROM_COMMENTS
    EXTRACT --> FROM_DOCS
    EXTRACT --> FROM_PATTERNS
    
    %% Name Analysis Flow
    FROM_NAMES --> ANALYZE_NAME
    ANALYZE_NAME --> SPLIT_ID
    SPLIT_ID --> CHECK_DOMAIN
    CHECK_DOMAIN --> CHECK_TECH
    CHECK_TECH --> CREATE_CONCEPT
    
    %% Pattern Analysis Flow
    FROM_PATTERNS --> ANALYZE_CLASS
    FROM_PATTERNS --> ANALYZE_FUNC
    ANALYZE_CLASS --> DETECT_DESIGN
    ANALYZE_FUNC --> DETECT_ARCH
    DETECT_DESIGN --> EXTRACT_ALGO
    DETECT_ARCH --> EXTRACT_ALGO
    EXTRACT_ALGO --> CREATE_CONCEPT
    
    %% LLM Enhancement Flow
    CREATE_CONCEPT --> ENHANCE_LLM
    ENHANCE_LLM --> CREATE_PROMPT
    CREATE_PROMPT --> CALL_LLM
    CALL_LLM --> PARSE_RESPONSE
    PARSE_RESPONSE --> MERGE_CONCEPTS
    
    %% Relationship Flow
    MERGE_CONCEPTS --> EXTRACT_RELS
    EXTRACT_RELS --> ANALYZE_COOCCUR
    ANALYZE_COOCCUR --> DETERMINE_TYPE
    DETERMINE_TYPE --> CREATE_REL
    
    %% Finalization Flow
    CREATE_REL --> MERGE_SIMILAR
    MERGE_SIMILAR --> VALIDATE
    VALIDATE --> CALCULATE_CONF
    CALCULATE_CONF --> RETURN_RESULTS
    RETURN_RESULTS --> END
    
    %% Cross-connections
    FROM_COMMENTS --> ENHANCE_LLM
    FROM_DOCS --> ENHANCE_LLM
    
    %% Styling
    classDef startEnd fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    classDef extraction fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef nameAnalysis fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef patternAnalysis fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef llmEnhancement fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff
    classDef relationship fill:#00bcd4,stroke:#00838f,stroke-width:2px,color:#fff
    classDef finalization fill:#8bc34a,stroke:#558b2f,stroke-width:2px,color:#fff
    
    class START,END startEnd
    class EXTRACT,FROM_NAMES,FROM_COMMENTS,FROM_DOCS,FROM_PATTERNS extraction
    class ANALYZE_NAME,SPLIT_ID,CHECK_DOMAIN,CHECK_TECH,CREATE_CONCEPT nameAnalysis
    class ANALYZE_CLASS,ANALYZE_FUNC,DETECT_DESIGN,DETECT_ARCH,EXTRACT_ALGO patternAnalysis
    class ENHANCE_LLM,CREATE_PROMPT,CALL_LLM,PARSE_RESPONSE,MERGE_CONCEPTS llmEnhancement
    class EXTRACT_RELS,ANALYZE_COOCCUR,DETERMINE_TYPE,CREATE_REL relationship
    class MERGE_SIMILAR,VALIDATE,CALCULATE_CONF,RETURN_RESULTS finalization
```

## 🔍 **Query Engine Function Flow**

```mermaid
graph TD
    subgraph "Query Engine Processing Flow"
        START([execute_query])
        
        subgraph "Query Preprocessing"
            PARSE[parse_query_input]
            VALIDATE[validate_query_params]
            ROUTE[route_by_query_type]
        end
        
        subgraph "Semantic Search Flow"
            SS_START[_execute_semantic_search]
            SS_EXTRACT[_extract_search_terms]
            SS_CONCEPTS[search_concept_nodes]
            SS_SYMBOLS[search_symbol_nodes]
            SS_CALC[_calculate_semantic_similarity]
            SS_RANK[rank_by_confidence]
            SS_LIMIT[limit_results]
        end
        
        subgraph "Relationship Query Flow"
            RQ_START[_execute_relationship_query]
            RQ_VALIDATE[validate_node_ids]
            RQ_GET_RELS[get_relationships_from_db]
            RQ_FILTER[filter_by_relationship_type]
            RQ_GET_NODES[get_related_nodes]
            RQ_BUILD[build_relationship_result]
        end
        
        subgraph "Path Finding Flow"
            PF_START[_execute_path_finding]
            PF_VALIDATE[validate_source_target]
            PF_BFS[breadth_first_search]
            PF_BUILD_PATH[build_path_result]
            PF_GET_RELS[get_path_relationships]
            PF_FORMAT[format_path_response]
        end
        
        subgraph "Natural Language Flow"
            NL_START[_execute_natural_language_query]
            NL_PROMPT[_create_nl_prompt]
            NL_LLM[call_llm_for_parsing]
            NL_PARSE[_parse_natural_language_query]
            NL_CONVERT[_convert_to_structured_query]
            NL_EXECUTE[execute_converted_query]
        end
        
        subgraph "Result Processing"
            COLLECT[collect_all_results]
            ENHANCE[enhance_with_relationships]
            CALCULATE[calculate_execution_time]
            FORMAT[format_final_response]
            CACHE[cache_query_results]
        end
        
        END([Return QueryResult])
    end
    
    %% Main Flow
    START --> PARSE
    PARSE --> VALIDATE
    VALIDATE --> ROUTE
    
    %% Route to specific handlers
    ROUTE --> SS_START
    ROUTE --> RQ_START
    ROUTE --> PF_START
    ROUTE --> NL_START
    
    %% Semantic Search Flow
    SS_START --> SS_EXTRACT
    SS_EXTRACT --> SS_CONCEPTS
    SS_CONCEPTS --> SS_SYMBOLS
    SS_SYMBOLS --> SS_CALC
    SS_CALC --> SS_RANK
    SS_RANK --> SS_LIMIT
    SS_LIMIT --> COLLECT
    
    %% Relationship Query Flow
    RQ_START --> RQ_VALIDATE
    RQ_VALIDATE --> RQ_GET_RELS
    RQ_GET_RELS --> RQ_FILTER
    RQ_FILTER --> RQ_GET_NODES
    RQ_GET_NODES --> RQ_BUILD
    RQ_BUILD --> COLLECT
    
    %% Path Finding Flow
    PF_START --> PF_VALIDATE
    PF_VALIDATE --> PF_BFS
    PF_BFS --> PF_BUILD_PATH
    PF_BUILD_PATH --> PF_GET_RELS
    PF_GET_RELS --> PF_FORMAT
    PF_FORMAT --> COLLECT
    
    %% Natural Language Flow
    NL_START --> NL_PROMPT
    NL_PROMPT --> NL_LLM
    NL_LLM --> NL_PARSE
    NL_PARSE --> NL_CONVERT
    NL_CONVERT --> NL_EXECUTE
    NL_EXECUTE --> COLLECT
    
    %% Result Processing Flow
    COLLECT --> ENHANCE
    ENHANCE --> CALCULATE
    CALCULATE --> FORMAT
    FORMAT --> CACHE
    CACHE --> END
    
    %% Styling
    classDef startEnd fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    classDef preprocessing fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef semanticSearch fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef relationshipQuery fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef pathFinding fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff
    classDef naturalLanguage fill:#00bcd4,stroke:#00838f,stroke-width:2px,color:#fff
    classDef resultProcessing fill:#8bc34a,stroke:#558b2f,stroke-width:2px,color:#fff
    
    class START,END startEnd
    class PARSE,VALIDATE,ROUTE preprocessing
    class SS_START,SS_EXTRACT,SS_CONCEPTS,SS_SYMBOLS,SS_CALC,SS_RANK,SS_LIMIT semanticSearch
    class RQ_START,RQ_VALIDATE,RQ_GET_RELS,RQ_FILTER,RQ_GET_NODES,RQ_BUILD relationshipQuery
    class PF_START,PF_VALIDATE,PF_BFS,PF_BUILD_PATH,PF_GET_RELS,PF_FORMAT pathFinding
    class NL_START,NL_PROMPT,NL_LLM,NL_PARSE,NL_CONVERT,NL_EXECUTE naturalLanguage
    class COLLECT,ENHANCE,CALCULATE,FORMAT,CACHE resultProcessing

## 🗄️ **Database Operations Function Flow**

```mermaid
graph TD
    subgraph "Database Layer Function Flow"
        subgraph "Connection Management"
            CONNECT[connect]
            VALIDATE_CONN[validate_connection]
            SETUP_DRIVER[setup_database_driver]
            TEST_CONN[test_connection]
            DISCONNECT[disconnect]
        end

        subgraph "Node Operations"
            CREATE_NODE[create_node]
            VALIDATE_NODE[validate_node_data]
            SERIALIZE_PROPS[serialize_properties]
            EXECUTE_CREATE[execute_create_query]
            UPDATE_CACHE[update_node_cache]

            GET_NODE[get_node]
            QUERY_BY_ID[query_node_by_id]
            DESERIALIZE[deserialize_node_data]
            CACHE_NODE[cache_retrieved_node]

            UPDATE_NODE[update_node]
            MERGE_PROPS[merge_properties]
            EXECUTE_UPDATE[execute_update_query]
            INVALIDATE_CACHE[invalidate_node_cache]

            DELETE_NODE[delete_node]
            CASCADE_DELETE[cascade_delete_relationships]
            EXECUTE_DELETE[execute_delete_query]
            REMOVE_FROM_CACHE[remove_from_cache]
        end

        subgraph "Relationship Operations"
            CREATE_REL[create_relationship]
            VALIDATE_REL[validate_relationship_data]
            CHECK_NODES[check_source_target_exist]
            SERIALIZE_REL[serialize_relationship_props]
            EXECUTE_REL_CREATE[execute_relationship_create]

            GET_RELS[get_relationships]
            QUERY_RELS[query_relationships_by_node]
            FILTER_RELS[filter_by_relationship_type]
            BUILD_REL_LIST[build_relationship_list]
        end

        subgraph "Query Operations"
            EXECUTE_QUERY[execute_custom_query]
            PARSE_QUERY[parse_query_string]
            VALIDATE_PARAMS[validate_query_parameters]
            EXECUTE_DB_QUERY[execute_database_query]
            PROCESS_RESULTS[process_query_results]
            FORMAT_RESPONSE[format_query_response]
        end

        subgraph "Performance Operations"
            BATCH_CREATE[batch_create_nodes]
            BATCH_UPDATE[batch_update_nodes]
            OPTIMIZE_QUERY[optimize_query_execution]
            MANAGE_INDEXES[manage_database_indexes]
            MONITOR_PERF[monitor_performance]
        end
    end

    %% Connection Flow
    CONNECT --> VALIDATE_CONN
    VALIDATE_CONN --> SETUP_DRIVER
    SETUP_DRIVER --> TEST_CONN

    %% Node Creation Flow
    CREATE_NODE --> VALIDATE_NODE
    VALIDATE_NODE --> SERIALIZE_PROPS
    SERIALIZE_PROPS --> EXECUTE_CREATE
    EXECUTE_CREATE --> UPDATE_CACHE

    %% Node Retrieval Flow
    GET_NODE --> QUERY_BY_ID
    QUERY_BY_ID --> DESERIALIZE
    DESERIALIZE --> CACHE_NODE

    %% Node Update Flow
    UPDATE_NODE --> MERGE_PROPS
    MERGE_PROPS --> EXECUTE_UPDATE
    EXECUTE_UPDATE --> INVALIDATE_CACHE

    %% Node Deletion Flow
    DELETE_NODE --> CASCADE_DELETE
    CASCADE_DELETE --> EXECUTE_DELETE
    EXECUTE_DELETE --> REMOVE_FROM_CACHE

    %% Relationship Creation Flow
    CREATE_REL --> VALIDATE_REL
    VALIDATE_REL --> CHECK_NODES
    CHECK_NODES --> SERIALIZE_REL
    SERIALIZE_REL --> EXECUTE_REL_CREATE

    %% Relationship Retrieval Flow
    GET_RELS --> QUERY_RELS
    QUERY_RELS --> FILTER_RELS
    FILTER_RELS --> BUILD_REL_LIST

    %% Query Execution Flow
    EXECUTE_QUERY --> PARSE_QUERY
    PARSE_QUERY --> VALIDATE_PARAMS
    VALIDATE_PARAMS --> EXECUTE_DB_QUERY
    EXECUTE_DB_QUERY --> PROCESS_RESULTS
    PROCESS_RESULTS --> FORMAT_RESPONSE

    %% Performance Flow
    BATCH_CREATE --> OPTIMIZE_QUERY
    BATCH_UPDATE --> OPTIMIZE_QUERY
    OPTIMIZE_QUERY --> MANAGE_INDEXES
    MANAGE_INDEXES --> MONITOR_PERF

    %% Cross-connections
    EXECUTE_CREATE --> MONITOR_PERF
    EXECUTE_UPDATE --> MONITOR_PERF
    EXECUTE_DELETE --> MONITOR_PERF
    EXECUTE_REL_CREATE --> MONITOR_PERF

    %% Styling
    classDef connection fill:#4caf50,stroke:#2e7d32,stroke-width:2px,color:#fff
    classDef nodeOps fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef relOps fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef queryOps fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef perfOps fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff

    class CONNECT,VALIDATE_CONN,SETUP_DRIVER,TEST_CONN,DISCONNECT connection
    class CREATE_NODE,VALIDATE_NODE,SERIALIZE_PROPS,EXECUTE_CREATE,UPDATE_CACHE,GET_NODE,QUERY_BY_ID,DESERIALIZE,CACHE_NODE,UPDATE_NODE,MERGE_PROPS,EXECUTE_UPDATE,INVALIDATE_CACHE,DELETE_NODE,CASCADE_DELETE,EXECUTE_DELETE,REMOVE_FROM_CACHE nodeOps
    class CREATE_REL,VALIDATE_REL,CHECK_NODES,SERIALIZE_REL,EXECUTE_REL_CREATE,GET_RELS,QUERY_RELS,FILTER_RELS,BUILD_REL_LIST relOps
    class EXECUTE_QUERY,PARSE_QUERY,VALIDATE_PARAMS,EXECUTE_DB_QUERY,PROCESS_RESULTS,FORMAT_RESPONSE queryOps
    class BATCH_CREATE,BATCH_UPDATE,OPTIMIZE_QUERY,MANAGE_INDEXES,MONITOR_PERF perfOps
```

## 🧩 **Concept Mapping Function Flow**

```mermaid
graph TD
    subgraph "Concept Mapping Function Flow"
        START([map_concepts])

        subgraph "Individual Concept Processing"
            MAP_SINGLE[_map_single_concept]
            DETERMINE_HIERARCHY[_determine_hierarchy_level]
            CALC_ABSTRACTION[_calculate_abstraction_level]
            CALC_DOMAIN[_calculate_domain_relevance]
            CREATE_TAXONOMY[_create_taxonomy_path]
            BUILD_CODE_CONCEPT[build_code_concept]
        end

        subgraph "Relationship Building"
            BUILD_RELATIONSHIPS[_build_concept_relationships]
            CALC_SIMILARITY[_calculate_concept_similarity]
            ANALYZE_RELATIONSHIP[_analyze_concept_relationship]
            FIND_PARENTS[_find_potential_parents]
            CREATE_HIERARCHY[create_concept_hierarchy]
        end

        subgraph "Clustering Operations"
            FIND_CLUSTERS[find_concept_clusters]
            GROUP_SIMILAR[group_similar_concepts]
            CALC_CLUSTER_SCORE[calculate_cluster_similarity]
            VALIDATE_CLUSTERS[validate_cluster_coherence]
            MERGE_CLUSTERS[merge_overlapping_clusters]
        end

        subgraph "Hierarchy Construction"
            SUGGEST_HIERARCHY[suggest_concept_hierarchy]
            GROUP_BY_ABSTRACTION[group_by_abstraction_level]
            BUILD_TREE[build_hierarchy_tree]
            VALIDATE_HIERARCHY[validate_hierarchy_structure]
            OPTIMIZE_STRUCTURE[optimize_hierarchy_structure]
        end

        subgraph "LLM Enhancement"
            ENHANCE_MAPPING[enhance_with_llm_mapping]
            CREATE_MAPPING_PROMPT[_create_concept_mapping_prompt]
            CALL_LLM_MAPPING[call_llm_for_mapping]
            PARSE_SUGGESTIONS[_parse_llm_response]
            APPLY_SUGGESTIONS[_apply_llm_mapping_suggestions]
        end

        subgraph "Taxonomy Creation"
            CREATE_TAXONOMY_OBJ[create_taxonomy]
            ORGANIZE_BY_LEVELS[organize_concepts_by_levels]
            BUILD_PARENT_CHILD[build_parent_child_relationships]
            CALC_DISTRIBUTIONS[calculate_concept_distributions]
            ADD_METADATA[add_taxonomy_metadata]
        end

        END([Return Mapped Concepts])
    end

    %% Main Flow
    START --> MAP_SINGLE

    %% Individual Processing Flow
    MAP_SINGLE --> DETERMINE_HIERARCHY
    DETERMINE_HIERARCHY --> CALC_ABSTRACTION
    CALC_ABSTRACTION --> CALC_DOMAIN
    CALC_DOMAIN --> CREATE_TAXONOMY
    CREATE_TAXONOMY --> BUILD_CODE_CONCEPT

    %% Relationship Building Flow
    BUILD_CODE_CONCEPT --> BUILD_RELATIONSHIPS
    BUILD_RELATIONSHIPS --> CALC_SIMILARITY
    CALC_SIMILARITY --> ANALYZE_RELATIONSHIP
    ANALYZE_RELATIONSHIP --> FIND_PARENTS
    FIND_PARENTS --> CREATE_HIERARCHY

    %% Clustering Flow
    CREATE_HIERARCHY --> FIND_CLUSTERS
    FIND_CLUSTERS --> GROUP_SIMILAR
    GROUP_SIMILAR --> CALC_CLUSTER_SCORE
    CALC_CLUSTER_SCORE --> VALIDATE_CLUSTERS
    VALIDATE_CLUSTERS --> MERGE_CLUSTERS

    %% Hierarchy Construction Flow
    MERGE_CLUSTERS --> SUGGEST_HIERARCHY
    SUGGEST_HIERARCHY --> GROUP_BY_ABSTRACTION
    GROUP_BY_ABSTRACTION --> BUILD_TREE
    BUILD_TREE --> VALIDATE_HIERARCHY
    VALIDATE_HIERARCHY --> OPTIMIZE_STRUCTURE

    %% LLM Enhancement Flow
    OPTIMIZE_STRUCTURE --> ENHANCE_MAPPING
    ENHANCE_MAPPING --> CREATE_MAPPING_PROMPT
    CREATE_MAPPING_PROMPT --> CALL_LLM_MAPPING
    CALL_LLM_MAPPING --> PARSE_SUGGESTIONS
    PARSE_SUGGESTIONS --> APPLY_SUGGESTIONS

    %% Taxonomy Creation Flow
    APPLY_SUGGESTIONS --> CREATE_TAXONOMY_OBJ
    CREATE_TAXONOMY_OBJ --> ORGANIZE_BY_LEVELS
    ORGANIZE_BY_LEVELS --> BUILD_PARENT_CHILD
    BUILD_PARENT_CHILD --> CALC_DISTRIBUTIONS
    CALC_DISTRIBUTIONS --> ADD_METADATA

    ADD_METADATA --> END

    %% Cross-connections
    CALC_SIMILARITY --> ENHANCE_MAPPING
    FIND_PARENTS --> SUGGEST_HIERARCHY

    %% Styling
    classDef startEnd fill:#4caf50,stroke:#2e7d32,stroke-width:3px,color:#fff
    classDef individual fill:#2196f3,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef relationship fill:#ff9800,stroke:#ef6c00,stroke-width:2px,color:#fff
    classDef clustering fill:#9c27b0,stroke:#6a1b9a,stroke-width:2px,color:#fff
    classDef hierarchy fill:#f44336,stroke:#c62828,stroke-width:2px,color:#fff
    classDef llmEnhancement fill:#00bcd4,stroke:#00838f,stroke-width:2px,color:#fff
    classDef taxonomy fill:#8bc34a,stroke:#558b2f,stroke-width:2px,color:#fff

    class START,END startEnd
    class MAP_SINGLE,DETERMINE_HIERARCHY,CALC_ABSTRACTION,CALC_DOMAIN,CREATE_TAXONOMY,BUILD_CODE_CONCEPT individual
    class BUILD_RELATIONSHIPS,CALC_SIMILARITY,ANALYZE_RELATIONSHIP,FIND_PARENTS,CREATE_HIERARCHY relationship
    class FIND_CLUSTERS,GROUP_SIMILAR,CALC_CLUSTER_SCORE,VALIDATE_CLUSTERS,MERGE_CLUSTERS clustering
    class SUGGEST_HIERARCHY,GROUP_BY_ABSTRACTION,BUILD_TREE,VALIDATE_HIERARCHY,OPTIMIZE_STRUCTURE hierarchy
    class ENHANCE_MAPPING,CREATE_MAPPING_PROMPT,CALL_LLM_MAPPING,PARSE_SUGGESTIONS,APPLY_SUGGESTIONS llmEnhancement
    class CREATE_TAXONOMY_OBJ,ORGANIZE_BY_LEVELS,BUILD_PARENT_CHILD,CALC_DISTRIBUTIONS,ADD_METADATA taxonomy
```
```
