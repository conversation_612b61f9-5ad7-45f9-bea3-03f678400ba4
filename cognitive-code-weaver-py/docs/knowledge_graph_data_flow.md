# 📊 Knowledge Graph System - Data Flow Diagrams

## 🌊 **Master Data Flow - Complete System**

```mermaid
graph TD
    subgraph "Knowledge Graph Data Flow"
        subgraph "Input Data Sources"
            SRC_CODE[Source Code Files]
            PARSE_RESULTS[Parse Results]
            SYMBOLS[Symbol Data]
            DEPENDENCIES[Dependency Graph]
            USER_QUERIES[User Queries]
        end
        
        subgraph "Data Transformation Layer"
            PARSER[Code Parser]
            SYMBOL_EXTRACTOR[Symbol Extractor]
            DEP_ANALYZER[Dependency Analyzer]
            SEMANTIC_ANALYZER[Semantic Analyzer]
            CONCEPT_EXTRACTOR[Concept Extractor]
        end
        
        subgraph "Semantic Processing"
            CONCEPTS[Semantic Concepts]
            RELATIONSHIPS[Semantic Relationships]
            HIERARCHIES[Concept Hierarchies]
            TAXONOMIES[Concept Taxonomies]
            CLUSTERS[Concept Clusters]
        end
        
        subgraph "Graph Construction"
            GRAPH_NODES[Graph Nodes]
            GRAPH_RELATIONSHIPS[Graph Relationships]
            GRAPH_METADATA[Graph Metadata]
            GRAPH_STATS[Graph Statistics]
        end
        
        subgraph "Storage Layer"
            NEO4J_DB[(Neo4j Database)]
            MEMORY_GRAPH[(Memory Graph)]
            QUERY_CACHE[(Query Cache)]
            NODE_CACHE[(Node Cache)]
        end
        
        subgraph "Query Processing"
            QUERY_PARSER[Query Parser]
            SEMANTIC_SEARCH[Semantic Search]
            RELATIONSHIP_QUERY[Relationship Query]
            PATH_FINDER[Path Finder]
            NL_PROCESSOR[NL Processor]
        end
        
        subgraph "Output Data"
            QUERY_RESULTS[Query Results]
            VISUALIZATIONS[Visualizations]
            API_RESPONSES[API Responses]
            REPORTS[Analysis Reports]
        end
    end
    
    %% Input Flow
    SRC_CODE --> PARSER
    PARSER --> PARSE_RESULTS
    PARSE_RESULTS --> SYMBOL_EXTRACTOR
    SYMBOL_EXTRACTOR --> SYMBOLS
    SYMBOLS --> DEP_ANALYZER
    DEP_ANALYZER --> DEPENDENCIES
    
    %% Semantic Processing Flow
    PARSE_RESULTS --> SEMANTIC_ANALYZER
    SYMBOLS --> SEMANTIC_ANALYZER
    SEMANTIC_ANALYZER --> CONCEPT_EXTRACTOR
    CONCEPT_EXTRACTOR --> CONCEPTS
    CONCEPTS --> RELATIONSHIPS
    RELATIONSHIPS --> HIERARCHIES
    HIERARCHIES --> TAXONOMIES
    TAXONOMIES --> CLUSTERS
    
    %% Graph Construction Flow
    CONCEPTS --> GRAPH_NODES
    RELATIONSHIPS --> GRAPH_RELATIONSHIPS
    HIERARCHIES --> GRAPH_METADATA
    CLUSTERS --> GRAPH_STATS
    
    %% Storage Flow
    GRAPH_NODES --> NEO4J_DB
    GRAPH_NODES --> MEMORY_GRAPH
    GRAPH_RELATIONSHIPS --> NEO4J_DB
    GRAPH_RELATIONSHIPS --> MEMORY_GRAPH
    GRAPH_METADATA --> QUERY_CACHE
    GRAPH_STATS --> NODE_CACHE
    
    %% Query Flow
    USER_QUERIES --> QUERY_PARSER
    QUERY_PARSER --> SEMANTIC_SEARCH
    QUERY_PARSER --> RELATIONSHIP_QUERY
    QUERY_PARSER --> PATH_FINDER
    QUERY_PARSER --> NL_PROCESSOR
    
    %% Query Execution Flow
    SEMANTIC_SEARCH --> NEO4J_DB
    SEMANTIC_SEARCH --> MEMORY_GRAPH
    RELATIONSHIP_QUERY --> NEO4J_DB
    PATH_FINDER --> MEMORY_GRAPH
    NL_PROCESSOR --> SEMANTIC_SEARCH
    
    %% Output Flow
    NEO4J_DB --> QUERY_RESULTS
    MEMORY_GRAPH --> QUERY_RESULTS
    QUERY_CACHE --> QUERY_RESULTS
    QUERY_RESULTS --> VISUALIZATIONS
    QUERY_RESULTS --> API_RESPONSES
    QUERY_RESULTS --> REPORTS
    
    %% Styling
    classDef inputData fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef transformation fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef semantic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef graph fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef query fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef output fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    
    class SRC_CODE,PARSE_RESULTS,SYMBOLS,DEPENDENCIES,USER_QUERIES inputData
    class PARSER,SYMBOL_EXTRACTOR,DEP_ANALYZER,SEMANTIC_ANALYZER,CONCEPT_EXTRACTOR transformation
    class CONCEPTS,RELATIONSHIPS,HIERARCHIES,TAXONOMIES,CLUSTERS semantic
    class GRAPH_NODES,GRAPH_RELATIONSHIPS,GRAPH_METADATA,GRAPH_STATS graph
    class NEO4J_DB,MEMORY_GRAPH,QUERY_CACHE,NODE_CACHE storage
    class QUERY_PARSER,SEMANTIC_SEARCH,RELATIONSHIP_QUERY,PATH_FINDER,NL_PROCESSOR query
    class QUERY_RESULTS,VISUALIZATIONS,API_RESPONSES,REPORTS output
```

## 🔄 **Semantic Analysis Data Flow**

```mermaid
graph TD
    subgraph "Semantic Analysis Data Pipeline"
        subgraph "Input Data"
            CODE_FILE[Code File Content]
            SYMBOL_LIST[Symbol List]
            METADATA[File Metadata]
        end
        
        subgraph "Text Extraction"
            NAMES[Symbol Names]
            COMMENTS[Code Comments]
            DOCSTRINGS[Docstrings]
            PATTERNS[Code Patterns]
        end
        
        subgraph "Concept Identification"
            DOMAIN_TERMS[Domain Terms]
            TECH_TERMS[Technical Terms]
            BUSINESS_TERMS[Business Terms]
            PATTERN_CONCEPTS[Pattern Concepts]
        end
        
        subgraph "LLM Enhancement"
            LLM_PROMPT[LLM Prompt Data]
            LLM_RESPONSE[LLM Response]
            ENHANCED_CONCEPTS[Enhanced Concepts]
        end
        
        subgraph "Concept Processing"
            RAW_CONCEPTS[Raw Concepts]
            MERGED_CONCEPTS[Merged Concepts]
            VALIDATED_CONCEPTS[Validated Concepts]
            FINAL_CONCEPTS[Final Concepts]
        end
        
        subgraph "Relationship Analysis"
            COOCCURRENCE[Co-occurrence Data]
            SIMILARITY_SCORES[Similarity Scores]
            RELATIONSHIP_TYPES[Relationship Types]
            SEMANTIC_RELATIONS[Semantic Relations]
        end
        
        subgraph "Output Data"
            CONCEPT_LIST[Concept List]
            RELATIONSHIP_LIST[Relationship List]
            CONFIDENCE_SCORES[Confidence Scores]
            ANALYSIS_METADATA[Analysis Metadata]
        end
    end
    
    %% Input Processing
    CODE_FILE --> NAMES
    CODE_FILE --> COMMENTS
    CODE_FILE --> DOCSTRINGS
    SYMBOL_LIST --> NAMES
    SYMBOL_LIST --> PATTERNS
    METADATA --> PATTERNS
    
    %% Text to Concepts
    NAMES --> DOMAIN_TERMS
    NAMES --> TECH_TERMS
    COMMENTS --> BUSINESS_TERMS
    DOCSTRINGS --> BUSINESS_TERMS
    PATTERNS --> PATTERN_CONCEPTS
    
    %% Concept Consolidation
    DOMAIN_TERMS --> RAW_CONCEPTS
    TECH_TERMS --> RAW_CONCEPTS
    BUSINESS_TERMS --> RAW_CONCEPTS
    PATTERN_CONCEPTS --> RAW_CONCEPTS
    
    %% LLM Enhancement Flow
    RAW_CONCEPTS --> LLM_PROMPT
    LLM_PROMPT --> LLM_RESPONSE
    LLM_RESPONSE --> ENHANCED_CONCEPTS
    ENHANCED_CONCEPTS --> MERGED_CONCEPTS
    
    %% Concept Processing Flow
    RAW_CONCEPTS --> MERGED_CONCEPTS
    MERGED_CONCEPTS --> VALIDATED_CONCEPTS
    VALIDATED_CONCEPTS --> FINAL_CONCEPTS
    
    %% Relationship Analysis Flow
    FINAL_CONCEPTS --> COOCCURRENCE
    COOCCURRENCE --> SIMILARITY_SCORES
    SIMILARITY_SCORES --> RELATIONSHIP_TYPES
    RELATIONSHIP_TYPES --> SEMANTIC_RELATIONS
    
    %% Output Generation
    FINAL_CONCEPTS --> CONCEPT_LIST
    SEMANTIC_RELATIONS --> RELATIONSHIP_LIST
    SIMILARITY_SCORES --> CONFIDENCE_SCORES
    VALIDATED_CONCEPTS --> ANALYSIS_METADATA
    
    %% Final Output
    CONCEPT_LIST --> ANALYSIS_METADATA
    RELATIONSHIP_LIST --> ANALYSIS_METADATA
    CONFIDENCE_SCORES --> ANALYSIS_METADATA
    
    %% Styling
    classDef inputData fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef textExtraction fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef conceptId fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef llmEnhancement fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef conceptProc fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef relationshipAnalysis fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef outputData fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    
    class CODE_FILE,SYMBOL_LIST,METADATA inputData
    class NAMES,COMMENTS,DOCSTRINGS,PATTERNS textExtraction
    class DOMAIN_TERMS,TECH_TERMS,BUSINESS_TERMS,PATTERN_CONCEPTS conceptId
    class LLM_PROMPT,LLM_RESPONSE,ENHANCED_CONCEPTS llmEnhancement
    class RAW_CONCEPTS,MERGED_CONCEPTS,VALIDATED_CONCEPTS,FINAL_CONCEPTS conceptProc
    class COOCCURRENCE,SIMILARITY_SCORES,RELATIONSHIP_TYPES,SEMANTIC_RELATIONS relationshipAnalysis
    class CONCEPT_LIST,RELATIONSHIP_LIST,CONFIDENCE_SCORES,ANALYSIS_METADATA outputData
```

## 🗄️ **Graph Database Data Flow**

```mermaid
graph TD
    subgraph "Graph Database Data Flow"
        subgraph "Input Operations"
            NODE_CREATE_REQ[Node Create Request]
            REL_CREATE_REQ[Relationship Create Request]
            QUERY_REQ[Query Request]
            UPDATE_REQ[Update Request]
        end
        
        subgraph "Data Validation"
            VALIDATE_NODE[Validate Node Data]
            VALIDATE_REL[Validate Relationship Data]
            VALIDATE_QUERY[Validate Query Parameters]
            SANITIZE_INPUT[Sanitize Input Data]
        end
        
        subgraph "Data Serialization"
            SERIALIZE_PROPS[Serialize Properties]
            SERIALIZE_METADATA[Serialize Metadata]
            FORMAT_QUERY[Format Query String]
            PREPARE_PARAMS[Prepare Parameters]
        end
        
        subgraph "Database Operations"
            EXECUTE_CREATE[Execute Create Operation]
            EXECUTE_QUERY[Execute Query Operation]
            EXECUTE_UPDATE[Execute Update Operation]
            EXECUTE_DELETE[Execute Delete Operation]
        end
        
        subgraph "Result Processing"
            RAW_RESULTS[Raw Database Results]
            DESERIALIZE_DATA[Deserialize Data]
            BUILD_OBJECTS[Build Graph Objects]
            CALCULATE_METRICS[Calculate Metrics]
        end
        
        subgraph "Cache Management"
            CHECK_CACHE[Check Cache]
            UPDATE_CACHE[Update Cache]
            INVALIDATE_CACHE[Invalidate Cache]
            CACHE_RESULTS[Cache Results]
        end
        
        subgraph "Output Data"
            GRAPH_NODES_OUT[Graph Nodes]
            GRAPH_RELS_OUT[Graph Relationships]
            QUERY_RESULTS_OUT[Query Results]
            OPERATION_STATUS[Operation Status]
        end
    end
    
    %% Input Processing
    NODE_CREATE_REQ --> VALIDATE_NODE
    REL_CREATE_REQ --> VALIDATE_REL
    QUERY_REQ --> VALIDATE_QUERY
    UPDATE_REQ --> VALIDATE_NODE
    
    %% Validation Flow
    VALIDATE_NODE --> SANITIZE_INPUT
    VALIDATE_REL --> SANITIZE_INPUT
    VALIDATE_QUERY --> SANITIZE_INPUT
    
    %% Serialization Flow
    SANITIZE_INPUT --> SERIALIZE_PROPS
    SANITIZE_INPUT --> SERIALIZE_METADATA
    SANITIZE_INPUT --> FORMAT_QUERY
    SANITIZE_INPUT --> PREPARE_PARAMS
    
    %% Database Operations Flow
    SERIALIZE_PROPS --> EXECUTE_CREATE
    SERIALIZE_METADATA --> EXECUTE_CREATE
    FORMAT_QUERY --> EXECUTE_QUERY
    PREPARE_PARAMS --> EXECUTE_UPDATE
    PREPARE_PARAMS --> EXECUTE_DELETE
    
    %% Result Processing Flow
    EXECUTE_CREATE --> RAW_RESULTS
    EXECUTE_QUERY --> RAW_RESULTS
    EXECUTE_UPDATE --> RAW_RESULTS
    EXECUTE_DELETE --> RAW_RESULTS
    
    RAW_RESULTS --> DESERIALIZE_DATA
    DESERIALIZE_DATA --> BUILD_OBJECTS
    BUILD_OBJECTS --> CALCULATE_METRICS
    
    %% Cache Management Flow
    VALIDATE_QUERY --> CHECK_CACHE
    CHECK_CACHE --> CACHE_RESULTS
    BUILD_OBJECTS --> UPDATE_CACHE
    EXECUTE_UPDATE --> INVALIDATE_CACHE
    EXECUTE_DELETE --> INVALIDATE_CACHE
    
    %% Output Generation
    BUILD_OBJECTS --> GRAPH_NODES_OUT
    BUILD_OBJECTS --> GRAPH_RELS_OUT
    CALCULATE_METRICS --> QUERY_RESULTS_OUT
    RAW_RESULTS --> OPERATION_STATUS
    
    %% Cache to Output
    CHECK_CACHE --> GRAPH_NODES_OUT
    CHECK_CACHE --> QUERY_RESULTS_OUT
    
    %% Styling
    classDef inputOps fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef validation fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef serialization fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dbOps fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef resultProc fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef cacheManagement fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef outputData fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    
    class NODE_CREATE_REQ,REL_CREATE_REQ,QUERY_REQ,UPDATE_REQ inputOps
    class VALIDATE_NODE,VALIDATE_REL,VALIDATE_QUERY,SANITIZE_INPUT validation
    class SERIALIZE_PROPS,SERIALIZE_METADATA,FORMAT_QUERY,PREPARE_PARAMS serialization
    class EXECUTE_CREATE,EXECUTE_QUERY,EXECUTE_UPDATE,EXECUTE_DELETE dbOps
    class RAW_RESULTS,DESERIALIZE_DATA,BUILD_OBJECTS,CALCULATE_METRICS resultProc
    class CHECK_CACHE,UPDATE_CACHE,INVALIDATE_CACHE,CACHE_RESULTS cacheManagement
    class GRAPH_NODES_OUT,GRAPH_RELS_OUT,QUERY_RESULTS_OUT,OPERATION_STATUS outputData

## 🔍 **Query Processing Data Flow**

```mermaid
graph TD
    subgraph "Query Processing Data Pipeline"
        subgraph "Query Input"
            USER_QUERY[User Query Text]
            QUERY_PARAMS[Query Parameters]
            QUERY_TYPE[Query Type]
            QUERY_OPTIONS[Query Options]
        end

        subgraph "Query Analysis"
            PARSED_QUERY[Parsed Query]
            SEARCH_TERMS[Search Terms]
            INTENT_DATA[Intent Data]
            FILTER_CRITERIA[Filter Criteria]
        end

        subgraph "LLM Processing"
            NL_PROMPT[Natural Language Prompt]
            LLM_ANALYSIS[LLM Analysis]
            PARSED_INTENT[Parsed Intent]
            STRUCTURED_QUERY[Structured Query]
        end

        subgraph "Database Query"
            GRAPH_QUERY[Graph Query]
            QUERY_EXECUTION[Query Execution]
            RAW_DB_RESULTS[Raw Database Results]
            RESULT_METADATA[Result Metadata]
        end

        subgraph "Result Processing"
            FILTERED_RESULTS[Filtered Results]
            SCORED_RESULTS[Scored Results]
            RANKED_RESULTS[Ranked Results]
            ENHANCED_RESULTS[Enhanced Results]
        end

        subgraph "Response Formatting"
            RESULT_NODES[Result Nodes]
            RESULT_RELATIONSHIPS[Result Relationships]
            CONFIDENCE_DATA[Confidence Data]
            EXECUTION_METRICS[Execution Metrics]
        end

        subgraph "Output Data"
            QUERY_RESPONSE[Query Response]
            VISUALIZATION_DATA[Visualization Data]
            EXPORT_DATA[Export Data]
            CACHE_DATA[Cache Data]
        end
    end

    %% Input Processing
    USER_QUERY --> PARSED_QUERY
    QUERY_PARAMS --> SEARCH_TERMS
    QUERY_TYPE --> INTENT_DATA
    QUERY_OPTIONS --> FILTER_CRITERIA

    %% Query Analysis Flow
    PARSED_QUERY --> SEARCH_TERMS
    SEARCH_TERMS --> INTENT_DATA
    INTENT_DATA --> FILTER_CRITERIA

    %% LLM Processing Flow (for Natural Language queries)
    USER_QUERY --> NL_PROMPT
    NL_PROMPT --> LLM_ANALYSIS
    LLM_ANALYSIS --> PARSED_INTENT
    PARSED_INTENT --> STRUCTURED_QUERY
    STRUCTURED_QUERY --> SEARCH_TERMS

    %% Database Query Flow
    FILTER_CRITERIA --> GRAPH_QUERY
    GRAPH_QUERY --> QUERY_EXECUTION
    QUERY_EXECUTION --> RAW_DB_RESULTS
    RAW_DB_RESULTS --> RESULT_METADATA

    %% Result Processing Flow
    RAW_DB_RESULTS --> FILTERED_RESULTS
    FILTERED_RESULTS --> SCORED_RESULTS
    SCORED_RESULTS --> RANKED_RESULTS
    RANKED_RESULTS --> ENHANCED_RESULTS

    %% Response Formatting Flow
    ENHANCED_RESULTS --> RESULT_NODES
    ENHANCED_RESULTS --> RESULT_RELATIONSHIPS
    SCORED_RESULTS --> CONFIDENCE_DATA
    RESULT_METADATA --> EXECUTION_METRICS

    %% Output Generation
    RESULT_NODES --> QUERY_RESPONSE
    RESULT_RELATIONSHIPS --> QUERY_RESPONSE
    CONFIDENCE_DATA --> QUERY_RESPONSE
    EXECUTION_METRICS --> QUERY_RESPONSE

    QUERY_RESPONSE --> VISUALIZATION_DATA
    QUERY_RESPONSE --> EXPORT_DATA
    QUERY_RESPONSE --> CACHE_DATA

    %% Styling
    classDef queryInput fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef queryAnalysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef llmProcessing fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef dbQuery fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef resultProc fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef responseFormat fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef outputData fill:#fff8e1,stroke:#ff8f00,stroke-width:2px

    class USER_QUERY,QUERY_PARAMS,QUERY_TYPE,QUERY_OPTIONS queryInput
    class PARSED_QUERY,SEARCH_TERMS,INTENT_DATA,FILTER_CRITERIA queryAnalysis
    class NL_PROMPT,LLM_ANALYSIS,PARSED_INTENT,STRUCTURED_QUERY llmProcessing
    class GRAPH_QUERY,QUERY_EXECUTION,RAW_DB_RESULTS,RESULT_METADATA dbQuery
    class FILTERED_RESULTS,SCORED_RESULTS,RANKED_RESULTS,ENHANCED_RESULTS resultProc
    class RESULT_NODES,RESULT_RELATIONSHIPS,CONFIDENCE_DATA,EXECUTION_METRICS responseFormat
    class QUERY_RESPONSE,VISUALIZATION_DATA,EXPORT_DATA,CACHE_DATA outputData
```

## 🧩 **Concept Mapping Data Flow**

```mermaid
graph TD
    subgraph "Concept Mapping Data Pipeline"
        subgraph "Input Concepts"
            SEMANTIC_CONCEPTS[Semantic Concepts]
            CONCEPT_METADATA[Concept Metadata]
            SOURCE_ELEMENTS[Source Elements]
            KEYWORDS[Keywords]
        end

        subgraph "Individual Analysis"
            CONCEPT_ANALYSIS[Individual Concept Analysis]
            HIERARCHY_LEVEL[Hierarchy Level]
            ABSTRACTION_SCORE[Abstraction Score]
            DOMAIN_SCORE[Domain Relevance Score]
            TAXONOMY_PATH[Taxonomy Path]
        end

        subgraph "Relationship Analysis"
            SIMILARITY_MATRIX[Similarity Matrix]
            RELATIONSHIP_SCORES[Relationship Scores]
            PARENT_CHILD_DATA[Parent-Child Data]
            RELATED_CONCEPTS_DATA[Related Concepts Data]
        end

        subgraph "Clustering Data"
            CLUSTER_ASSIGNMENTS[Cluster Assignments]
            CLUSTER_CENTERS[Cluster Centers]
            CLUSTER_SCORES[Cluster Scores]
            CLUSTER_METADATA[Cluster Metadata]
        end

        subgraph "Hierarchy Data"
            HIERARCHY_LEVELS[Hierarchy Levels]
            TREE_STRUCTURE[Tree Structure]
            LEVEL_ASSIGNMENTS[Level Assignments]
            HIERARCHY_METADATA[Hierarchy Metadata]
        end

        subgraph "LLM Enhancement Data"
            LLM_SUGGESTIONS[LLM Suggestions]
            ENHANCED_RELATIONSHIPS[Enhanced Relationships]
            IMPROVED_HIERARCHIES[Improved Hierarchies]
            VALIDATION_DATA[Validation Data]
        end

        subgraph "Output Data"
            MAPPED_CONCEPTS[Mapped Concepts]
            CONCEPT_TAXONOMIES[Concept Taxonomies]
            CONCEPT_HIERARCHIES[Concept Hierarchies]
            CONCEPT_CLUSTERS[Concept Clusters]
        end
    end

    %% Input Processing
    SEMANTIC_CONCEPTS --> CONCEPT_ANALYSIS
    CONCEPT_METADATA --> CONCEPT_ANALYSIS
    SOURCE_ELEMENTS --> CONCEPT_ANALYSIS
    KEYWORDS --> CONCEPT_ANALYSIS

    %% Individual Analysis Flow
    CONCEPT_ANALYSIS --> HIERARCHY_LEVEL
    CONCEPT_ANALYSIS --> ABSTRACTION_SCORE
    CONCEPT_ANALYSIS --> DOMAIN_SCORE
    CONCEPT_ANALYSIS --> TAXONOMY_PATH

    %% Relationship Analysis Flow
    HIERARCHY_LEVEL --> SIMILARITY_MATRIX
    ABSTRACTION_SCORE --> SIMILARITY_MATRIX
    DOMAIN_SCORE --> SIMILARITY_MATRIX
    SIMILARITY_MATRIX --> RELATIONSHIP_SCORES
    RELATIONSHIP_SCORES --> PARENT_CHILD_DATA
    RELATIONSHIP_SCORES --> RELATED_CONCEPTS_DATA

    %% Clustering Flow
    SIMILARITY_MATRIX --> CLUSTER_ASSIGNMENTS
    CLUSTER_ASSIGNMENTS --> CLUSTER_CENTERS
    CLUSTER_CENTERS --> CLUSTER_SCORES
    CLUSTER_SCORES --> CLUSTER_METADATA

    %% Hierarchy Construction Flow
    PARENT_CHILD_DATA --> HIERARCHY_LEVELS
    HIERARCHY_LEVELS --> TREE_STRUCTURE
    TREE_STRUCTURE --> LEVEL_ASSIGNMENTS
    LEVEL_ASSIGNMENTS --> HIERARCHY_METADATA

    %% LLM Enhancement Flow
    CLUSTER_METADATA --> LLM_SUGGESTIONS
    HIERARCHY_METADATA --> LLM_SUGGESTIONS
    LLM_SUGGESTIONS --> ENHANCED_RELATIONSHIPS
    ENHANCED_RELATIONSHIPS --> IMPROVED_HIERARCHIES
    IMPROVED_HIERARCHIES --> VALIDATION_DATA

    %% Output Generation
    VALIDATION_DATA --> MAPPED_CONCEPTS
    TAXONOMY_PATH --> CONCEPT_TAXONOMIES
    IMPROVED_HIERARCHIES --> CONCEPT_HIERARCHIES
    CLUSTER_METADATA --> CONCEPT_CLUSTERS

    %% Cross-connections
    RELATED_CONCEPTS_DATA --> MAPPED_CONCEPTS
    TREE_STRUCTURE --> CONCEPT_HIERARCHIES
    CLUSTER_ASSIGNMENTS --> CONCEPT_CLUSTERS

    %% Styling
    classDef inputConcepts fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef individualAnalysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef relationshipAnalysis fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef clusteringData fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef hierarchyData fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef llmEnhancement fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef outputData fill:#fff8e1,stroke:#ff8f00,stroke-width:2px

    class SEMANTIC_CONCEPTS,CONCEPT_METADATA,SOURCE_ELEMENTS,KEYWORDS inputConcepts
    class CONCEPT_ANALYSIS,HIERARCHY_LEVEL,ABSTRACTION_SCORE,DOMAIN_SCORE,TAXONOMY_PATH individualAnalysis
    class SIMILARITY_MATRIX,RELATIONSHIP_SCORES,PARENT_CHILD_DATA,RELATED_CONCEPTS_DATA relationshipAnalysis
    class CLUSTER_ASSIGNMENTS,CLUSTER_CENTERS,CLUSTER_SCORES,CLUSTER_METADATA clusteringData
    class HIERARCHY_LEVELS,TREE_STRUCTURE,LEVEL_ASSIGNMENTS,HIERARCHY_METADATA hierarchyData
    class LLM_SUGGESTIONS,ENHANCED_RELATIONSHIPS,IMPROVED_HIERARCHIES,VALIDATION_DATA llmEnhancement
    class MAPPED_CONCEPTS,CONCEPT_TAXONOMIES,CONCEPT_HIERARCHIES,CONCEPT_CLUSTERS outputData
```
```
