# 🖥️ Cognitive Code Weaver - CLI Interface Guide

## 📋 **Overview**

The Cognitive Code Weaver CLI provides a powerful command-line interface for AI-powered code analysis and reasoning. It offers both simple and advanced modes to suit different use cases and environments.

## 🚀 **Quick Start**

### **Installation**

```bash
# Install the package
pip install -e .

# Or use the simple CLI directly
python ccw/cli/simple_cli.py --help
```

### **Basic Usage**

```bash
# Show version
ccw version

# Analyze a single file
ccw analyze file ./src/main.py

# Analyze a workspace
ccw analyze workspace ./src

# Start interactive mode
ccw interactive
```

## 📚 **Available CLI Interfaces**

### **1. Full-Featured CLI (`ccw.cli.main`)**
- **Rich UI**: Beautiful terminal output with colors and progress bars
- **Advanced Features**: LLM integration, knowledge graph, debugging
- **Agent System**: Full multi-agent orchestration
- **Dependencies**: Requires typer, rich, and full system dependencies

### **2. Simple CLI (`ccw.cli.simple_cli`)**
- **Minimal Dependencies**: Works with just Python standard library
- **Basic Analysis**: File and workspace analysis without AI features
- **Lightweight**: Perfect for CI/CD and resource-constrained environments
- **Standalone**: Can run without full system installation

## 🔧 **Command Reference**

### **Analysis Commands**

#### **Analyze File**
```bash
# Basic file analysis
ccw analyze file <file_path>

# With output file
ccw analyze file <file_path> --output results.json --format json

# Include AST analysis (full CLI only)
ccw analyze file <file_path> --ast --metrics
```

#### **Analyze Workspace**
```bash
# Basic workspace analysis
ccw analyze workspace <workspace_path>

# Limit number of files
ccw analyze workspace <workspace_path> --max-files 100

# Include dependencies and metrics
ccw analyze workspace <workspace_path> --deps --metrics

# Parallel processing (full CLI only)
ccw analyze workspace <workspace_path> --parallel
```

#### **Compare Workspaces** (Full CLI only)
```bash
# Compare two workspaces
ccw analyze compare <workspace1> <workspace2>

# Focus on specific metrics
ccw analyze compare <workspace1> <workspace2> --focus complexity
```

### **AI-Powered Commands** (Full CLI only)

#### **Ask Questions**
```bash
# Ask about codebase
ccw ask "How does authentication work?" --workspace ./src

# Ask with specific context
ccw ask "Find all security vulnerabilities" --workspace ./src
```

#### **LLM Integration**
```bash
# Test LLM connection
ccw llm test --text "Hello, world!"

# List available providers
ccw llm providers

# Validate provider connections
ccw llm validate

# Test streaming
ccw llm stream --text "Write a Python function"
```

#### **Knowledge Graph**
```bash
# Query knowledge graph
ccw knowledge query "find all classes" --workspace ./src

# Build knowledge graph
ccw knowledge build --workspace ./src

# Export knowledge graph
ccw knowledge export --workspace ./src --format json

# Show statistics
ccw knowledge stats --workspace ./src
```

#### **Debug Analysis**
```bash
# Debug code issues
ccw debug ./src

# Focus on specific issue types
ccw debug ./src --type security --severity high

# Save debug report
ccw debug ./src --output debug_report.json
```

#### **Metrics Calculation**
```bash
# Calculate all metrics
ccw metrics ./src

# Focus on specific metric types
ccw metrics ./src --type complexity --threshold 10

# Export metrics
ccw metrics ./src --format json --output metrics.json
```

### **System Commands**

#### **System Status**
```bash
# Show system status
ccw status

# Show with configuration
ccw status --config ./config/ccw.yaml
```

#### **Agent Management**
```bash
# List all agents
ccw agents list

# Enable specific agent
ccw agents enable --agent cognitive_agent

# Disable specific agent
ccw agents disable --agent bug_detector
```

#### **Configuration**
```bash
# Initialize configuration
ccw init

# Initialize with custom path
ccw init --config ./my_config.yaml

# Force overwrite existing config
ccw init --force
```

### **Interactive Mode**

```bash
# Start interactive mode
ccw interactive

# Interactive commands
ccw> help
ccw> analyze ./src
ccw> ask "How does this work?"
ccw> llm "Explain this code"
ccw> knowledge query "find functions"
ccw> debug ./src
ccw> metrics ./src
ccw> status
ccw> agents
ccw> exit
```

## ⚙️ **Configuration**

### **Configuration File**

Create `config/ccw.yaml`:

```yaml
# LLM Configuration
llm:
  providers:
    openai:
      api_key: "your-openai-key"
      model: "gpt-4"
    anthropic:
      api_key: "your-anthropic-key"
      model: "claude-3-sonnet"
  
  default_provider: "openai"
  max_tokens: 1000
  temperature: 0.7

# Analysis Configuration
analysis:
  max_files: 1000
  include_metrics: true
  include_dependencies: true
  parallel_processing: true

# Agent Configuration
agents:
  enabled:
    - master_agent
    - cognitive_agent
    - planner_agent
    - code_reader_agent
    - reasoner_agent
    - knowledge_graph_agent

# Logging Configuration
logging:
  level: "INFO"
  file: "logs/ccw.log"

# Cache Configuration
cache:
  enabled: true
  directory: ".ccw_cache"
  ttl: 3600
```

### **Environment Variables**

```bash
# Configuration
export CCW_CONFIG_FILE="./config/ccw.yaml"
export CCW_VERBOSE=true
export CCW_NO_COLOR=false

# Output Settings
export CCW_OUTPUT_FORMAT="json"
export CCW_MAX_FILES=1000

# LLM Settings
export CCW_DEFAULT_PROVIDER="openai"
export CCW_MAX_TOKENS=1000
export CCW_TEMPERATURE=0.7

# Performance Settings
export CCW_PARALLEL=true
export CCW_CACHE=true

# Debug Settings
export CCW_DEBUG=false
export CCW_LOG_LEVEL="INFO"
```

## 📊 **Output Formats**

### **Text Format** (Default)
Human-readable output with emojis and formatting:

```
📄 File Analysis Results

File: ./src/main.py
Language: Python
Total Lines: 150
Complexity: 8.5
Maintainability: 75.2
```

### **JSON Format**
Machine-readable structured data:

```json
{
  "file_path": "./src/main.py",
  "language": "Python",
  "metrics": {
    "total_lines": 150,
    "complexity": 8.5,
    "maintainability": 75.2
  }
}
```

### **YAML Format**
Human and machine-readable:

```yaml
file_path: ./src/main.py
language: Python
metrics:
  total_lines: 150
  complexity: 8.5
  maintainability: 75.2
```

## 🎯 **Use Cases**

### **Development Workflow**
```bash
# Daily code review
ccw analyze workspace ./src --format json --output daily_report.json

# Pre-commit analysis
ccw debug ./src --severity high

# Code quality check
ccw metrics ./src --type quality --threshold 70
```

### **CI/CD Integration**
```bash
# Simple analysis for CI
python ccw/cli/simple_cli.py analyze workspace ./src --format json

# Quality gate check
ccw metrics ./src --type complexity --threshold 15 || exit 1

# Security analysis
ccw debug ./src --type security --severity medium
```

### **Code Exploration**
```bash
# Interactive exploration
ccw interactive

# Ask questions about codebase
ccw ask "What are the main components?" --workspace ./src

# Query knowledge graph
ccw knowledge query "find all API endpoints" --workspace ./src
```

### **Documentation Generation**
```bash
# Export knowledge graph
ccw knowledge export --workspace ./src --format yaml --output docs/architecture.yaml

# Generate metrics report
ccw metrics ./src --format json --output docs/metrics.json

# Create analysis report
ccw analyze workspace ./src --format yaml --output docs/analysis.yaml
```

## 🔍 **Troubleshooting**

### **Common Issues**

1. **Import Errors**
   ```bash
   # Use simple CLI if dependencies are missing
   python ccw/cli/simple_cli.py version
   ```

2. **Configuration Issues**
   ```bash
   # Initialize fresh configuration
   ccw init --force
   ```

3. **LLM Connection Issues**
   ```bash
   # Validate LLM providers
   ccw llm validate
   ```

4. **Performance Issues**
   ```bash
   # Limit file analysis
   ccw analyze workspace ./src --max-files 100
   
   # Disable parallel processing
   ccw analyze workspace ./src --sequential
   ```

### **Debug Mode**
```bash
# Enable debug output
export CCW_DEBUG=true
export CCW_LOG_LEVEL=DEBUG

# Run with verbose output
ccw analyze workspace ./src --verbose
```

## 📈 **Performance Tips**

1. **Use file limits for large codebases**
2. **Enable caching for repeated analysis**
3. **Use parallel processing when available**
4. **Choose appropriate output formats**
5. **Use simple CLI for basic analysis**

## 🎉 **Next Steps**

1. **Try the interactive mode**: `ccw interactive`
2. **Explore AI features**: `ccw ask "How does this work?"`
3. **Set up configuration**: `ccw init`
4. **Integrate with CI/CD**: Use simple CLI for automation
5. **Build knowledge graphs**: `ccw knowledge build`

The CLI interface provides a powerful and flexible way to interact with the Cognitive Code Weaver system, from simple file analysis to advanced AI-powered code understanding!
