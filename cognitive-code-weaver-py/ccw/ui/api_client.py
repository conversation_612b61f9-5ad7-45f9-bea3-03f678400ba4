"""
API Client for UI Integration

Provides a Python client for interacting with the Cognitive Code Weaver API
from UI components, with support for async operations and WebSocket connections.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from datetime import datetime
import aiohttp
import websockets
from urllib.parse import urljoin

from ccw.api.models import *

logger = logging.getLogger(__name__)


class APIClientError(Exception):
    """API client error"""
    pass


class WebSocketClient:
    """WebSocket client for real-time updates"""
    
    def __init__(self, base_url: str, connection_id: str):
        self.base_url = base_url
        self.connection_id = connection_id
        self.websocket = None
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
    
    async def connect(self):
        """Connect to WebSocket"""
        try:
            ws_url = self.base_url.replace("http", "ws") + f"/ws/{self.connection_id}"
            self.websocket = await websockets.connect(ws_url)
            self.running = True
            self.reconnect_attempts = 0
            
            logger.info(f"WebSocket connected: {self.connection_id}")
            
            # Start message handling loop
            asyncio.create_task(self._message_loop())
            
        except Exception as e:
            logger.error(f"WebSocket connection failed: {e}")
            raise APIClientError(f"Failed to connect WebSocket: {e}")
    
    async def disconnect(self):
        """Disconnect from WebSocket"""
        self.running = False
        
        if self.websocket:
            await self.websocket.close()
            self.websocket = None
        
        logger.info(f"WebSocket disconnected: {self.connection_id}")
    
    async def send_message(self, message: Dict[str, Any]):
        """Send message to WebSocket"""
        if not self.websocket:
            raise APIClientError("WebSocket not connected")
        
        try:
            await self.websocket.send(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            raise APIClientError(f"Failed to send message: {e}")
    
    async def subscribe_to_topic(self, topic: str):
        """Subscribe to a topic"""
        await self.send_message({
            "type": "subscribe",
            "data": {"topic": topic}
        })
    
    async def unsubscribe_from_topic(self, topic: str):
        """Unsubscribe from a topic"""
        await self.send_message({
            "type": "unsubscribe",
            "data": {"topic": topic}
        })
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
    
    def unregister_event_handler(self, event_type: str, handler: Callable):
        """Unregister event handler"""
        if event_type in self.event_handlers:
            self.event_handlers[event_type] = [
                h for h in self.event_handlers[event_type] if h != handler
            ]
    
    async def _message_loop(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON in WebSocket message: {e}")
                except Exception as e:
                    logger.error(f"Error handling WebSocket message: {e}")
        
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            if self.running:
                await self._attempt_reconnect()
        
        except Exception as e:
            logger.error(f"WebSocket message loop error: {e}")
            if self.running:
                await self._attempt_reconnect()
    
    async def _handle_message(self, data: Dict[str, Any]):
        """Handle incoming message"""
        message_type = data.get("type", "unknown")
        
        # Call registered handlers
        handlers = self.event_handlers.get(message_type, [])
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(data)
                else:
                    handler(data)
            except Exception as e:
                logger.error(f"Error in WebSocket event handler: {e}")
    
    async def _attempt_reconnect(self):
        """Attempt to reconnect WebSocket"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Max reconnection attempts reached")
            self.running = False
            return
        
        self.reconnect_attempts += 1
        logger.info(f"Attempting WebSocket reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")
        
        await asyncio.sleep(self.reconnect_delay)
        
        try:
            await self.connect()
        except Exception as e:
            logger.error(f"Reconnection attempt failed: {e}")
            await self._attempt_reconnect()


class APIClient:
    """HTTP API client for Cognitive Code Weaver"""
    
    def __init__(self, base_url: str = "http://localhost:8000", timeout: int = 30):
        self.base_url = base_url.rstrip("/")
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.session: Optional[aiohttp.ClientSession] = None
        self.websocket_client: Optional[WebSocketClient] = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self):
        """Start the API client"""
        if not self.session:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
    
    async def close(self):
        """Close the API client"""
        if self.websocket_client:
            await self.websocket_client.disconnect()
        
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request"""
        if not self.session:
            await self.start()
        
        url = urljoin(self.base_url + "/", endpoint.lstrip("/"))
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if response.content_type == "application/json":
                    data = await response.json()
                else:
                    data = {"content": await response.text()}
                
                if response.status >= 400:
                    error_msg = data.get("message", f"HTTP {response.status}")
                    raise APIClientError(f"API request failed: {error_msg}")
                
                return data
        
        except aiohttp.ClientError as e:
            raise APIClientError(f"Network error: {e}")
        except Exception as e:
            raise APIClientError(f"Request failed: {e}")
    
    # Health and Status
    async def health_check(self) -> HealthCheckResponse:
        """Check API health"""
        data = await self._request("GET", "/api/v1/health")
        return HealthCheckResponse(**data)
    
    async def get_system_status(self) -> SystemStatusResponse:
        """Get system status"""
        data = await self._request("GET", "/api/v1/status")
        return SystemStatusResponse(**data)
    
    # Analysis Operations
    async def start_analysis(self, request: AnalysisRequest) -> AnalysisResponse:
        """Start code analysis"""
        data = await self._request("POST", "/api/v1/analysis", json=request.dict())
        return AnalysisResponse(**data)
    
    async def get_analysis_result(self, analysis_id: str) -> AnalysisResponse:
        """Get analysis result"""
        data = await self._request("GET", f"/api/v1/analysis/{analysis_id}")
        return AnalysisResponse(**data)
    
    # Agent Management
    async def list_agents(self) -> AgentListResponse:
        """List all agents"""
        data = await self._request("GET", "/api/v1/agents")
        return AgentListResponse(**data)
    
    async def get_agent_info(self, agent_id: str) -> AgentInfo:
        """Get agent information"""
        data = await self._request("GET", f"/api/v1/agents/{agent_id}")
        return AgentInfo(**data)
    
    # Task Management
    async def create_task(self, request: TaskRequest) -> TaskResponse:
        """Create and execute task"""
        data = await self._request("POST", "/api/v1/tasks", json=request.dict())
        return TaskResponse(**data)
    
    async def list_tasks(self, page: int = 1, page_size: int = 20) -> TaskListResponse:
        """List tasks"""
        params = {"page": page, "page_size": page_size}
        data = await self._request("GET", "/api/v1/tasks", params=params)
        return TaskListResponse(**data)
    
    async def get_task(self, task_id: str) -> TaskResponse:
        """Get task information"""
        data = await self._request("GET", f"/api/v1/tasks/{task_id}")
        return TaskResponse(**data)
    
    # Query Operations
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        """Process natural language query"""
        data = await self._request("POST", "/api/v1/query", json=request.dict())
        return QueryResponse(**data)
    
    # Configuration Management
    async def get_configuration(self) -> ConfigurationResponse:
        """Get system configuration"""
        data = await self._request("GET", "/api/v1/config")
        return ConfigurationResponse(**data)
    
    async def update_configuration(self, update: ConfigurationUpdate) -> ConfigurationResponse:
        """Update configuration"""
        data = await self._request("PUT", "/api/v1/config", json=update.dict())
        return ConfigurationResponse(**data)
    
    # File Operations
    async def upload_file(self, file_path: str, file_content: bytes) -> FileUploadResponse:
        """Upload file for analysis"""
        data = aiohttp.FormData()
        data.add_field('file', file_content, filename=file_path)
        
        response_data = await self._request("POST", "/api/v1/upload", data=data)
        return FileUploadResponse(**response_data)
    
    # Export Operations
    async def export_analysis(self, request: ExportRequest) -> ExportResponse:
        """Export analysis results"""
        data = await self._request("POST", "/api/v1/export", json=request.dict())
        return ExportResponse(**data)
    
    # WebSocket Operations
    async def connect_websocket(self, connection_id: str) -> WebSocketClient:
        """Connect to WebSocket for real-time updates"""
        if self.websocket_client:
            await self.websocket_client.disconnect()
        
        self.websocket_client = WebSocketClient(self.base_url, connection_id)
        await self.websocket_client.connect()
        
        return self.websocket_client
    
    async def disconnect_websocket(self):
        """Disconnect WebSocket"""
        if self.websocket_client:
            await self.websocket_client.disconnect()
            self.websocket_client = None
    
    # Streaming Operations
    async def stream_analysis_progress(self, analysis_id: str) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream analysis progress updates"""
        if not self.websocket_client:
            raise APIClientError("WebSocket not connected")
        
        # Subscribe to analysis progress
        await self.websocket_client.subscribe_to_topic("analysis_progress")
        
        # Set up event handler for progress updates
        progress_queue = asyncio.Queue()
        
        def progress_handler(data: Dict[str, Any]):
            if data.get("type") == "analysis_progress_update":
                progress_data = data.get("data", {})
                if progress_data.get("analysis_id") == analysis_id:
                    progress_queue.put_nowait(progress_data)
        
        self.websocket_client.register_event_handler("analysis_progress_update", progress_handler)
        
        try:
            while True:
                progress_data = await progress_queue.get()
                yield progress_data
                
                # Stop if analysis is complete
                if progress_data.get("progress", 0) >= 1.0:
                    break
        
        finally:
            self.websocket_client.unregister_event_handler("analysis_progress_update", progress_handler)
    
    async def stream_agent_status(self) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream agent status updates"""
        if not self.websocket_client:
            raise APIClientError("WebSocket not connected")
        
        # Subscribe to agent status
        await self.websocket_client.subscribe_to_topic("agent_status")
        
        # Set up event handler
        status_queue = asyncio.Queue()
        
        def status_handler(data: Dict[str, Any]):
            if data.get("type") == "agent_status_update":
                status_queue.put_nowait(data.get("data", {}))
        
        self.websocket_client.register_event_handler("agent_status_update", status_handler)
        
        try:
            while True:
                status_data = await status_queue.get()
                yield status_data
        
        finally:
            self.websocket_client.unregister_event_handler("agent_status_update", status_handler)
    
    # Utility Methods
    def get_websocket_client(self) -> Optional[WebSocketClient]:
        """Get WebSocket client"""
        return self.websocket_client
    
    async def ping(self) -> bool:
        """Ping the API server"""
        try:
            await self.health_check()
            return True
        except Exception:
            return False
