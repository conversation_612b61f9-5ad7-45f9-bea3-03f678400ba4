"""
Data Transformers for UI Integration

Transforms core system data structures into UI-friendly formats
for visualization, charts, and interactive components.
"""

import json
import math
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import asdict

from ccw.core.agent import AgentStatus
from ccw.analysis.parser import LanguageType


class DataTransformer:
    """Base data transformer for UI components"""

    def transform_agents_for_ui(self, agents_info: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Transform agent information for UI display"""
        ui_agents = []

        for agent_info in agents_info:
            ui_agent = {
                "id": agent_info["agent_id"],
                "name": agent_info["agent_id"].replace("_", " ").title(),
                "status": "running" if agent_info.get("is_running", False) else "stopped",
                "capabilities": agent_info.get("capabilities", []),
                "health": "healthy" if agent_info.get("is_running", False) else "inactive",
                "last_activity": agent_info.get("last_activity"),
                "metrics": {
                    "tasks_completed": agent_info.get("tasks_completed", 0),
                    "tasks_failed": agent_info.get("tasks_failed", 0),
                    "average_execution_time": agent_info.get("average_execution_time", 0.0)
                }
            }
            ui_agents.append(ui_agent)

        return ui_agents

    def transform_agent_status_for_ui(self, agent_id: str, status_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform agent status update for UI"""
        return {
            "agent_id": agent_id,
            "agent_name": agent_id.replace("_", " ").title(),
            "status": status_data.get("status", "unknown"),
            "current_task": status_data.get("current_task"),
            "progress": status_data.get("progress", 0.0),
            "message": status_data.get("message"),
            "timestamp": datetime.now().isoformat(),
            "health_indicator": self._get_health_indicator(status_data)
        }

    def transform_task_progress_for_ui(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform task progress for UI"""
        return {
            "task_id": task_data.get("task_id"),
            "task_name": task_data.get("task_type", "Unknown Task").replace("_", " ").title(),
            "status": task_data.get("status", "unknown"),
            "progress": task_data.get("progress", 0.0),
            "progress_percentage": int(task_data.get("progress", 0.0) * 100),
            "agent_id": task_data.get("agent_id"),
            "message": task_data.get("message"),
            "estimated_remaining": task_data.get("estimated_remaining"),
            "timestamp": datetime.now().isoformat()
        }

    def transform_analysis_result_for_ui(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform analysis result for UI display"""
        return {
            "analysis_id": analysis_data.get("analysis_id"),
            "workspace_path": analysis_data.get("workspace_path"),
            "summary": {
                "total_files": analysis_data.get("total_files", 0),
                "analyzed_files": analysis_data.get("analyzed_files", 0),
                "skipped_files": analysis_data.get("skipped_files", 0),
                "total_lines_of_code": analysis_data.get("total_lines_of_code", 0),
                "total_functions": analysis_data.get("total_functions", 0),
                "total_classes": analysis_data.get("total_classes", 0),
                "average_complexity": analysis_data.get("average_complexity", 0.0)
            },
            "quality_metrics": self._extract_quality_metrics(analysis_data),
            "language_breakdown": self._extract_language_breakdown(analysis_data),
            "completion_time": analysis_data.get("analysis_duration", 0.0),
            "timestamp": datetime.now().isoformat()
        }

    def transform_system_event_for_ui(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform system event for UI notification"""
        return {
            "event_id": event_data.get("event_id", "unknown"),
            "event_type": event_data.get("event_type", "system"),
            "title": self._get_event_title(event_data),
            "message": event_data.get("message", "System event occurred"),
            "severity": event_data.get("severity", "info"),
            "source": event_data.get("source", "system"),
            "timestamp": datetime.now().isoformat(),
            "details": event_data.get("details", {})
        }

    def _get_health_indicator(self, status_data: Dict[str, Any]) -> str:
        """Get health indicator for agent"""
        status = status_data.get("status", "unknown")

        if status == "running":
            return "healthy"
        elif status == "error" or status == "failed":
            return "error"
        elif status == "idle":
            return "idle"
        else:
            return "unknown"

    def _extract_quality_metrics(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract quality metrics from analysis data"""
        quality_report = analysis_data.get("quality_report", {})

        return {
            "overall_grade": quality_report.get("overall_grade", "N/A"),
            "maintainability_index": quality_report.get("maintainability_index", 0.0),
            "technical_debt_ratio": quality_report.get("technical_debt_ratio", 0.0),
            "test_coverage": quality_report.get("test_coverage", 0.0),
            "code_duplication": quality_report.get("code_duplication", 0.0)
        }

    def _extract_language_breakdown(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract language breakdown from analysis data"""
        files = analysis_data.get("files", [])
        language_counts = {}

        for file_info in files:
            language = file_info.get("language", "unknown")
            if isinstance(language, LanguageType):
                language = language.value

            if language not in language_counts:
                language_counts[language] = {"files": 0, "lines": 0}

            language_counts[language]["files"] += 1
            language_counts[language]["lines"] += file_info.get("lines_of_code", 0)

        return language_counts

    def _get_event_title(self, event_data: Dict[str, Any]) -> str:
        """Get user-friendly title for event"""
        event_type = event_data.get("event_type", "system")

        title_map = {
            "agent_started": "Agent Started",
            "agent_stopped": "Agent Stopped",
            "analysis_complete": "Analysis Complete",
            "task_failed": "Task Failed",
            "system_error": "System Error",
            "configuration_updated": "Configuration Updated"
        }

        return title_map.get(event_type, event_type.replace("_", " ").title())


class VisualizationDataTransformer:
    """Specialized transformer for visualization data"""

    def transform_analysis_result(self, analysis_data: Any) -> Dict[str, Any]:
        """Transform analysis result for visualization"""
        if hasattr(analysis_data, '__dict__'):
            data = analysis_data.__dict__
        elif isinstance(analysis_data, dict):
            data = analysis_data
        else:
            data = {"error": "Invalid analysis data format"}

        return {
            "charts": {
                "complexity_distribution": self._create_complexity_chart(data),
                "language_breakdown": self._create_language_pie_chart(data),
                "file_size_distribution": self._create_file_size_chart(data),
                "quality_metrics": self._create_quality_radar_chart(data)
            },
            "graphs": {
                "dependency_graph": self._create_dependency_graph(data),
                "call_graph": self._create_call_graph(data)
            },
            "heatmaps": {
                "complexity_heatmap": self._create_complexity_heatmap(data),
                "activity_heatmap": self._create_activity_heatmap(data)
            }
        }

    def transform_agent_network(self, agents_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Transform agent information for network visualization"""
        nodes = []
        edges = []

        for agent_info in agents_info:
            node = {
                "id": agent_info["agent_id"],
                "label": agent_info["agent_id"].replace("_", " ").title(),
                "type": "agent",
                "status": "active" if agent_info.get("is_running", False) else "inactive",
                "capabilities": agent_info.get("capabilities", []),
                "size": len(agent_info.get("capabilities", [])) * 10 + 20,
                "color": "#4CAF50" if agent_info.get("is_running", False) else "#9E9E9E"
            }
            nodes.append(node)

        # Create edges based on capability relationships
        for i, agent1 in enumerate(agents_info):
            for j, agent2 in enumerate(agents_info):
                if i != j:
                    shared_caps = set(agent1.get("capabilities", [])) & set(agent2.get("capabilities", []))
                    if shared_caps:
                        edge = {
                            "source": agent1["agent_id"],
                            "target": agent2["agent_id"],
                            "weight": len(shared_caps),
                            "label": f"Shared: {', '.join(list(shared_caps)[:2])}"
                        }
                        edges.append(edge)

        return {
            "nodes": nodes,
            "edges": edges,
            "layout": "force",
            "options": {
                "physics": True,
                "interaction": {"hover": True, "selectConnectedEdges": True}
            }
        }

    def create_metrics_visualization(self, workspace_path: str) -> Dict[str, Any]:
        """Create metrics visualization data"""
        # Mock data for demonstration
        return {
            "charts": {
                "metrics_trend": {
                    "type": "line",
                    "data": {
                        "labels": ["Week 1", "Week 2", "Week 3", "Week 4"],
                        "datasets": [
                            {
                                "label": "Complexity",
                                "data": [7.2, 6.8, 7.5, 7.1],
                                "borderColor": "#FF6384"
                            },
                            {
                                "label": "Maintainability",
                                "data": [75, 78, 72, 76],
                                "borderColor": "#36A2EB"
                            }
                        ]
                    }
                },
                "quality_distribution": {
                    "type": "doughnut",
                    "data": {
                        "labels": ["Excellent", "Good", "Fair", "Poor"],
                        "datasets": [{
                            "data": [25, 45, 20, 10],
                            "backgroundColor": ["#4CAF50", "#FFC107", "#FF9800", "#F44336"]
                        }]
                    }
                }
            }
        }

    def _create_complexity_chart(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create complexity distribution chart"""
        files = data.get("files", [])

        # Group files by complexity ranges
        complexity_ranges = {"Low (1-5)": 0, "Medium (6-10)": 0, "High (11-20)": 0, "Very High (20+)": 0}

        for file_info in files:
            complexity = file_info.get("complexity_score", 0)
            if complexity <= 5:
                complexity_ranges["Low (1-5)"] += 1
            elif complexity <= 10:
                complexity_ranges["Medium (6-10)"] += 1
            elif complexity <= 20:
                complexity_ranges["High (11-20)"] += 1
            else:
                complexity_ranges["Very High (20+)"] += 1

        return {
            "type": "bar",
            "data": {
                "labels": list(complexity_ranges.keys()),
                "datasets": [{
                    "label": "Number of Files",
                    "data": list(complexity_ranges.values()),
                    "backgroundColor": ["#4CAF50", "#FFC107", "#FF9800", "#F44336"]
                }]
            },
            "options": {
                "responsive": True,
                "plugins": {"title": {"display": True, "text": "Complexity Distribution"}}
            }
        }

    def _create_language_pie_chart(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create language breakdown pie chart"""
        files = data.get("files", [])
        language_counts = {}

        for file_info in files:
            language = file_info.get("language", "unknown")
            if isinstance(language, LanguageType):
                language = language.value

            language_counts[language] = language_counts.get(language, 0) + 1

        return {
            "type": "pie",
            "data": {
                "labels": list(language_counts.keys()),
                "datasets": [{
                    "data": list(language_counts.values()),
                    "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"]
                }]
            },
            "options": {
                "responsive": True,
                "plugins": {"title": {"display": True, "text": "Language Distribution"}}
            }
        }

    def _create_file_size_chart(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create file size distribution chart"""
        files = data.get("files", [])

        size_ranges = {"Small (<100)": 0, "Medium (100-500)": 0, "Large (500-1000)": 0, "Very Large (1000+)": 0}

        for file_info in files:
            lines = file_info.get("lines_of_code", 0)
            if lines < 100:
                size_ranges["Small (<100)"] += 1
            elif lines < 500:
                size_ranges["Medium (100-500)"] += 1
            elif lines < 1000:
                size_ranges["Large (500-1000)"] += 1
            else:
                size_ranges["Very Large (1000+)"] += 1

        return {
            "type": "bar",
            "data": {
                "labels": list(size_ranges.keys()),
                "datasets": [{
                    "label": "Number of Files",
                    "data": list(size_ranges.values()),
                    "backgroundColor": "#36A2EB"
                }]
            },
            "options": {
                "responsive": True,
                "plugins": {"title": {"display": True, "text": "File Size Distribution"}}
            }
        }

    def _create_quality_radar_chart(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create quality metrics radar chart"""
        quality_report = data.get("quality_report", {})

        return {
            "type": "radar",
            "data": {
                "labels": ["Maintainability", "Complexity", "Coverage", "Duplication", "Documentation"],
                "datasets": [{
                    "label": "Quality Metrics",
                    "data": [
                        quality_report.get("maintainability_index", 50),
                        100 - quality_report.get("average_complexity", 5) * 10,  # Invert complexity
                        quality_report.get("test_coverage", 0) * 100,
                        100 - quality_report.get("code_duplication", 0) * 100,  # Invert duplication
                        quality_report.get("documentation_coverage", 50)
                    ],
                    "backgroundColor": "rgba(54, 162, 235, 0.2)",
                    "borderColor": "rgba(54, 162, 235, 1)"
                }]
            },
            "options": {
                "responsive": True,
                "scales": {"r": {"beginAtZero": True, "max": 100}},
                "plugins": {"title": {"display": True, "text": "Quality Metrics"}}
            }
        }

    def _create_dependency_graph(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create dependency graph visualization"""
        dependencies = data.get("dependencies", [])

        nodes = set()
        edges = []

        for dep in dependencies:
            source = dep.get("source", "unknown")
            target = dep.get("target", "unknown")

            nodes.add(source)
            nodes.add(target)

            edges.append({
                "source": source,
                "target": target,
                "weight": dep.get("strength", 1.0)
            })

        return {
            "nodes": [{"id": node, "label": node} for node in nodes],
            "edges": edges,
            "layout": "hierarchical",
            "options": {"physics": False}
        }

    def _create_call_graph(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create call graph visualization"""
        # Mock call graph data
        return {
            "nodes": [
                {"id": "main", "label": "main()", "level": 0},
                {"id": "process", "label": "process_data()", "level": 1},
                {"id": "validate", "label": "validate_input()", "level": 1},
                {"id": "save", "label": "save_result()", "level": 2}
            ],
            "edges": [
                {"source": "main", "target": "process"},
                {"source": "main", "target": "validate"},
                {"source": "process", "target": "save"}
            ],
            "layout": "hierarchical",
            "options": {"physics": False}
        }

    def _create_complexity_heatmap(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create complexity heatmap data"""
        files = data.get("files", [])

        heatmap_data = []
        for i, file_info in enumerate(files[:20]):  # Limit to first 20 files
            heatmap_data.append({
                "x": i % 5,
                "y": i // 5,
                "value": file_info.get("complexity_score", 0),
                "label": file_info.get("path", "unknown")
            })

        return {
            "type": "heatmap",
            "data": heatmap_data,
            "options": {
                "colorScale": {"min": 0, "max": 20, "colors": ["#4CAF50", "#FFC107", "#F44336"]}
            }
        }

    def _create_activity_heatmap(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create activity heatmap data"""
        # Mock activity data
        activity_data = []
        for day in range(7):
            for hour in range(24):
                activity_data.append({
                    "x": hour,
                    "y": day,
                    "value": math.sin(hour * 0.3) * math.cos(day * 0.5) * 10 + 10,
                    "label": f"Day {day}, Hour {hour}"
                })

        return {
            "type": "heatmap",
            "data": activity_data,
            "options": {
                "colorScale": {"min": 0, "max": 20, "colors": ["#E3F2FD", "#1976D2"]}
            }
        }