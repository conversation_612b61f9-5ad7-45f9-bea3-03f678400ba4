"""
UI State Manager

Manages UI state, preferences, and session data for web interface components.
Provides persistent state management and synchronization across UI components.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict, field

logger = logging.getLogger(__name__)


@dataclass
class UIPreferences:
    """User interface preferences"""
    theme: str = "light"
    language: str = "en"
    auto_refresh: bool = True
    refresh_interval: int = 5000  # milliseconds
    show_notifications: bool = True
    notification_duration: int = 5000  # milliseconds
    default_view: str = "dashboard"
    sidebar_collapsed: bool = False
    
    # Visualization preferences
    chart_animation: bool = True
    color_scheme: str = "default"
    default_chart_type: str = "bar"
    show_grid_lines: bool = True
    
    # Analysis preferences
    auto_analyze_on_upload: bool = True
    include_dependencies: bool = True
    include_metrics: bool = True
    max_files_to_analyze: int = 1000
    
    # Advanced preferences
    debug_mode: bool = False
    performance_monitoring: bool = False
    experimental_features: bool = False


@dataclass
class ViewState:
    """State for a specific UI view"""
    view_id: str
    view_type: str
    data: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)
    sort_config: Dict[str, Any] = field(default_factory=dict)
    pagination: Dict[str, Any] = field(default_factory=dict)
    selected_items: List[str] = field(default_factory=list)
    expanded_items: Set[str] = field(default_factory=set)
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class SessionState:
    """Complete session state"""
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    preferences: UIPreferences = field(default_factory=UIPreferences)
    view_states: Dict[str, ViewState] = field(default_factory=dict)
    workspace_path: Optional[str] = None
    active_analysis_id: Optional[str] = None
    recent_files: List[str] = field(default_factory=list)
    bookmarks: List[Dict[str, Any]] = field(default_factory=list)
    custom_data: Dict[str, Any] = field(default_factory=dict)


class UIStateManager:
    """Manages UI state and preferences"""
    
    def __init__(self, state_file: Optional[Path] = None):
        self.state_file = state_file or Path.cwd() / ".ui_state.json"
        self.sessions: Dict[str, SessionState] = {}
        self.state_change_handlers: List[callable] = []
        self.auto_save = True
        self.save_interval = 30  # seconds
        self.last_save = datetime.now()
        
        # Load existing state
        self._load_state()
    
    def create_session(self, session_id: str, user_id: Optional[str] = None) -> SessionState:
        """Create a new UI session"""
        session_state = SessionState(
            session_id=session_id,
            user_id=user_id
        )
        
        self.sessions[session_id] = session_state
        self._notify_state_change("session_created", session_id, session_state)
        
        if self.auto_save:
            self._save_state()
        
        return session_state
    
    def get_session(self, session_id: str) -> Optional[SessionState]:
        """Get session state"""
        session = self.sessions.get(session_id)
        if session:
            session.last_activity = datetime.now()
        return session
    
    def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session state"""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        # Update session fields
        for key, value in updates.items():
            if hasattr(session, key):
                setattr(session, key, value)
        
        session.last_activity = datetime.now()
        self._notify_state_change("session_updated", session_id, session)
        
        if self.auto_save:
            self._save_state()
        
        return True
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions.pop(session_id)
        self._notify_state_change("session_deleted", session_id, session)
        
        if self.auto_save:
            self._save_state()
        
        return True
    
    def get_preferences(self, session_id: str) -> Optional[UIPreferences]:
        """Get user preferences for session"""
        session = self.get_session(session_id)
        return session.preferences if session else None
    
    def update_preferences(self, session_id: str, preference_updates: Dict[str, Any]) -> bool:
        """Update user preferences"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        # Update preference fields
        for key, value in preference_updates.items():
            if hasattr(session.preferences, key):
                setattr(session.preferences, key, value)
        
        session.last_activity = datetime.now()
        self._notify_state_change("preferences_updated", session_id, session.preferences)
        
        if self.auto_save:
            self._save_state()
        
        return True
    
    def get_view_state(self, session_id: str, view_id: str) -> Optional[ViewState]:
        """Get state for a specific view"""
        session = self.get_session(session_id)
        if not session:
            return None
        
        return session.view_states.get(view_id)
    
    def update_view_state(self, session_id: str, view_id: str, view_type: str, 
                         state_updates: Dict[str, Any]) -> bool:
        """Update state for a specific view"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        # Get or create view state
        if view_id not in session.view_states:
            session.view_states[view_id] = ViewState(
                view_id=view_id,
                view_type=view_type
            )
        
        view_state = session.view_states[view_id]
        
        # Update view state fields
        for key, value in state_updates.items():
            if hasattr(view_state, key):
                if key == "expanded_items" and isinstance(value, list):
                    # Convert list to set for expanded_items
                    setattr(view_state, key, set(value))
                else:
                    setattr(view_state, key, value)
        
        view_state.last_updated = datetime.now()
        session.last_activity = datetime.now()
        
        self._notify_state_change("view_state_updated", session_id, view_state)
        
        if self.auto_save:
            self._save_state()
        
        return True
    
    def get_state(self, session_id: str) -> Dict[str, Any]:
        """Get complete state for session"""
        session = self.get_session(session_id)
        if not session:
            return {}
        
        # Convert to dictionary with proper serialization
        state_dict = asdict(session)
        
        # Handle special fields that need custom serialization
        state_dict["created_at"] = session.created_at.isoformat()
        state_dict["last_activity"] = session.last_activity.isoformat()
        
        # Convert view states
        view_states_dict = {}
        for view_id, view_state in session.view_states.items():
            view_dict = asdict(view_state)
            view_dict["last_updated"] = view_state.last_updated.isoformat()
            view_dict["expanded_items"] = list(view_state.expanded_items)
            view_states_dict[view_id] = view_dict
        
        state_dict["view_states"] = view_states_dict
        
        return state_dict
    
    def update_state(self, session_id: str, state_updates: Dict[str, Any]):
        """Update complete state for session"""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Handle nested updates
        for key, value in state_updates.items():
            if key == "preferences" and isinstance(value, dict):
                self.update_preferences(session_id, value)
            elif key == "view_states" and isinstance(value, dict):
                for view_id, view_updates in value.items():
                    view_type = view_updates.get("view_type", "unknown")
                    self.update_view_state(session_id, view_id, view_type, view_updates)
            elif hasattr(session, key):
                setattr(session, key, value)
        
        session.last_activity = datetime.now()
        
        if self.auto_save:
            self._save_state()
    
    def add_recent_file(self, session_id: str, file_path: str, max_recent: int = 10):
        """Add file to recent files list"""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Remove if already exists
        if file_path in session.recent_files:
            session.recent_files.remove(file_path)
        
        # Add to beginning
        session.recent_files.insert(0, file_path)
        
        # Limit size
        session.recent_files = session.recent_files[:max_recent]
        
        session.last_activity = datetime.now()
        
        if self.auto_save:
            self._save_state()
    
    def add_bookmark(self, session_id: str, bookmark: Dict[str, Any]):
        """Add bookmark"""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Add timestamp if not present
        if "created_at" not in bookmark:
            bookmark["created_at"] = datetime.now().isoformat()
        
        session.bookmarks.append(bookmark)
        session.last_activity = datetime.now()
        
        if self.auto_save:
            self._save_state()
    
    def remove_bookmark(self, session_id: str, bookmark_id: str) -> bool:
        """Remove bookmark by ID"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        original_length = len(session.bookmarks)
        session.bookmarks = [b for b in session.bookmarks if b.get("id") != bookmark_id]
        
        if len(session.bookmarks) < original_length:
            session.last_activity = datetime.now()
            if self.auto_save:
                self._save_state()
            return True
        
        return False
    
    def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """Clean up expired sessions"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if session.last_activity < cutoff_time
        ]
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
        
        logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    def register_state_change_handler(self, handler: callable):
        """Register handler for state changes"""
        self.state_change_handlers.append(handler)
    
    def unregister_state_change_handler(self, handler: callable):
        """Unregister state change handler"""
        if handler in self.state_change_handlers:
            self.state_change_handlers.remove(handler)
    
    def _notify_state_change(self, change_type: str, session_id: str, data: Any):
        """Notify registered handlers of state changes"""
        for handler in self.state_change_handlers:
            try:
                handler(change_type, session_id, data)
            except Exception as e:
                logger.error(f"Error in state change handler: {e}")
    
    def _save_state(self):
        """Save state to file"""
        try:
            # Convert sessions to serializable format
            serializable_sessions = {}
            
            for session_id, session in self.sessions.items():
                session_dict = asdict(session)
                
                # Convert datetime objects
                session_dict["created_at"] = session.created_at.isoformat()
                session_dict["last_activity"] = session.last_activity.isoformat()
                
                # Convert view states
                view_states_dict = {}
                for view_id, view_state in session.view_states.items():
                    view_dict = asdict(view_state)
                    view_dict["last_updated"] = view_state.last_updated.isoformat()
                    view_dict["expanded_items"] = list(view_state.expanded_items)
                    view_states_dict[view_id] = view_dict
                
                session_dict["view_states"] = view_states_dict
                serializable_sessions[session_id] = session_dict
            
            # Save to file
            with open(self.state_file, 'w') as f:
                json.dump(serializable_sessions, f, indent=2)
            
            self.last_save = datetime.now()
            
        except Exception as e:
            logger.error(f"Error saving UI state: {e}")
    
    def _load_state(self):
        """Load state from file"""
        try:
            if not self.state_file.exists():
                return
            
            with open(self.state_file, 'r') as f:
                data = json.load(f)
            
            # Convert back to SessionState objects
            for session_id, session_data in data.items():
                # Convert datetime strings back
                session_data["created_at"] = datetime.fromisoformat(session_data["created_at"])
                session_data["last_activity"] = datetime.fromisoformat(session_data["last_activity"])
                
                # Convert preferences
                preferences_data = session_data.get("preferences", {})
                preferences = UIPreferences(**preferences_data)
                
                # Convert view states
                view_states = {}
                view_states_data = session_data.get("view_states", {})
                for view_id, view_data in view_states_data.items():
                    view_data["last_updated"] = datetime.fromisoformat(view_data["last_updated"])
                    view_data["expanded_items"] = set(view_data.get("expanded_items", []))
                    view_states[view_id] = ViewState(**view_data)
                
                # Create session
                session = SessionState(
                    session_id=session_id,
                    user_id=session_data.get("user_id"),
                    created_at=session_data["created_at"],
                    last_activity=session_data["last_activity"],
                    preferences=preferences,
                    view_states=view_states,
                    workspace_path=session_data.get("workspace_path"),
                    active_analysis_id=session_data.get("active_analysis_id"),
                    recent_files=session_data.get("recent_files", []),
                    bookmarks=session_data.get("bookmarks", []),
                    custom_data=session_data.get("custom_data", {})
                )
                
                self.sessions[session_id] = session
            
            logger.info(f"Loaded UI state for {len(self.sessions)} sessions")
            
        except Exception as e:
            logger.error(f"Error loading UI state: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get state manager statistics"""
        total_view_states = sum(len(session.view_states) for session in self.sessions.values())
        
        return {
            "total_sessions": len(self.sessions),
            "total_view_states": total_view_states,
            "state_file": str(self.state_file),
            "last_save": self.last_save.isoformat() if self.last_save else None,
            "auto_save": self.auto_save
        }


# Global UI state manager instance
ui_state_manager = UIStateManager()
