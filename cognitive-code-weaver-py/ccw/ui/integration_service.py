"""
UI Integration Service

Coordinates between the core system and UI components, providing
data transformation, state management, and real-time updates.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, asdict

from ccw.core.message_bus import message_bus, MessageHandler, Message, MessageType
from ccw.core.registry import agent_registry
from ccw.core.agent import AgentStatus
from ccw.analysis.workspace import WorkspaceAnalyzer
from ccw.ui.data_transformers import DataTransformer, VisualizationDataTransformer
from ccw.ui.state_manager import UIStateManager

logger = logging.getLogger(__name__)


@dataclass
class UIEvent:
    """UI event data structure"""
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
    source: str
    target: Optional[str] = None


class UIIntegrationService(MessageHandler):
    """Service for integrating core system with UI components"""
    
    def __init__(self):
        super().__init__("ui_integration_service")
        self.data_transformer = DataTransformer()
        self.viz_transformer = VisualizationDataTransformer()
        self.state_manager = UIStateManager()
        self.workspace_analyzer = WorkspaceAnalyzer()
        
        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # Active sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # UI state cache
        self.ui_cache: Dict[str, Any] = {}
        
        self.running = False
    
    async def start(self):
        """Start the UI integration service"""
        if self.running:
            return
        
        self.running = True
        
        # Register with message bus
        message_bus.register_handler(self)
        message_bus.subscribe_to_topic(self.handler_id, "agent_status")
        message_bus.subscribe_to_topic(self.handler_id, "task_progress")
        message_bus.subscribe_to_topic(self.handler_id, "analysis_complete")
        message_bus.subscribe_to_topic(self.handler_id, "system_events")
        
        logger.info("UI Integration Service started")
    
    async def stop(self):
        """Stop the UI integration service"""
        if not self.running:
            return
        
        self.running = False
        
        # Unregister from message bus
        message_bus.unregister_handler(self.handler_id)
        
        # Clear active sessions
        self.active_sessions.clear()
        self.ui_cache.clear()
        
        logger.info("UI Integration Service stopped")
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """Handle messages from the message bus"""
        try:
            if message.type == MessageType.STATUS_UPDATE:
                await self._handle_agent_status_update(message)
            
            elif message.type == MessageType.TASK_PROGRESS:
                await self._handle_task_progress_update(message)
            
            elif message.type == MessageType.ANALYSIS_COMPLETE:
                await self._handle_analysis_complete(message)
            
            elif message.type == MessageType.SYSTEM_EVENT:
                await self._handle_system_event(message)
            
        except Exception as e:
            logger.error(f"Error handling message in UI service: {e}")
        
        return None
    
    # Session Management
    def create_session(self, session_id: str, user_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a new UI session"""
        session_data = {
            "session_id": session_id,
            "created_at": datetime.now(),
            "user_info": user_info or {},
            "active_analyses": [],
            "active_tasks": [],
            "workspace_path": None,
            "preferences": self._get_default_preferences(),
            "state": {}
        }
        
        self.active_sessions[session_id] = session_data
        
        # Emit session created event
        self._emit_ui_event("session_created", {
            "session_id": session_id,
            "session_data": session_data
        })
        
        return session_data
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        return self.active_sessions.get(session_id)
    
    def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session data"""
        if session_id not in self.active_sessions:
            return False
        
        self.active_sessions[session_id].update(updates)
        
        # Emit session updated event
        self._emit_ui_event("session_updated", {
            "session_id": session_id,
            "updates": updates
        })
        
        return True
    
    def close_session(self, session_id: str) -> bool:
        """Close a UI session"""
        if session_id not in self.active_sessions:
            return False
        
        session_data = self.active_sessions.pop(session_id)
        
        # Emit session closed event
        self._emit_ui_event("session_closed", {
            "session_id": session_id,
            "session_data": session_data
        })
        
        return True
    
    # Data Transformation for UI
    def get_dashboard_data(self, session_id: str) -> Dict[str, Any]:
        """Get dashboard data for UI"""
        try:
            # Get system status
            agents_info = agent_registry.list_agents()
            running_agents = sum(1 for agent in agents_info if agent.get('is_running', False))
            
            # Get session data
            session = self.get_session(session_id)
            
            # Transform data for UI
            dashboard_data = {
                "system_status": {
                    "agents_running": running_agents,
                    "total_agents": len(agents_info),
                    "active_analyses": len(session.get('active_analyses', [])) if session else 0,
                    "active_tasks": len(session.get('active_tasks', [])) if session else 0,
                    "timestamp": datetime.now().isoformat()
                },
                "agents": self.data_transformer.transform_agents_for_ui(agents_info),
                "recent_activities": self._get_recent_activities(session_id),
                "workspace_info": self._get_workspace_info(session_id)
            }
            
            return dashboard_data
            
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {"error": str(e)}
    
    def get_analysis_visualization_data(self, analysis_result: Any) -> Dict[str, Any]:
        """Transform analysis result for visualization"""
        try:
            return self.viz_transformer.transform_analysis_result(analysis_result)
        except Exception as e:
            logger.error(f"Error transforming analysis data: {e}")
            return {"error": str(e)}
    
    def get_agent_network_data(self) -> Dict[str, Any]:
        """Get agent network visualization data"""
        try:
            agents_info = agent_registry.list_agents()
            return self.viz_transformer.transform_agent_network(agents_info)
        except Exception as e:
            logger.error(f"Error getting agent network data: {e}")
            return {"error": str(e)}
    
    def get_code_metrics_visualization(self, workspace_path: str) -> Dict[str, Any]:
        """Get code metrics visualization data"""
        try:
            # This would typically use cached analysis results
            # For now, return mock data structure
            return self.viz_transformer.create_metrics_visualization(workspace_path)
        except Exception as e:
            logger.error(f"Error getting code metrics visualization: {e}")
            return {"error": str(e)}
    
    # Real-time Updates
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register event handler for UI updates"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
    
    def unregister_event_handler(self, event_type: str, handler: Callable):
        """Unregister event handler"""
        if event_type in self.event_handlers:
            self.event_handlers[event_type] = [
                h for h in self.event_handlers[event_type] if h != handler
            ]
    
    def _emit_ui_event(self, event_type: str, data: Dict[str, Any], target: Optional[str] = None):
        """Emit UI event to registered handlers"""
        event = UIEvent(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source="ui_integration_service",
            target=target
        )
        
        # Call registered handlers
        handlers = self.event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    asyncio.create_task(handler(event))
                else:
                    handler(event)
            except Exception as e:
                logger.error(f"Error in UI event handler: {e}")
    
    # Message Bus Event Handlers
    async def _handle_agent_status_update(self, message: Message):
        """Handle agent status update"""
        agent_id = message.sender
        status_data = message.payload
        
        # Transform for UI
        ui_data = self.data_transformer.transform_agent_status_for_ui(agent_id, status_data)
        
        # Emit UI event
        self._emit_ui_event("agent_status_update", ui_data)
        
        # Update cache
        self.ui_cache[f"agent_status_{agent_id}"] = ui_data
    
    async def _handle_task_progress_update(self, message: Message):
        """Handle task progress update"""
        task_data = message.payload
        
        # Transform for UI
        ui_data = self.data_transformer.transform_task_progress_for_ui(task_data)
        
        # Emit UI event
        self._emit_ui_event("task_progress_update", ui_data)
        
        # Update session data
        task_id = task_data.get("task_id")
        if task_id:
            for session_id, session_data in self.active_sessions.items():
                if task_id in session_data.get("active_tasks", []):
                    # Update task in session
                    break
    
    async def _handle_analysis_complete(self, message: Message):
        """Handle analysis completion"""
        analysis_data = message.payload
        
        # Transform for UI
        ui_data = self.data_transformer.transform_analysis_result_for_ui(analysis_data)
        viz_data = self.viz_transformer.transform_analysis_result(analysis_data)
        
        # Combine data
        complete_data = {
            **ui_data,
            "visualizations": viz_data
        }
        
        # Emit UI event
        self._emit_ui_event("analysis_complete", complete_data)
        
        # Update cache
        analysis_id = analysis_data.get("analysis_id")
        if analysis_id:
            self.ui_cache[f"analysis_{analysis_id}"] = complete_data
    
    async def _handle_system_event(self, message: Message):
        """Handle system event"""
        event_data = message.payload
        
        # Transform for UI
        ui_data = self.data_transformer.transform_system_event_for_ui(event_data)
        
        # Emit UI event
        self._emit_ui_event("system_event", ui_data)
    
    # Helper Methods
    def _get_default_preferences(self) -> Dict[str, Any]:
        """Get default UI preferences"""
        return {
            "theme": "light",
            "auto_refresh": True,
            "refresh_interval": 5000,  # milliseconds
            "show_notifications": True,
            "visualization_preferences": {
                "default_chart_type": "bar",
                "color_scheme": "default",
                "animation_enabled": True
            }
        }
    
    def _get_recent_activities(self, session_id: str) -> List[Dict[str, Any]]:
        """Get recent activities for session"""
        # In production, this would query activity logs
        # For now, return mock data
        return [
            {
                "id": "activity_1",
                "type": "analysis_started",
                "description": "Started workspace analysis",
                "timestamp": datetime.now().isoformat(),
                "status": "completed"
            }
        ]
    
    def _get_workspace_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get workspace information for session"""
        session = self.get_session(session_id)
        if not session or not session.get("workspace_path"):
            return None
        
        workspace_path = session["workspace_path"]
        
        # Get basic workspace info
        try:
            from pathlib import Path
            workspace = Path(workspace_path)
            
            if not workspace.exists():
                return None
            
            # Count files by type
            py_files = len(list(workspace.glob("**/*.py")))
            js_files = len(list(workspace.glob("**/*.js")))
            ts_files = len(list(workspace.glob("**/*.ts")))
            
            return {
                "path": str(workspace),
                "name": workspace.name,
                "file_counts": {
                    "python": py_files,
                    "javascript": js_files,
                    "typescript": ts_files,
                    "total": py_files + js_files + ts_files
                },
                "last_analyzed": session.get("last_analysis_time")
            }
            
        except Exception as e:
            logger.error(f"Error getting workspace info: {e}")
            return None
    
    # Cache Management
    def get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached UI data"""
        return self.ui_cache.get(key)
    
    def set_cached_data(self, key: str, data: Any):
        """Set cached UI data"""
        self.ui_cache[key] = data
    
    def clear_cache(self, pattern: Optional[str] = None):
        """Clear cached data"""
        if pattern:
            keys_to_remove = [key for key in self.ui_cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self.ui_cache[key]
        else:
            self.ui_cache.clear()
    
    # State Management
    def get_ui_state(self, session_id: str) -> Dict[str, Any]:
        """Get UI state for session"""
        return self.state_manager.get_state(session_id)
    
    def update_ui_state(self, session_id: str, state_updates: Dict[str, Any]):
        """Update UI state for session"""
        self.state_manager.update_state(session_id, state_updates)
        
        # Emit state update event
        self._emit_ui_event("ui_state_update", {
            "session_id": session_id,
            "state_updates": state_updates
        })


# Global UI integration service instance
ui_service = UIIntegrationService()
