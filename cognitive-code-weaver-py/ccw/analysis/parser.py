"""
Code Parser - Multi-language code parsing system

Provides unified interface for parsing different programming languages
using Tree-sitter and language-specific parsers.
"""

import logging
import os
import re
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
from enum import Enum

try:
    import tree_sitter
    from tree_sitter import Language, Parser, Node
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

logger = logging.getLogger(__name__)


class LanguageType(Enum):
    """Supported programming languages"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    C = "c"
    CPP = "cpp"
    CSHARP = "csharp"
    GO = "go"
    RUST = "rust"
    RUBY = "ruby"
    PHP = "php"
    KOTLIN = "kotlin"
    SWIFT = "swift"
    UNKNOWN = "unknown"


@dataclass
class ParseResult:
    """Result of parsing a code file"""
    file_path: str
    language: LanguageType
    content: str
    tree: Optional[Any] = None  # Tree-sitter tree
    ast_nodes: List[Dict[str, Any]] = field(default_factory=list)
    symbols: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    exports: List[str] = field(default_factory=list)
    functions: List[Dict[str, Any]] = field(default_factory=list)
    classes: List[Dict[str, Any]] = field(default_factory=list)
    variables: List[Dict[str, Any]] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class LanguageParser(ABC):
    """Abstract base class for language-specific parsers"""
    
    def __init__(self, language: LanguageType):
        self.language = language
        self.logger = logging.getLogger(f"ccw.analysis.parser.{language.value}")
    
    @abstractmethod
    def parse(self, content: str, file_path: str = "") -> ParseResult:
        """Parse code content and return structured result"""
        pass
    
    @abstractmethod
    def extract_symbols(self, content: str) -> List[Dict[str, Any]]:
        """Extract symbols (functions, classes, variables) from code"""
        pass
    
    @abstractmethod
    def extract_imports(self, content: str) -> List[str]:
        """Extract import statements from code"""
        pass
    
    def get_file_extensions(self) -> List[str]:
        """Get file extensions for this language"""
        extensions = {
            LanguageType.PYTHON: [".py", ".pyw"],
            LanguageType.JAVASCRIPT: [".js", ".mjs"],
            LanguageType.TYPESCRIPT: [".ts", ".tsx"],
            LanguageType.JAVA: [".java"],
            LanguageType.C: [".c", ".h"],
            LanguageType.CPP: [".cpp", ".cxx", ".cc", ".hpp", ".hxx"],
            LanguageType.CSHARP: [".cs"],
            LanguageType.GO: [".go"],
            LanguageType.RUST: [".rs"],
            LanguageType.RUBY: [".rb"],
            LanguageType.PHP: [".php"],
            LanguageType.KOTLIN: [".kt", ".kts"],
            LanguageType.SWIFT: [".swift"]
        }
        return extensions.get(self.language, [])


class TreeSitterParser(LanguageParser):
    """Tree-sitter based parser for multiple languages"""
    
    def __init__(self, language: LanguageType):
        super().__init__(language)
        self.parser: Optional[Parser] = None
        self.ts_language: Optional[Language] = None
        
        if TREE_SITTER_AVAILABLE:
            self._initialize_parser()
    
    def _initialize_parser(self):
        """Initialize Tree-sitter parser for the language"""
        try:
            # This would need actual Tree-sitter language libraries
            # For now, we'll use a fallback approach
            self.parser = Parser()
            
            # Language library paths would be configured here
            # self.ts_language = Language(library_path, language_name)
            # self.parser.set_language(self.ts_language)
            
            self.logger.info(f"Tree-sitter parser initialized for {self.language.value}")
            
        except Exception as e:
            self.logger.warning(f"Failed to initialize Tree-sitter for {self.language.value}: {e}")
            self.parser = None
    
    def parse(self, content: str, file_path: str = "") -> ParseResult:
        """Parse code using Tree-sitter"""
        result = ParseResult(
            file_path=file_path,
            language=self.language,
            content=content
        )
        
        try:
            if self.parser and self.ts_language:
                # Parse with Tree-sitter
                tree = self.parser.parse(bytes(content, "utf8"))
                result.tree = tree
                result.ast_nodes = self._extract_ast_nodes(tree.root_node)
            else:
                # Fallback to regex-based parsing
                result = self._fallback_parse(content, file_path)
            
            # Extract language-specific elements
            result.symbols = self.extract_symbols(content)
            result.imports = self.extract_imports(content)
            result.functions = self._extract_functions(content)
            result.classes = self._extract_classes(content)
            result.variables = self._extract_variables(content)
            
        except Exception as e:
            error_msg = f"Parsing failed for {file_path}: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
        
        return result
    
    def _extract_ast_nodes(self, node: Any) -> List[Dict[str, Any]]:
        """Extract AST nodes from Tree-sitter tree"""
        nodes = []
        
        def traverse(n, depth=0):
            node_info = {
                "type": n.type,
                "start_point": n.start_point,
                "end_point": n.end_point,
                "text": n.text.decode("utf8") if n.text else "",
                "depth": depth,
                "children_count": len(n.children)
            }
            nodes.append(node_info)
            
            for child in n.children:
                traverse(child, depth + 1)
        
        traverse(node)
        return nodes
    
    def _fallback_parse(self, content: str, file_path: str) -> ParseResult:
        """Fallback parsing using regex patterns"""
        result = ParseResult(
            file_path=file_path,
            language=self.language,
            content=content
        )
        
        # Basic line-by-line analysis
        lines = content.split('\n')
        for i, line in enumerate(lines):
            line_info = {
                "line_number": i + 1,
                "content": line.strip(),
                "type": self._classify_line(line.strip())
            }
            result.ast_nodes.append(line_info)
        
        return result
    
    def _classify_line(self, line: str) -> str:
        """Classify a line of code"""
        line = line.strip()
        
        if not line or line.startswith('#') or line.startswith('//'):
            return "comment"
        elif line.startswith('import ') or line.startswith('from '):
            return "import"
        elif 'def ' in line or 'function ' in line:
            return "function_definition"
        elif 'class ' in line:
            return "class_definition"
        elif '=' in line and not line.startswith('if '):
            return "assignment"
        else:
            return "statement"
    
    def extract_symbols(self, content: str) -> List[Dict[str, Any]]:
        """Extract symbols using language-specific patterns"""
        symbols = []
        
        if self.language == LanguageType.PYTHON:
            symbols.extend(self._extract_python_symbols(content))
        elif self.language == LanguageType.JAVASCRIPT:
            symbols.extend(self._extract_javascript_symbols(content))
        elif self.language == LanguageType.JAVA:
            symbols.extend(self._extract_java_symbols(content))
        
        return symbols
    
    def extract_imports(self, content: str) -> List[str]:
        """Extract import statements"""
        imports = []
        
        if self.language == LanguageType.PYTHON:
            imports.extend(self._extract_python_imports(content))
        elif self.language == LanguageType.JAVASCRIPT:
            imports.extend(self._extract_javascript_imports(content))
        elif self.language == LanguageType.JAVA:
            imports.extend(self._extract_java_imports(content))
        
        return imports
    
    def _extract_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract function definitions"""
        functions = []
        
        if self.language == LanguageType.PYTHON:
            pattern = r'def\s+(\w+)\s*\((.*?)\):'
            for match in re.finditer(pattern, content):
                functions.append({
                    "name": match.group(1),
                    "parameters": match.group(2),
                    "line": content[:match.start()].count('\n') + 1
                })
        
        elif self.language == LanguageType.JAVASCRIPT:
            # Function declarations
            pattern = r'function\s+(\w+)\s*\((.*?)\)'
            for match in re.finditer(pattern, content):
                functions.append({
                    "name": match.group(1),
                    "parameters": match.group(2),
                    "line": content[:match.start()].count('\n') + 1,
                    "type": "function_declaration"
                })
            
            # Arrow functions
            pattern = r'const\s+(\w+)\s*=\s*\((.*?)\)\s*=>'
            for match in re.finditer(pattern, content):
                functions.append({
                    "name": match.group(1),
                    "parameters": match.group(2),
                    "line": content[:match.start()].count('\n') + 1,
                    "type": "arrow_function"
                })
        
        return functions
    
    def _extract_classes(self, content: str) -> List[Dict[str, Any]]:
        """Extract class definitions"""
        classes = []
        
        if self.language == LanguageType.PYTHON:
            pattern = r'class\s+(\w+)(?:\((.*?)\))?:'
            for match in re.finditer(pattern, content):
                classes.append({
                    "name": match.group(1),
                    "inheritance": match.group(2) if match.group(2) else None,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        elif self.language == LanguageType.JAVA:
            pattern = r'class\s+(\w+)(?:\s+extends\s+(\w+))?'
            for match in re.finditer(pattern, content):
                classes.append({
                    "name": match.group(1),
                    "inheritance": match.group(2) if match.group(2) else None,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        return classes
    
    def _extract_variables(self, content: str) -> List[Dict[str, Any]]:
        """Extract variable assignments"""
        variables = []
        
        # Simple variable assignment pattern
        pattern = r'^(\s*)(\w+)\s*=\s*(.+)$'
        for i, line in enumerate(content.split('\n')):
            match = re.match(pattern, line)
            if match and not line.strip().startswith('#'):
                variables.append({
                    "name": match.group(2),
                    "value": match.group(3).strip(),
                    "line": i + 1,
                    "indentation": len(match.group(1))
                })
        
        return variables
    
    def _extract_python_symbols(self, content: str) -> List[Dict[str, Any]]:
        """Extract Python-specific symbols"""
        symbols = []
        
        # Functions
        for match in re.finditer(r'def\s+(\w+)', content):
            symbols.append({
                "name": match.group(1),
                "type": "function",
                "line": content[:match.start()].count('\n') + 1
            })
        
        # Classes
        for match in re.finditer(r'class\s+(\w+)', content):
            symbols.append({
                "name": match.group(1),
                "type": "class",
                "line": content[:match.start()].count('\n') + 1
            })
        
        return symbols
    
    def _extract_javascript_symbols(self, content: str) -> List[Dict[str, Any]]:
        """Extract JavaScript-specific symbols"""
        symbols = []
        
        # Function declarations
        for match in re.finditer(r'function\s+(\w+)', content):
            symbols.append({
                "name": match.group(1),
                "type": "function",
                "line": content[:match.start()].count('\n') + 1
            })
        
        # Class declarations
        for match in re.finditer(r'class\s+(\w+)', content):
            symbols.append({
                "name": match.group(1),
                "type": "class",
                "line": content[:match.start()].count('\n') + 1
            })
        
        return symbols
    
    def _extract_java_symbols(self, content: str) -> List[Dict[str, Any]]:
        """Extract Java-specific symbols"""
        symbols = []
        
        # Method declarations
        for match in re.finditer(r'(public|private|protected)?\s*(static)?\s*\w+\s+(\w+)\s*\(', content):
            symbols.append({
                "name": match.group(3),
                "type": "method",
                "line": content[:match.start()].count('\n') + 1
            })
        
        # Class declarations
        for match in re.finditer(r'class\s+(\w+)', content):
            symbols.append({
                "name": match.group(1),
                "type": "class",
                "line": content[:match.start()].count('\n') + 1
            })
        
        return symbols
    
    def _extract_python_imports(self, content: str) -> List[str]:
        """Extract Python imports"""
        imports = []
        
        # import statements
        for match in re.finditer(r'import\s+([\w\.]+)', content):
            imports.append(match.group(1))
        
        # from ... import statements
        for match in re.finditer(r'from\s+([\w\.]+)\s+import', content):
            imports.append(match.group(1))
        
        return imports
    
    def _extract_javascript_imports(self, content: str) -> List[str]:
        """Extract JavaScript imports"""
        imports = []
        
        # ES6 imports
        for match in re.finditer(r'import.*from\s+[\'\"](.*?)[\'\"]', content):
            imports.append(match.group(1))
        
        # require statements
        for match in re.finditer(r'require\s*\(\s*[\'\"](.*?)[\'\"]\s*\)', content):
            imports.append(match.group(1))
        
        return imports
    
    def _extract_java_imports(self, content: str) -> List[str]:
        """Extract Java imports"""
        imports = []
        
        for match in re.finditer(r'import\s+([\w\.]+)', content):
            imports.append(match.group(1))
        
        return imports


class CodeParser:
    """Main code parser that delegates to language-specific parsers"""
    
    def __init__(self):
        self.parsers: Dict[LanguageType, LanguageParser] = {}
        self.logger = logging.getLogger("ccw.analysis.parser")
        
        # Initialize parsers for supported languages
        self._initialize_parsers()
    
    def _initialize_parsers(self):
        """Initialize language-specific parsers"""
        supported_languages = [
            LanguageType.PYTHON,
            LanguageType.JAVASCRIPT,
            LanguageType.TYPESCRIPT,
            LanguageType.JAVA,
            LanguageType.C,
            LanguageType.CPP
        ]
        
        for language in supported_languages:
            try:
                parser = TreeSitterParser(language)
                self.parsers[language] = parser
                self.logger.debug(f"Initialized parser for {language.value}")
            except Exception as e:
                self.logger.warning(f"Failed to initialize parser for {language.value}: {e}")
    
    def detect_language(self, file_path: str, content: Optional[str] = None) -> LanguageType:
        """Detect programming language from file path and content"""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # Extension-based detection
        extension_map = {
            '.py': LanguageType.PYTHON,
            '.pyw': LanguageType.PYTHON,
            '.js': LanguageType.JAVASCRIPT,
            '.mjs': LanguageType.JAVASCRIPT,
            '.ts': LanguageType.TYPESCRIPT,
            '.tsx': LanguageType.TYPESCRIPT,
            '.java': LanguageType.JAVA,
            '.c': LanguageType.C,
            '.h': LanguageType.C,
            '.cpp': LanguageType.CPP,
            '.cxx': LanguageType.CPP,
            '.cc': LanguageType.CPP,
            '.hpp': LanguageType.CPP,
            '.cs': LanguageType.CSHARP,
            '.go': LanguageType.GO,
            '.rs': LanguageType.RUST,
            '.rb': LanguageType.RUBY,
            '.php': LanguageType.PHP,
            '.kt': LanguageType.KOTLIN,
            '.swift': LanguageType.SWIFT
        }
        
        if extension in extension_map:
            return extension_map[extension]
        
        # Content-based detection if extension is unknown
        if content:
            return self._detect_language_from_content(content)
        
        return LanguageType.UNKNOWN
    
    def _detect_language_from_content(self, content: str) -> LanguageType:
        """Detect language from content patterns"""
        content_lower = content.lower()
        
        # Python indicators
        if 'def ' in content or 'import ' in content or 'from ' in content:
            return LanguageType.PYTHON
        
        # JavaScript indicators
        if 'function ' in content or 'const ' in content or 'let ' in content:
            return LanguageType.JAVASCRIPT
        
        # Java indicators
        if 'public class' in content or 'package ' in content:
            return LanguageType.JAVA
        
        return LanguageType.UNKNOWN
    
    def parse_file(self, file_path: str) -> ParseResult:
        """Parse a single file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            language = self.detect_language(file_path, content)
            
            if language in self.parsers:
                return self.parsers[language].parse(content, file_path)
            else:
                # Create basic result for unsupported languages
                return ParseResult(
                    file_path=file_path,
                    language=language,
                    content=content,
                    errors=[f"No parser available for {language.value}"]
                )
                
        except Exception as e:
            error_msg = f"Failed to parse file {file_path}: {e}"
            self.logger.error(error_msg)
            
            return ParseResult(
                file_path=file_path,
                language=LanguageType.UNKNOWN,
                content="",
                errors=[error_msg]
            )
    
    def parse_content(self, content: str, language: Optional[LanguageType] = None, 
                     file_path: str = "") -> ParseResult:
        """Parse content directly"""
        if language is None:
            language = self._detect_language_from_content(content)
        
        if language in self.parsers:
            return self.parsers[language].parse(content, file_path)
        else:
            return ParseResult(
                file_path=file_path,
                language=language,
                content=content,
                errors=[f"No parser available for {language.value}"]
            )
    
    def get_supported_languages(self) -> List[LanguageType]:
        """Get list of supported languages"""
        return list(self.parsers.keys())
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions"""
        extensions = []
        for parser in self.parsers.values():
            extensions.extend(parser.get_file_extensions())
        return list(set(extensions))
