"""
Symbol Extractor - Extract and analyze code symbols

Identifies and categorizes symbols (functions, classes, variables, etc.)
in code and tracks their relationships and usage patterns.
"""

import ast
import logging
import re
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set
from enum import Enum

from .parser import ParseResult, LanguageType

logger = logging.getLogger(__name__)


class SymbolType(Enum):
    """Types of code symbols"""
    FUNCTION = "function"
    METHOD = "method"
    CLASS = "class"
    VARIABLE = "variable"
    CONSTANT = "constant"
    PARAMETER = "parameter"
    IMPORT = "import"
    MODULE = "module"
    PROPERTY = "property"
    DECORATOR = "decorator"
    UNKNOWN = "unknown"


class Visibility(Enum):
    """Symbol visibility levels"""
    PUBLIC = "public"
    PRIVATE = "private"
    PROTECTED = "protected"
    INTERNAL = "internal"


@dataclass
class Symbol:
    """Represents a code symbol"""
    name: str
    symbol_type: SymbolType
    file_path: str
    line_number: int
    column_number: int = 0
    visibility: Visibility = Visibility.PUBLIC
    scope: Optional[str] = None
    parent: Optional[str] = None
    signature: Optional[str] = None
    docstring: Optional[str] = None
    decorators: List[str] = field(default_factory=list)
    references: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_reference(self, file_path: str, line_number: int, context: str = ""):
        """Add a reference to this symbol"""
        self.references.append({
            "file_path": file_path,
            "line_number": line_number,
            "context": context
        })
    
    def get_qualified_name(self) -> str:
        """Get fully qualified name including scope"""
        if self.parent:
            return f"{self.parent}.{self.name}"
        elif self.scope and self.scope != "global":
            return f"{self.scope}.{self.name}"
        else:
            return self.name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert symbol to dictionary representation"""
        return {
            "name": self.name,
            "type": self.symbol_type.value,
            "file_path": self.file_path,
            "line_number": self.line_number,
            "column_number": self.column_number,
            "visibility": self.visibility.value,
            "scope": self.scope,
            "parent": self.parent,
            "signature": self.signature,
            "docstring": self.docstring,
            "decorators": self.decorators,
            "qualified_name": self.get_qualified_name(),
            "reference_count": len(self.references),
            "references": self.references,
            "metadata": self.metadata
        }


class SymbolExtractor:
    """
    Extracts symbols from parsed code
    
    Identifies and categorizes all symbols in code including:
    - Functions and methods
    - Classes and interfaces
    - Variables and constants
    - Imports and modules
    - Cross-references and usage patterns
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ccw.analysis.symbols")
    
    def extract(self, parse_result: ParseResult) -> List[Symbol]:
        """Extract symbols from parsed code"""
        symbols = []
        
        try:
            if parse_result.language == LanguageType.PYTHON:
                symbols = self._extract_python_symbols(parse_result)
            elif parse_result.language == LanguageType.JAVASCRIPT:
                symbols = self._extract_javascript_symbols(parse_result)
            elif parse_result.language == LanguageType.JAVA:
                symbols = self._extract_java_symbols(parse_result)
            else:
                symbols = self._extract_generic_symbols(parse_result)
                
        except Exception as e:
            self.logger.error(f"Symbol extraction failed for {parse_result.file_path}: {e}")
        
        return symbols
    
    def _extract_python_symbols(self, parse_result: ParseResult) -> List[Symbol]:
        """Extract symbols from Python code using AST"""
        symbols = []
        
        try:
            tree = ast.parse(parse_result.content)
            symbols = self._extract_python_ast_symbols(tree, parse_result.file_path)
        except SyntaxError:
            # Fallback to regex-based extraction
            symbols = self._extract_python_regex_symbols(parse_result)
        
        return symbols
    
    def _extract_python_ast_symbols(self, tree: ast.AST, file_path: str) -> List[Symbol]:
        """Extract Python symbols using AST analysis"""
        symbols = []
        scope_stack = ["global"]
        
        def extract_from_node(node, parent_class=None):
            current_scope = ".".join(scope_stack)
            
            if isinstance(node, ast.FunctionDef):
                # Determine if it's a method or function
                symbol_type = SymbolType.METHOD if parent_class else SymbolType.FUNCTION
                visibility = self._get_python_visibility(node.name)
                
                # Extract signature
                signature = self._get_python_function_signature(node)
                
                # Extract docstring
                docstring = self._get_python_docstring(node)
                
                # Extract decorators
                decorators = [self._get_decorator_name(d) for d in node.decorator_list]
                
                symbol = Symbol(
                    name=node.name,
                    symbol_type=symbol_type,
                    file_path=file_path,
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    visibility=visibility,
                    scope=current_scope,
                    parent=parent_class,
                    signature=signature,
                    docstring=docstring,
                    decorators=decorators
                )
                symbols.append(symbol)
                
                # Process function body with new scope
                scope_stack.append(node.name)
                for child in node.body:
                    extract_from_node(child, parent_class)
                scope_stack.pop()
            
            elif isinstance(node, ast.ClassDef):
                visibility = self._get_python_visibility(node.name)
                docstring = self._get_python_docstring(node)
                decorators = [self._get_decorator_name(d) for d in node.decorator_list]
                
                # Get base classes
                base_classes = [self._get_base_name(base) for base in node.bases]
                
                symbol = Symbol(
                    name=node.name,
                    symbol_type=SymbolType.CLASS,
                    file_path=file_path,
                    line_number=node.lineno,
                    column_number=node.col_offset,
                    visibility=visibility,
                    scope=current_scope,
                    docstring=docstring,
                    decorators=decorators,
                    metadata={"base_classes": base_classes}
                )
                symbols.append(symbol)
                
                # Process class body
                scope_stack.append(node.name)
                for child in node.body:
                    extract_from_node(child, node.name)
                scope_stack.pop()
            
            elif isinstance(node, ast.Assign):
                # Extract variable assignments
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        visibility = self._get_python_visibility(target.id)
                        symbol_type = SymbolType.CONSTANT if target.id.isupper() else SymbolType.VARIABLE
                        
                        symbol = Symbol(
                            name=target.id,
                            symbol_type=symbol_type,
                            file_path=file_path,
                            line_number=node.lineno,
                            column_number=node.col_offset,
                            visibility=visibility,
                            scope=current_scope,
                            parent=parent_class
                        )
                        symbols.append(symbol)
            
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                # Extract imports
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        symbol = Symbol(
                            name=alias.asname or alias.name,
                            symbol_type=SymbolType.IMPORT,
                            file_path=file_path,
                            line_number=node.lineno,
                            column_number=node.col_offset,
                            scope=current_scope,
                            metadata={"module": alias.name}
                        )
                        symbols.append(symbol)
                else:  # ImportFrom
                    for alias in node.names:
                        symbol = Symbol(
                            name=alias.asname or alias.name,
                            symbol_type=SymbolType.IMPORT,
                            file_path=file_path,
                            line_number=node.lineno,
                            column_number=node.col_offset,
                            scope=current_scope,
                            metadata={"module": node.module, "imported_name": alias.name}
                        )
                        symbols.append(symbol)
            
            # Recursively process child nodes
            for child in ast.iter_child_nodes(node):
                if not isinstance(child, (ast.FunctionDef, ast.ClassDef)):
                    extract_from_node(child, parent_class)
        
        extract_from_node(tree)
        return symbols
    
    def _extract_python_regex_symbols(self, parse_result: ParseResult) -> List[Symbol]:
        """Fallback regex-based Python symbol extraction"""
        symbols = []
        content = parse_result.content
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Function definitions
            func_match = re.match(r'def\s+(\w+)\s*\((.*?)\):', stripped)
            if func_match:
                symbol = Symbol(
                    name=func_match.group(1),
                    symbol_type=SymbolType.FUNCTION,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    signature=f"def {func_match.group(1)}({func_match.group(2)})",
                    visibility=self._get_python_visibility(func_match.group(1))
                )
                symbols.append(symbol)
            
            # Class definitions
            class_match = re.match(r'class\s+(\w+)(?:\((.*?)\))?:', stripped)
            if class_match:
                symbol = Symbol(
                    name=class_match.group(1),
                    symbol_type=SymbolType.CLASS,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    visibility=self._get_python_visibility(class_match.group(1))
                )
                symbols.append(symbol)
            
            # Variable assignments
            var_match = re.match(r'(\w+)\s*=\s*(.+)', stripped)
            if var_match and not stripped.startswith('def ') and not stripped.startswith('class '):
                var_name = var_match.group(1)
                symbol_type = SymbolType.CONSTANT if var_name.isupper() else SymbolType.VARIABLE
                
                symbol = Symbol(
                    name=var_name,
                    symbol_type=symbol_type,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    visibility=self._get_python_visibility(var_name)
                )
                symbols.append(symbol)
        
        return symbols
    
    def _extract_javascript_symbols(self, parse_result: ParseResult) -> List[Symbol]:
        """Extract symbols from JavaScript code"""
        symbols = []
        content = parse_result.content
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Function declarations
            func_match = re.match(r'function\s+(\w+)\s*\((.*?)\)', stripped)
            if func_match:
                symbol = Symbol(
                    name=func_match.group(1),
                    symbol_type=SymbolType.FUNCTION,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    signature=f"function {func_match.group(1)}({func_match.group(2)})"
                )
                symbols.append(symbol)
            
            # Arrow functions
            arrow_match = re.match(r'(?:const|let|var)\s+(\w+)\s*=\s*\((.*?)\)\s*=>', stripped)
            if arrow_match:
                symbol = Symbol(
                    name=arrow_match.group(1),
                    symbol_type=SymbolType.FUNCTION,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    signature=f"{arrow_match.group(1)} = ({arrow_match.group(2)}) =>"
                )
                symbols.append(symbol)
            
            # Class declarations
            class_match = re.match(r'class\s+(\w+)(?:\s+extends\s+(\w+))?', stripped)
            if class_match:
                symbol = Symbol(
                    name=class_match.group(1),
                    symbol_type=SymbolType.CLASS,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    metadata={"extends": class_match.group(2)} if class_match.group(2) else {}
                )
                symbols.append(symbol)
            
            # Variable declarations
            var_match = re.match(r'(?:const|let|var)\s+(\w+)', stripped)
            if var_match:
                var_name = var_match.group(1)
                symbol_type = SymbolType.CONSTANT if stripped.startswith('const') else SymbolType.VARIABLE
                
                symbol = Symbol(
                    name=var_name,
                    symbol_type=symbol_type,
                    file_path=parse_result.file_path,
                    line_number=line_num
                )
                symbols.append(symbol)
        
        return symbols
    
    def _extract_java_symbols(self, parse_result: ParseResult) -> List[Symbol]:
        """Extract symbols from Java code"""
        symbols = []
        content = parse_result.content
        lines = content.split('\n')
        
        current_class = None
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Class declarations
            class_match = re.match(r'(?:public|private|protected)?\s*class\s+(\w+)(?:\s+extends\s+(\w+))?', stripped)
            if class_match:
                current_class = class_match.group(1)
                symbol = Symbol(
                    name=current_class,
                    symbol_type=SymbolType.CLASS,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    visibility=self._get_java_visibility(stripped),
                    metadata={"extends": class_match.group(2)} if class_match.group(2) else {}
                )
                symbols.append(symbol)
            
            # Method declarations
            method_match = re.match(r'(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\((.*?)\)', stripped)
            if method_match and current_class:
                symbol = Symbol(
                    name=method_match.group(1),
                    symbol_type=SymbolType.METHOD,
                    file_path=parse_result.file_path,
                    line_number=line_num,
                    visibility=self._get_java_visibility(stripped),
                    parent=current_class,
                    signature=f"{method_match.group(1)}({method_match.group(2)})"
                )
                symbols.append(symbol)
        
        return symbols
    
    def _extract_generic_symbols(self, parse_result: ParseResult) -> List[Symbol]:
        """Generic symbol extraction for unsupported languages"""
        symbols = []
        
        # Use the symbols already extracted by the parser
        for func in parse_result.functions:
            symbol = Symbol(
                name=func['name'],
                symbol_type=SymbolType.FUNCTION,
                file_path=parse_result.file_path,
                line_number=func.get('line', 0)
            )
            symbols.append(symbol)
        
        for cls in parse_result.classes:
            symbol = Symbol(
                name=cls['name'],
                symbol_type=SymbolType.CLASS,
                file_path=parse_result.file_path,
                line_number=cls.get('line', 0)
            )
            symbols.append(symbol)
        
        return symbols
    
    def find_symbol_references(self, symbols: List[Symbol], parse_results: List[ParseResult]) -> List[Symbol]:
        """Find references to symbols across multiple files"""
        # Create symbol lookup table
        symbol_table = {symbol.name: symbol for symbol in symbols}
        
        # Search for references in all files
        for parse_result in parse_results:
            content = parse_result.content
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                line_num = i + 1
                
                # Find symbol references in this line
                for symbol_name, symbol in symbol_table.items():
                    if symbol_name in line and symbol.file_path != parse_result.file_path:
                        # Found a potential reference
                        symbol.add_reference(parse_result.file_path, line_num, line.strip())
        
        return symbols
    
    # Helper methods
    def _get_python_visibility(self, name: str) -> Visibility:
        """Determine Python symbol visibility from name"""
        if name.startswith('__') and name.endswith('__'):
            return Visibility.PUBLIC  # Magic methods are public
        elif name.startswith('__'):
            return Visibility.PRIVATE
        elif name.startswith('_'):
            return Visibility.PROTECTED
        else:
            return Visibility.PUBLIC
    
    def _get_java_visibility(self, line: str) -> Visibility:
        """Determine Java symbol visibility from declaration"""
        if 'private' in line:
            return Visibility.PRIVATE
        elif 'protected' in line:
            return Visibility.PROTECTED
        elif 'public' in line:
            return Visibility.PUBLIC
        else:
            return Visibility.INTERNAL  # Package-private
    
    def _get_python_function_signature(self, node: ast.FunctionDef) -> str:
        """Extract function signature from AST node"""
        args = []
        for arg in node.args.args:
            args.append(arg.arg)
        
        return f"def {node.name}({', '.join(args)})"
    
    def _get_python_docstring(self, node) -> Optional[str]:
        """Extract docstring from AST node"""
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            return node.body[0].value.value
        return None
    
    def _get_decorator_name(self, decorator: ast.AST) -> str:
        """Get decorator name from AST node"""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{self._get_attr_name(decorator.value)}.{decorator.attr}"
        else:
            return str(decorator)
    
    def _get_base_name(self, base: ast.AST) -> str:
        """Get base class name from AST node"""
        if isinstance(base, ast.Name):
            return base.id
        elif isinstance(base, ast.Attribute):
            return f"{self._get_attr_name(base.value)}.{base.attr}"
        else:
            return str(base)
    
    def _get_attr_name(self, node: ast.AST) -> str:
        """Get attribute name from AST node"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_attr_name(node.value)}.{node.attr}"
        else:
            return "unknown"
