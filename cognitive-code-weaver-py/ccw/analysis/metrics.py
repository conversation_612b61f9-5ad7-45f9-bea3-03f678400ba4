"""
Code Metrics - Comprehensive code quality and complexity analysis

Calculates various code metrics including complexity, maintainability,
readability, and quality indicators for code assessment.
"""

import ast
import logging
import re
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set
from enum import Enum

from .parser import ParseResult, LanguageType

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of code metrics"""
    COMPLEXITY = "complexity"
    SIZE = "size"
    COUPLING = "coupling"
    COHESION = "cohesion"
    MAINTAINABILITY = "maintainability"
    READABILITY = "readability"
    QUALITY = "quality"


@dataclass
class ComplexityMetrics:
    """Complexity-related metrics"""
    cyclomatic_complexity: int = 0
    cognitive_complexity: int = 0
    nesting_depth: int = 0
    halstead_difficulty: float = 0.0
    halstead_effort: float = 0.0
    halstead_volume: float = 0.0


@dataclass
class SizeMetrics:
    """Size-related metrics"""
    lines_of_code: int = 0
    source_lines_of_code: int = 0
    comment_lines: int = 0
    blank_lines: int = 0
    functions_count: int = 0
    classes_count: int = 0
    methods_count: int = 0
    variables_count: int = 0


@dataclass
class QualityMetrics:
    """Quality-related metrics"""
    comment_ratio: float = 0.0
    docstring_coverage: float = 0.0
    naming_consistency: float = 0.0
    code_duplication: float = 0.0
    test_coverage: float = 0.0


@dataclass
class CodeMetrics:
    """Comprehensive code metrics"""
    file_path: str
    language: LanguageType
    complexity: ComplexityMetrics = field(default_factory=ComplexityMetrics)
    size: SizeMetrics = field(default_factory=SizeMetrics)
    quality: QualityMetrics = field(default_factory=QualityMetrics)
    maintainability_index: float = 0.0
    technical_debt_ratio: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            "file_path": self.file_path,
            "language": self.language.value,
            "complexity": {
                "cyclomatic_complexity": self.complexity.cyclomatic_complexity,
                "cognitive_complexity": self.complexity.cognitive_complexity,
                "nesting_depth": self.complexity.nesting_depth,
                "halstead_difficulty": self.complexity.halstead_difficulty,
                "halstead_effort": self.complexity.halstead_effort,
                "halstead_volume": self.complexity.halstead_volume
            },
            "size": {
                "lines_of_code": self.size.lines_of_code,
                "source_lines_of_code": self.size.source_lines_of_code,
                "comment_lines": self.size.comment_lines,
                "blank_lines": self.size.blank_lines,
                "functions_count": self.size.functions_count,
                "classes_count": self.size.classes_count,
                "methods_count": self.size.methods_count,
                "variables_count": self.size.variables_count
            },
            "quality": {
                "comment_ratio": self.quality.comment_ratio,
                "docstring_coverage": self.quality.docstring_coverage,
                "naming_consistency": self.quality.naming_consistency,
                "code_duplication": self.quality.code_duplication,
                "test_coverage": self.quality.test_coverage
            },
            "maintainability_index": self.maintainability_index,
            "technical_debt_ratio": self.technical_debt_ratio,
            "metadata": self.metadata
        }


class ComplexityAnalyzer:
    """
    Analyzes code complexity using various metrics
    
    Calculates:
    - Cyclomatic complexity
    - Cognitive complexity
    - Nesting depth
    - Halstead metrics
    - Maintainability index
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ccw.analysis.complexity")
    
    def analyze(self, parse_result: ParseResult) -> CodeMetrics:
        """Analyze code and calculate comprehensive metrics"""
        metrics = CodeMetrics(
            file_path=parse_result.file_path,
            language=parse_result.language
        )
        
        try:
            if parse_result.language == LanguageType.PYTHON:
                self._analyze_python(parse_result, metrics)
            elif parse_result.language == LanguageType.JAVASCRIPT:
                self._analyze_javascript(parse_result, metrics)
            elif parse_result.language == LanguageType.JAVA:
                self._analyze_java(parse_result, metrics)
            else:
                self._analyze_generic(parse_result, metrics)
            
            # Calculate derived metrics
            self._calculate_maintainability_index(metrics)
            self._calculate_technical_debt(metrics)
            
        except Exception as e:
            self.logger.error(f"Metrics analysis failed for {parse_result.file_path}: {e}")
        
        return metrics
    
    def _analyze_python(self, parse_result: ParseResult, metrics: CodeMetrics):
        """Analyze Python code metrics"""
        content = parse_result.content
        lines = content.split('\n')
        
        # Size metrics
        metrics.size.lines_of_code = len(lines)
        metrics.size.blank_lines = len([line for line in lines if not line.strip()])
        metrics.size.comment_lines = len([line for line in lines if line.strip().startswith('#')])
        metrics.size.source_lines_of_code = metrics.size.lines_of_code - metrics.size.blank_lines - metrics.size.comment_lines
        
        metrics.size.functions_count = len(parse_result.functions)
        metrics.size.classes_count = len(parse_result.classes)
        metrics.size.variables_count = len(parse_result.variables)
        
        # Try to parse with AST for detailed analysis
        try:
            tree = ast.parse(content)
            self._analyze_python_ast(tree, metrics)
        except SyntaxError:
            # Fallback to regex-based analysis
            self._analyze_python_regex(content, metrics)
        
        # Quality metrics
        self._calculate_python_quality_metrics(content, metrics)
    
    def _analyze_python_ast(self, tree: ast.AST, metrics: CodeMetrics):
        """Analyze Python AST for detailed metrics"""
        complexity = 0
        cognitive_complexity = 0
        max_nesting = 0
        
        # Halstead metrics
        operators = set()
        operands = set()
        operator_count = 0
        operand_count = 0
        
        def analyze_node(node, nesting_level=0):
            nonlocal complexity, cognitive_complexity, max_nesting
            nonlocal operator_count, operand_count
            
            max_nesting = max(max_nesting, nesting_level)
            
            # Cyclomatic complexity
            if isinstance(node, (ast.If, ast.For, ast.While, ast.ExceptHandler, 
                               ast.With, ast.Assert, ast.AsyncWith, ast.AsyncFor)):
                complexity += 1
                cognitive_complexity += 1 + nesting_level  # Cognitive complexity considers nesting
            
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
                cognitive_complexity += len(node.values) - 1
            
            # Halstead metrics
            if isinstance(node, ast.BinOp):
                operators.add(type(node.op).__name__)
                operator_count += 1
            elif isinstance(node, ast.Name):
                operands.add(node.id)
                operand_count += 1
            elif isinstance(node, ast.Constant):
                operands.add(str(node.value))
                operand_count += 1
            
            # Recurse with appropriate nesting level
            next_nesting = nesting_level
            if isinstance(node, (ast.If, ast.For, ast.While, ast.With, ast.AsyncWith, 
                               ast.AsyncFor, ast.FunctionDef, ast.AsyncFunctionDef, 
                               ast.ClassDef)):
                next_nesting += 1
            
            for child in ast.iter_child_nodes(node):
                analyze_node(child, next_nesting)
        
        # Start analysis
        complexity = 1  # Base complexity
        analyze_node(tree)
        
        metrics.complexity.cyclomatic_complexity = complexity
        metrics.complexity.cognitive_complexity = cognitive_complexity
        metrics.complexity.nesting_depth = max_nesting
        
        # Calculate Halstead metrics
        if operators and operands:
            n1 = len(operators)  # Unique operators
            n2 = len(operands)   # Unique operands
            N1 = operator_count  # Total operators
            N2 = operand_count   # Total operands
            
            vocabulary = n1 + n2
            length = N1 + N2
            
            if n2 > 0:
                metrics.complexity.halstead_volume = length * (vocabulary.bit_length() if vocabulary > 0 else 0)
                metrics.complexity.halstead_difficulty = (n1 / 2) * (N2 / n2) if n2 > 0 else 0
                metrics.complexity.halstead_effort = metrics.complexity.halstead_difficulty * metrics.complexity.halstead_volume
    
    def _analyze_python_regex(self, content: str, metrics: CodeMetrics):
        """Fallback regex-based analysis for Python"""
        # Basic complexity estimation
        complexity = 1  # Base complexity
        complexity += content.count('if ')
        complexity += content.count('elif ')
        complexity += content.count('for ')
        complexity += content.count('while ')
        complexity += content.count('except ')
        complexity += content.count('and ')
        complexity += content.count('or ')
        
        metrics.complexity.cyclomatic_complexity = complexity
        metrics.complexity.cognitive_complexity = complexity  # Approximation
    
    def _calculate_python_quality_metrics(self, content: str, metrics: CodeMetrics):
        """Calculate Python-specific quality metrics"""
        lines = content.split('\n')
        
        # Comment ratio
        if metrics.size.source_lines_of_code > 0:
            metrics.quality.comment_ratio = metrics.size.comment_lines / metrics.size.source_lines_of_code
        
        # Docstring coverage (simplified)
        docstring_count = content.count('"""') // 2 + content.count("'''") // 2
        total_definitions = metrics.size.functions_count + metrics.size.classes_count
        if total_definitions > 0:
            metrics.quality.docstring_coverage = min(docstring_count / total_definitions, 1.0)
        
        # Naming consistency (simplified check for snake_case)
        function_names = re.findall(r'def\s+(\w+)', content)
        variable_names = re.findall(r'^(\w+)\s*=', content, re.MULTILINE)
        
        all_names = function_names + variable_names
        if all_names:
            snake_case_count = sum(1 for name in all_names if self._is_snake_case(name))
            metrics.quality.naming_consistency = snake_case_count / len(all_names)
    
    def _analyze_javascript(self, parse_result: ParseResult, metrics: CodeMetrics):
        """Analyze JavaScript code metrics"""
        content = parse_result.content
        lines = content.split('\n')
        
        # Size metrics
        metrics.size.lines_of_code = len(lines)
        metrics.size.blank_lines = len([line for line in lines if not line.strip()])
        metrics.size.comment_lines = len([line for line in lines 
                                        if line.strip().startswith('//') or 
                                           line.strip().startswith('/*')])
        metrics.size.source_lines_of_code = (metrics.size.lines_of_code - 
                                           metrics.size.blank_lines - 
                                           metrics.size.comment_lines)
        
        metrics.size.functions_count = len(parse_result.functions)
        metrics.size.classes_count = len(parse_result.classes)
        
        # Basic complexity estimation
        complexity = 1
        complexity += content.count('if ')
        complexity += content.count('else if')
        complexity += content.count('for ')
        complexity += content.count('while ')
        complexity += content.count('switch ')
        complexity += content.count('case ')
        complexity += content.count('catch ')
        complexity += content.count('&&')
        complexity += content.count('||')
        
        metrics.complexity.cyclomatic_complexity = complexity
        metrics.complexity.cognitive_complexity = complexity
        
        # Quality metrics
        if metrics.size.source_lines_of_code > 0:
            metrics.quality.comment_ratio = metrics.size.comment_lines / metrics.size.source_lines_of_code
    
    def _analyze_java(self, parse_result: ParseResult, metrics: CodeMetrics):
        """Analyze Java code metrics"""
        content = parse_result.content
        lines = content.split('\n')
        
        # Size metrics
        metrics.size.lines_of_code = len(lines)
        metrics.size.blank_lines = len([line for line in lines if not line.strip()])
        metrics.size.comment_lines = len([line for line in lines 
                                        if line.strip().startswith('//') or 
                                           line.strip().startswith('/*')])
        metrics.size.source_lines_of_code = (metrics.size.lines_of_code - 
                                           metrics.size.blank_lines - 
                                           metrics.size.comment_lines)
        
        metrics.size.functions_count = len(parse_result.functions)
        metrics.size.classes_count = len(parse_result.classes)
        
        # Basic complexity estimation
        complexity = 1
        complexity += content.count('if ')
        complexity += content.count('else if')
        complexity += content.count('for ')
        complexity += content.count('while ')
        complexity += content.count('switch ')
        complexity += content.count('case ')
        complexity += content.count('catch ')
        complexity += content.count('&&')
        complexity += content.count('||')
        
        metrics.complexity.cyclomatic_complexity = complexity
        metrics.complexity.cognitive_complexity = complexity
        
        # Quality metrics
        if metrics.size.source_lines_of_code > 0:
            metrics.quality.comment_ratio = metrics.size.comment_lines / metrics.size.source_lines_of_code
    
    def _analyze_generic(self, parse_result: ParseResult, metrics: CodeMetrics):
        """Generic analysis for unsupported languages"""
        content = parse_result.content
        lines = content.split('\n')
        
        # Basic size metrics
        metrics.size.lines_of_code = len(lines)
        metrics.size.blank_lines = len([line for line in lines if not line.strip()])
        metrics.size.source_lines_of_code = metrics.size.lines_of_code - metrics.size.blank_lines
        
        # Basic complexity estimation
        complexity = 1
        complexity += content.count('if')
        complexity += content.count('for')
        complexity += content.count('while')
        
        metrics.complexity.cyclomatic_complexity = complexity
    
    def _calculate_maintainability_index(self, metrics: CodeMetrics):
        """Calculate maintainability index"""
        # Simplified maintainability index calculation
        # MI = 171 - 5.2 * ln(HV) - 0.23 * CC - 16.2 * ln(LOC)
        # Where HV = Halstead Volume, CC = Cyclomatic Complexity, LOC = Lines of Code
        
        import math
        
        hv = max(metrics.complexity.halstead_volume, 1)
        cc = max(metrics.complexity.cyclomatic_complexity, 1)
        loc = max(metrics.size.source_lines_of_code, 1)
        
        try:
            mi = 171 - 5.2 * math.log(hv) - 0.23 * cc - 16.2 * math.log(loc)
            metrics.maintainability_index = max(0, min(100, mi))  # Clamp to 0-100
        except (ValueError, ZeroDivisionError):
            metrics.maintainability_index = 50  # Default value
    
    def _calculate_technical_debt(self, metrics: CodeMetrics):
        """Calculate technical debt ratio"""
        # Simplified technical debt calculation based on various factors
        debt_factors = []
        
        # High complexity
        if metrics.complexity.cyclomatic_complexity > 10:
            debt_factors.append(0.2)
        
        # Low comment ratio
        if metrics.quality.comment_ratio < 0.1:
            debt_factors.append(0.1)
        
        # Large functions (estimated)
        if metrics.size.functions_count > 0:
            avg_function_size = metrics.size.source_lines_of_code / metrics.size.functions_count
            if avg_function_size > 50:
                debt_factors.append(0.15)
        
        # Deep nesting
        if metrics.complexity.nesting_depth > 4:
            debt_factors.append(0.1)
        
        metrics.technical_debt_ratio = min(sum(debt_factors), 1.0)
    
    def _is_snake_case(self, name: str) -> bool:
        """Check if a name follows snake_case convention"""
        return re.match(r'^[a-z_][a-z0-9_]*$', name) is not None
    
    def calculate_workspace_metrics(self, file_metrics: List[CodeMetrics]) -> Dict[str, Any]:
        """Calculate aggregated metrics for entire workspace"""
        if not file_metrics:
            return {}
        
        total_loc = sum(m.size.lines_of_code for m in file_metrics)
        total_sloc = sum(m.size.source_lines_of_code for m in file_metrics)
        total_functions = sum(m.size.functions_count for m in file_metrics)
        total_classes = sum(m.size.classes_count for m in file_metrics)
        
        avg_complexity = sum(m.complexity.cyclomatic_complexity for m in file_metrics) / len(file_metrics)
        avg_maintainability = sum(m.maintainability_index for m in file_metrics) / len(file_metrics)
        avg_comment_ratio = sum(m.quality.comment_ratio for m in file_metrics) / len(file_metrics)
        
        # Find files with highest complexity
        high_complexity_files = sorted(
            [(m.file_path, m.complexity.cyclomatic_complexity) for m in file_metrics],
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        # Find files with lowest maintainability
        low_maintainability_files = sorted(
            [(m.file_path, m.maintainability_index) for m in file_metrics],
            key=lambda x: x[1]
        )[:5]
        
        return {
            "total_files": len(file_metrics),
            "total_lines_of_code": total_loc,
            "total_source_lines_of_code": total_sloc,
            "total_functions": total_functions,
            "total_classes": total_classes,
            "average_complexity": avg_complexity,
            "average_maintainability": avg_maintainability,
            "average_comment_ratio": avg_comment_ratio,
            "high_complexity_files": high_complexity_files,
            "low_maintainability_files": low_maintainability_files,
            "language_distribution": self._calculate_language_distribution(file_metrics)
        }
    
    def _calculate_language_distribution(self, file_metrics: List[CodeMetrics]) -> Dict[str, int]:
        """Calculate distribution of programming languages"""
        distribution = {}
        for metrics in file_metrics:
            lang = metrics.language.value
            distribution[lang] = distribution.get(lang, 0) + 1
        return distribution
