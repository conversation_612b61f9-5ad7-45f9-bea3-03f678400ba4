"""
Dependency Graph - Code dependency analysis and visualization

Builds and analyzes dependency relationships between files, modules,
functions, and classes to understand code architecture and coupling.
"""

import logging
from collections import defaultdict, deque
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from enum import Enum

from .parser import Pa<PERSON><PERSON><PERSON>ult, LanguageType

logger = logging.getLogger(__name__)


class DependencyType(Enum):
    """Types of dependencies between code elements"""
    IMPORT = "import"
    INHERITANCE = "inheritance"
    COMPOSITION = "composition"
    FUNCTION_CALL = "function_call"
    METHOD_CALL = "method_call"
    VARIABLE_REFERENCE = "variable_reference"
    TYPE_ANNOTATION = "type_annotation"
    DECORATOR = "decorator"
    UNKNOWN = "unknown"


@dataclass
class DependencyEdge:
    """Represents a dependency relationship between two code elements"""
    source: str
    target: str
    dependency_type: DependencyType
    line_number: Optional[int] = None
    context: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __hash__(self):
        return hash((self.source, self.target, self.dependency_type))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "source": self.source,
            "target": self.target,
            "type": self.dependency_type.value,
            "line_number": self.line_number,
            "context": self.context,
            "metadata": self.metadata
        }


@dataclass
class DependencyNode:
    """Represents a node in the dependency graph"""
    identifier: str
    node_type: str  # file, module, class, function, variable
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    incoming_edges: Set[DependencyEdge] = field(default_factory=set)
    outgoing_edges: Set[DependencyEdge] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_incoming_edge(self, edge: DependencyEdge):
        """Add an incoming dependency edge"""
        self.incoming_edges.add(edge)
    
    def add_outgoing_edge(self, edge: DependencyEdge):
        """Add an outgoing dependency edge"""
        self.outgoing_edges.add(edge)
    
    def get_dependencies(self) -> Set[str]:
        """Get all dependencies (targets of outgoing edges)"""
        return {edge.target for edge in self.outgoing_edges}
    
    def get_dependents(self) -> Set[str]:
        """Get all dependents (sources of incoming edges)"""
        return {edge.source for edge in self.incoming_edges}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "identifier": self.identifier,
            "type": self.node_type,
            "file_path": self.file_path,
            "line_number": self.line_number,
            "dependencies": list(self.get_dependencies()),
            "dependents": list(self.get_dependents()),
            "metadata": self.metadata
        }


class DependencyGraph:
    """
    Represents and analyzes dependency relationships in code
    
    Provides functionality to:
    - Build dependency graphs from parsed code
    - Detect circular dependencies
    - Calculate coupling metrics
    - Find dependency paths
    - Analyze architectural patterns
    """
    
    def __init__(self):
        self.nodes: Dict[str, DependencyNode] = {}
        self.edges: Set[DependencyEdge] = set()
        self.logger = logging.getLogger("ccw.analysis.dependency_graph")
    
    def add_node(self, identifier: str, node_type: str, file_path: Optional[str] = None,
                 line_number: Optional[int] = None, **metadata) -> DependencyNode:
        """Add a node to the dependency graph"""
        if identifier not in self.nodes:
            self.nodes[identifier] = DependencyNode(
                identifier=identifier,
                node_type=node_type,
                file_path=file_path,
                line_number=line_number,
                metadata=metadata
            )
        return self.nodes[identifier]
    
    def add_edge(self, source: str, target: str, dependency_type: DependencyType,
                 line_number: Optional[int] = None, context: Optional[str] = None,
                 **metadata) -> DependencyEdge:
        """Add a dependency edge between two nodes"""
        # Ensure nodes exist
        if source not in self.nodes:
            self.add_node(source, "unknown")
        if target not in self.nodes:
            self.add_node(target, "unknown")
        
        edge = DependencyEdge(
            source=source,
            target=target,
            dependency_type=dependency_type,
            line_number=line_number,
            context=context,
            metadata=metadata
        )
        
        self.edges.add(edge)
        self.nodes[source].add_outgoing_edge(edge)
        self.nodes[target].add_incoming_edge(edge)
        
        return edge
    
    def get_node(self, identifier: str) -> Optional[DependencyNode]:
        """Get a node by identifier"""
        return self.nodes.get(identifier)
    
    def get_dependencies(self, identifier: str) -> Set[str]:
        """Get all dependencies of a node"""
        node = self.get_node(identifier)
        return node.get_dependencies() if node else set()
    
    def get_dependents(self, identifier: str) -> Set[str]:
        """Get all dependents of a node"""
        node = self.get_node(identifier)
        return node.get_dependents() if node else set()
    
    def find_circular_dependencies(self) -> List[List[str]]:
        """Find circular dependencies in the graph"""
        cycles = []
        visited = set()
        rec_stack = set()
        
        def dfs(node_id: str, path: List[str]) -> bool:
            if node_id in rec_stack:
                # Found a cycle
                cycle_start = path.index(node_id)
                cycle = path[cycle_start:] + [node_id]
                cycles.append(cycle)
                return True
            
            if node_id in visited:
                return False
            
            visited.add(node_id)
            rec_stack.add(node_id)
            path.append(node_id)
            
            # Visit all dependencies
            dependencies = self.get_dependencies(node_id)
            for dep in dependencies:
                if dfs(dep, path.copy()):
                    pass  # Continue to find all cycles
            
            rec_stack.remove(node_id)
            return False
        
        # Check all nodes
        for node_id in self.nodes:
            if node_id not in visited:
                dfs(node_id, [])
        
        return cycles
    
    def find_dependency_path(self, source: str, target: str) -> Optional[List[str]]:
        """Find a dependency path between two nodes using BFS"""
        if source not in self.nodes or target not in self.nodes:
            return None
        
        if source == target:
            return [source]
        
        queue = deque([(source, [source])])
        visited = {source}
        
        while queue:
            current, path = queue.popleft()
            
            for dependency in self.get_dependencies(current):
                if dependency == target:
                    return path + [dependency]
                
                if dependency not in visited:
                    visited.add(dependency)
                    queue.append((dependency, path + [dependency]))
        
        return None
    
    def calculate_coupling_metrics(self) -> Dict[str, Any]:
        """Calculate coupling metrics for the dependency graph"""
        metrics = {
            "total_nodes": len(self.nodes),
            "total_edges": len(self.edges),
            "average_dependencies": 0.0,
            "average_dependents": 0.0,
            "max_dependencies": 0,
            "max_dependents": 0,
            "coupling_ratio": 0.0,
            "circular_dependencies": len(self.find_circular_dependencies())
        }
        
        if self.nodes:
            dependencies_counts = [len(node.get_dependencies()) for node in self.nodes.values()]
            dependents_counts = [len(node.get_dependents()) for node in self.nodes.values()]
            
            metrics["average_dependencies"] = sum(dependencies_counts) / len(dependencies_counts)
            metrics["average_dependents"] = sum(dependents_counts) / len(dependents_counts)
            metrics["max_dependencies"] = max(dependencies_counts)
            metrics["max_dependents"] = max(dependents_counts)
            
            # Coupling ratio: actual edges / possible edges
            max_possible_edges = len(self.nodes) * (len(self.nodes) - 1)
            if max_possible_edges > 0:
                metrics["coupling_ratio"] = len(self.edges) / max_possible_edges
        
        return metrics
    
    def get_highly_coupled_nodes(self, threshold: int = 5) -> List[Tuple[str, int]]:
        """Get nodes with high coupling (many dependencies or dependents)"""
        highly_coupled = []
        
        for node_id, node in self.nodes.items():
            total_coupling = len(node.get_dependencies()) + len(node.get_dependents())
            if total_coupling >= threshold:
                highly_coupled.append((node_id, total_coupling))
        
        return sorted(highly_coupled, key=lambda x: x[1], reverse=True)
    
    def get_leaf_nodes(self) -> List[str]:
        """Get nodes with no dependencies (leaf nodes)"""
        return [node_id for node_id, node in self.nodes.items() 
                if not node.get_dependencies()]
    
    def get_root_nodes(self) -> List[str]:
        """Get nodes with no dependents (root nodes)"""
        return [node_id for node_id, node in self.nodes.items() 
                if not node.get_dependents()]
    
    def get_subgraph(self, node_ids: Set[str]) -> 'DependencyGraph':
        """Extract a subgraph containing only specified nodes"""
        subgraph = DependencyGraph()
        
        # Add nodes
        for node_id in node_ids:
            if node_id in self.nodes:
                node = self.nodes[node_id]
                subgraph.add_node(
                    node.identifier,
                    node.node_type,
                    node.file_path,
                    node.line_number,
                    **node.metadata
                )
        
        # Add edges between included nodes
        for edge in self.edges:
            if edge.source in node_ids and edge.target in node_ids:
                subgraph.add_edge(
                    edge.source,
                    edge.target,
                    edge.dependency_type,
                    edge.line_number,
                    edge.context,
                    **edge.metadata
                )
        
        return subgraph
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert graph to dictionary representation"""
        return {
            "nodes": {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            "edges": [edge.to_dict() for edge in self.edges],
            "metrics": self.calculate_coupling_metrics()
        }
    
    def export_dot(self) -> str:
        """Export graph in DOT format for visualization"""
        lines = ["digraph DependencyGraph {"]
        lines.append("  rankdir=LR;")
        lines.append("  node [shape=box];")
        
        # Add nodes
        for node_id, node in self.nodes.items():
            label = f"{node_id}\\n({node.node_type})"
            lines.append(f'  "{node_id}" [label="{label}"];')
        
        # Add edges
        for edge in self.edges:
            style = self._get_edge_style(edge.dependency_type)
            lines.append(f'  "{edge.source}" -> "{edge.target}" [label="{edge.dependency_type.value}" {style}];')
        
        lines.append("}")
        return "\n".join(lines)
    
    def _get_edge_style(self, dependency_type: DependencyType) -> str:
        """Get DOT style for edge based on dependency type"""
        styles = {
            DependencyType.IMPORT: 'color=blue',
            DependencyType.INHERITANCE: 'color=red style=bold',
            DependencyType.COMPOSITION: 'color=green',
            DependencyType.FUNCTION_CALL: 'color=orange style=dashed',
            DependencyType.METHOD_CALL: 'color=purple style=dashed'
        }
        return styles.get(dependency_type, 'color=black')


class DependencyAnalyzer:
    """
    Analyzes code to build dependency graphs
    
    Extracts dependency relationships from parsed code and builds
    comprehensive dependency graphs for analysis.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ccw.analysis.dependency_analyzer")
    
    def analyze_file(self, parse_result: ParseResult) -> DependencyGraph:
        """Analyze a single file and build its dependency graph"""
        graph = DependencyGraph()
        
        try:
            file_path = parse_result.file_path
            file_node_id = f"file:{file_path}"
            
            # Add file node
            graph.add_node(file_node_id, "file", file_path=file_path)
            
            # Add import dependencies
            for import_stmt in parse_result.imports:
                import_node_id = f"module:{import_stmt}"
                graph.add_node(import_node_id, "module")
                graph.add_edge(file_node_id, import_node_id, DependencyType.IMPORT)
            
            # Add function nodes and their dependencies
            for func in parse_result.functions:
                func_node_id = f"function:{file_path}:{func['name']}"
                graph.add_node(
                    func_node_id, 
                    "function", 
                    file_path=file_path,
                    line_number=func.get('line')
                )
                graph.add_edge(file_node_id, func_node_id, DependencyType.COMPOSITION)
                
                # Add function call dependencies
                if 'calls' in func:
                    for call in func['calls']:
                        call_node_id = f"function:{call}"
                        graph.add_node(call_node_id, "function")
                        graph.add_edge(func_node_id, call_node_id, DependencyType.FUNCTION_CALL)
            
            # Add class nodes and their dependencies
            for cls in parse_result.classes:
                class_node_id = f"class:{file_path}:{cls['name']}"
                graph.add_node(
                    class_node_id,
                    "class",
                    file_path=file_path,
                    line_number=cls.get('line')
                )
                graph.add_edge(file_node_id, class_node_id, DependencyType.COMPOSITION)
                
                # Add inheritance dependencies
                if 'inheritance' in cls and cls['inheritance']:
                    parent_node_id = f"class:{cls['inheritance']}"
                    graph.add_node(parent_node_id, "class")
                    graph.add_edge(class_node_id, parent_node_id, DependencyType.INHERITANCE)
            
        except Exception as e:
            self.logger.error(f"Failed to analyze dependencies for {parse_result.file_path}: {e}")
        
        return graph
    
    def analyze_workspace(self, parse_results: List[ParseResult]) -> DependencyGraph:
        """Analyze multiple files and build a comprehensive dependency graph"""
        workspace_graph = DependencyGraph()
        
        # First pass: analyze individual files
        file_graphs = {}
        for parse_result in parse_results:
            file_graph = self.analyze_file(parse_result)
            file_graphs[parse_result.file_path] = file_graph
            
            # Merge into workspace graph
            self._merge_graphs(workspace_graph, file_graph)
        
        # Second pass: resolve cross-file dependencies
        self._resolve_cross_file_dependencies(workspace_graph, parse_results)
        
        return workspace_graph
    
    def _merge_graphs(self, target_graph: DependencyGraph, source_graph: DependencyGraph):
        """Merge source graph into target graph"""
        # Add all nodes
        for node_id, node in source_graph.nodes.items():
            if node_id not in target_graph.nodes:
                target_graph.add_node(
                    node.identifier,
                    node.node_type,
                    node.file_path,
                    node.line_number,
                    **node.metadata
                )
        
        # Add all edges
        for edge in source_graph.edges:
            target_graph.add_edge(
                edge.source,
                edge.target,
                edge.dependency_type,
                edge.line_number,
                edge.context,
                **edge.metadata
            )
    
    def _resolve_cross_file_dependencies(self, graph: DependencyGraph, 
                                       parse_results: List[ParseResult]):
        """Resolve dependencies between different files"""
        # Build symbol tables
        symbol_table = self._build_symbol_table(parse_results)
        
        # Resolve function calls and class references
        for parse_result in parse_results:
            file_path = parse_result.file_path
            
            # Resolve function calls
            for func in parse_result.functions:
                func_node_id = f"function:{file_path}:{func['name']}"
                
                if 'calls' in func:
                    for call in func['calls']:
                        # Try to find the actual definition
                        if call in symbol_table:
                            target_file, target_type = symbol_table[call]
                            target_node_id = f"{target_type}:{target_file}:{call}"
                            
                            # Update the edge to point to the actual definition
                            graph.add_edge(
                                func_node_id,
                                target_node_id,
                                DependencyType.FUNCTION_CALL
                            )
    
    def _build_symbol_table(self, parse_results: List[ParseResult]) -> Dict[str, Tuple[str, str]]:
        """Build a symbol table mapping symbol names to their definitions"""
        symbol_table = {}
        
        for parse_result in parse_results:
            file_path = parse_result.file_path
            
            # Add functions
            for func in parse_result.functions:
                symbol_table[func['name']] = (file_path, "function")
            
            # Add classes
            for cls in parse_result.classes:
                symbol_table[cls['name']] = (file_path, "class")
        
        return symbol_table
