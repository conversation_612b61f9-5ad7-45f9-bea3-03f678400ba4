"""
AST Analyzer - Abstract Syntax Tree analysis and manipulation

Provides deep analysis of code structure, control flow, and semantic patterns
using Abstract Syntax Trees from various parsing backends.
"""

import ast
import logging
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set, Union
from enum import Enum

from .parser import ParseResult, LanguageType

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Types of AST nodes"""
    MODULE = "module"
    CLASS = "class"
    FUNCTION = "function"
    METHOD = "method"
    VARIABLE = "variable"
    IMPORT = "import"
    CALL = "call"
    ASSIGNMENT = "assignment"
    CONDITIONAL = "conditional"
    LOOP = "loop"
    RETURN = "return"
    EXPRESSION = "expression"
    STATEMENT = "statement"
    COMMENT = "comment"
    UNKNOWN = "unknown"


@dataclass
class ASTNode:
    """Represents a node in the Abstract Syntax Tree"""
    node_type: NodeType
    name: str
    line_start: int
    line_end: int
    column_start: int = 0
    column_end: int = 0
    parent: Optional['ASTNode'] = None
    children: List['ASTNode'] = field(default_factory=list)
    attributes: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_child(self, child: 'ASTNode'):
        """Add a child node"""
        child.parent = self
        self.children.append(child)
    
    def get_children_by_type(self, node_type: NodeType) -> List['ASTNode']:
        """Get all children of a specific type"""
        return [child for child in self.children if child.node_type == node_type]
    
    def find_descendants_by_type(self, node_type: NodeType) -> List['ASTNode']:
        """Find all descendants of a specific type"""
        descendants = []
        
        def traverse(node):
            if node.node_type == node_type:
                descendants.append(node)
            for child in node.children:
                traverse(child)
        
        traverse(self)
        return descendants
    
    def get_depth(self) -> int:
        """Get the depth of this node in the tree"""
        depth = 0
        current = self.parent
        while current:
            depth += 1
            current = current.parent
        return depth
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert node to dictionary representation"""
        return {
            "type": self.node_type.value,
            "name": self.name,
            "line_start": self.line_start,
            "line_end": self.line_end,
            "column_start": self.column_start,
            "column_end": self.column_end,
            "attributes": self.attributes,
            "metadata": self.metadata,
            "children": [child.to_dict() for child in self.children]
        }


@dataclass
class ControlFlowInfo:
    """Information about control flow in code"""
    complexity: int = 0
    max_depth: int = 0
    branches: List[Dict[str, Any]] = field(default_factory=list)
    loops: List[Dict[str, Any]] = field(default_factory=list)
    conditions: List[Dict[str, Any]] = field(default_factory=list)
    returns: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class FunctionInfo:
    """Detailed information about a function"""
    name: str
    parameters: List[str]
    return_type: Optional[str] = None
    docstring: Optional[str] = None
    decorators: List[str] = field(default_factory=list)
    calls: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    complexity: int = 0
    line_count: int = 0
    control_flow: ControlFlowInfo = field(default_factory=ControlFlowInfo)


@dataclass
class ClassInfo:
    """Detailed information about a class"""
    name: str
    base_classes: List[str] = field(default_factory=list)
    methods: List[FunctionInfo] = field(default_factory=list)
    attributes: List[str] = field(default_factory=list)
    decorators: List[str] = field(default_factory=list)
    docstring: Optional[str] = None
    line_count: int = 0


class ASTAnalyzer:
    """
    Analyzes Abstract Syntax Trees to extract semantic information
    
    Provides deep analysis of code structure including:
    - Function and class extraction
    - Control flow analysis
    - Complexity metrics
    - Symbol relationships
    - Code patterns
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ccw.analysis.ast")
    
    def analyze(self, parse_result: ParseResult) -> Dict[str, Any]:
        """
        Perform comprehensive AST analysis
        
        Args:
            parse_result: Result from code parsing
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            "file_path": parse_result.file_path,
            "language": parse_result.language.value,
            "ast_tree": None,
            "functions": [],
            "classes": [],
            "imports": [],
            "variables": [],
            "control_flow": ControlFlowInfo(),
            "complexity_metrics": {},
            "patterns": [],
            "errors": []
        }
        
        try:
            if parse_result.language == LanguageType.PYTHON:
                analysis = self._analyze_python(parse_result, analysis)
            elif parse_result.language == LanguageType.JAVASCRIPT:
                analysis = self._analyze_javascript(parse_result, analysis)
            elif parse_result.language == LanguageType.JAVA:
                analysis = self._analyze_java(parse_result, analysis)
            else:
                analysis = self._analyze_generic(parse_result, analysis)
                
        except Exception as e:
            error_msg = f"AST analysis failed: {e}"
            analysis["errors"].append(error_msg)
            self.logger.error(error_msg)
        
        return analysis
    
    def _analyze_python(self, parse_result: ParseResult, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze Python code using built-in AST module"""
        try:
            # Parse with Python's AST module
            tree = ast.parse(parse_result.content)
            analysis["ast_tree"] = self._convert_python_ast(tree)
            
            # Extract functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = self._extract_python_function(node, parse_result.content)
                    analysis["functions"].append(func_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = self._extract_python_class(node, parse_result.content)
                    analysis["classes"].append(class_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    import_info = self._extract_python_import(node)
                    analysis["imports"].append(import_info)
            
            # Calculate complexity metrics
            analysis["complexity_metrics"] = self._calculate_python_complexity(tree)
            
            # Detect patterns
            analysis["patterns"] = self._detect_python_patterns(tree)
            
        except SyntaxError as e:
            analysis["errors"].append(f"Python syntax error: {e}")
        except Exception as e:
            analysis["errors"].append(f"Python analysis error: {e}")
        
        return analysis
    
    def _convert_python_ast(self, tree: ast.AST) -> ASTNode:
        """Convert Python AST to our ASTNode format"""
        def convert_node(node: ast.AST, parent: Optional[ASTNode] = None) -> ASTNode:
            node_type = self._get_python_node_type(node)
            name = getattr(node, 'name', str(type(node).__name__))
            
            ast_node = ASTNode(
                node_type=node_type,
                name=name,
                line_start=getattr(node, 'lineno', 0),
                line_end=getattr(node, 'end_lineno', getattr(node, 'lineno', 0)),
                column_start=getattr(node, 'col_offset', 0),
                column_end=getattr(node, 'end_col_offset', 0),
                parent=parent
            )
            
            # Add node-specific attributes
            if isinstance(node, ast.FunctionDef):
                ast_node.attributes['parameters'] = [arg.arg for arg in node.args.args]
                ast_node.attributes['decorators'] = [self._get_decorator_name(d) for d in node.decorator_list]
            
            elif isinstance(node, ast.ClassDef):
                ast_node.attributes['bases'] = [self._get_base_name(base) for base in node.bases]
                ast_node.attributes['decorators'] = [self._get_decorator_name(d) for d in node.decorator_list]
            
            # Convert children
            for child in ast.iter_child_nodes(node):
                child_node = convert_node(child, ast_node)
                ast_node.add_child(child_node)
            
            return ast_node
        
        return convert_node(tree)
    
    def _get_python_node_type(self, node: ast.AST) -> NodeType:
        """Map Python AST node types to our NodeType enum"""
        type_map = {
            ast.Module: NodeType.MODULE,
            ast.ClassDef: NodeType.CLASS,
            ast.FunctionDef: NodeType.FUNCTION,
            ast.AsyncFunctionDef: NodeType.FUNCTION,
            ast.Import: NodeType.IMPORT,
            ast.ImportFrom: NodeType.IMPORT,
            ast.Assign: NodeType.ASSIGNMENT,
            ast.AugAssign: NodeType.ASSIGNMENT,
            ast.If: NodeType.CONDITIONAL,
            ast.For: NodeType.LOOP,
            ast.While: NodeType.LOOP,
            ast.Return: NodeType.RETURN,
            ast.Call: NodeType.CALL,
            ast.Expr: NodeType.EXPRESSION
        }
        
        return type_map.get(type(node), NodeType.UNKNOWN)
    
    def _extract_python_function(self, node: ast.FunctionDef, content: str) -> FunctionInfo:
        """Extract detailed information about a Python function"""
        func_info = FunctionInfo(
            name=node.name,
            parameters=[arg.arg for arg in node.args.args],
            decorators=[self._get_decorator_name(d) for d in node.decorator_list],
            line_count=node.end_lineno - node.lineno + 1 if node.end_lineno else 1
        )
        
        # Extract docstring
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            func_info.docstring = node.body[0].value.value
        
        # Extract function calls
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                call_name = self._get_call_name(child)
                if call_name:
                    func_info.calls.append(call_name)
        
        # Calculate complexity
        func_info.complexity = self._calculate_function_complexity(node)
        func_info.control_flow = self._analyze_control_flow(node)
        
        return func_info
    
    def _extract_python_class(self, node: ast.ClassDef, content: str) -> ClassInfo:
        """Extract detailed information about a Python class"""
        class_info = ClassInfo(
            name=node.name,
            base_classes=[self._get_base_name(base) for base in node.bases],
            decorators=[self._get_decorator_name(d) for d in node.decorator_list],
            line_count=node.end_lineno - node.lineno + 1 if node.end_lineno else 1
        )
        
        # Extract docstring
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            class_info.docstring = node.body[0].value.value
        
        # Extract methods
        for child in node.body:
            if isinstance(child, ast.FunctionDef):
                method_info = self._extract_python_function(child, content)
                method_info.name = child.name  # Ensure method name is set
                class_info.methods.append(method_info)
        
        return class_info
    
    def _extract_python_import(self, node: Union[ast.Import, ast.ImportFrom]) -> Dict[str, Any]:
        """Extract import information"""
        if isinstance(node, ast.Import):
            return {
                "type": "import",
                "modules": [alias.name for alias in node.names],
                "line": node.lineno
            }
        else:  # ImportFrom
            return {
                "type": "from_import",
                "module": node.module,
                "names": [alias.name for alias in node.names],
                "line": node.lineno
            }
    
    def _calculate_python_complexity(self, tree: ast.AST) -> Dict[str, Any]:
        """Calculate complexity metrics for Python code"""
        metrics = {
            "cyclomatic_complexity": 1,  # Base complexity
            "cognitive_complexity": 0,
            "lines_of_code": 0,
            "functions_count": 0,
            "classes_count": 0,
            "max_nesting_depth": 0
        }
        
        # Count different node types and calculate complexity
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.ExceptHandler)):
                metrics["cyclomatic_complexity"] += 1
            elif isinstance(node, ast.FunctionDef):
                metrics["functions_count"] += 1
            elif isinstance(node, ast.ClassDef):
                metrics["classes_count"] += 1
        
        return metrics
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate cyclomatic complexity for a function"""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.ExceptHandler, 
                                ast.With, ast.Assert)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _analyze_control_flow(self, node: ast.FunctionDef) -> ControlFlowInfo:
        """Analyze control flow within a function"""
        control_flow = ControlFlowInfo()
        
        def analyze_node(n, depth=0):
            control_flow.max_depth = max(control_flow.max_depth, depth)
            
            if isinstance(n, ast.If):
                control_flow.conditions.append({
                    "line": n.lineno,
                    "type": "if",
                    "depth": depth
                })
                control_flow.complexity += 1
                
                for child in ast.iter_child_nodes(n):
                    analyze_node(child, depth + 1)
            
            elif isinstance(n, (ast.For, ast.While)):
                control_flow.loops.append({
                    "line": n.lineno,
                    "type": type(n).__name__.lower(),
                    "depth": depth
                })
                control_flow.complexity += 1
                
                for child in ast.iter_child_nodes(n):
                    analyze_node(child, depth + 1)
            
            elif isinstance(n, ast.Return):
                control_flow.returns.append({
                    "line": n.lineno,
                    "depth": depth
                })
            
            else:
                for child in ast.iter_child_nodes(n):
                    analyze_node(child, depth)
        
        analyze_node(node)
        return control_flow
    
    def _detect_python_patterns(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Detect common Python patterns and anti-patterns"""
        patterns = []
        
        # Detect list comprehensions
        for node in ast.walk(tree):
            if isinstance(node, ast.ListComp):
                patterns.append({
                    "type": "list_comprehension",
                    "line": node.lineno,
                    "description": "List comprehension usage"
                })
            
            elif isinstance(node, ast.Lambda):
                patterns.append({
                    "type": "lambda_function",
                    "line": node.lineno,
                    "description": "Lambda function usage"
                })
        
        return patterns
    
    def _analyze_javascript(self, parse_result: ParseResult, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze JavaScript code (fallback to regex-based analysis)"""
        # For now, use the parsed symbols from the parser
        analysis["functions"] = parse_result.functions
        analysis["imports"] = parse_result.imports
        
        # Basic complexity estimation
        content = parse_result.content
        analysis["complexity_metrics"] = {
            "estimated_complexity": content.count('if ') + content.count('for ') + content.count('while '),
            "functions_count": len(parse_result.functions),
            "lines_of_code": len(content.split('\n'))
        }
        
        return analysis
    
    def _analyze_java(self, parse_result: ParseResult, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze Java code (fallback to regex-based analysis)"""
        # Similar to JavaScript analysis
        analysis["functions"] = parse_result.functions
        analysis["classes"] = parse_result.classes
        analysis["imports"] = parse_result.imports
        
        content = parse_result.content
        analysis["complexity_metrics"] = {
            "estimated_complexity": content.count('if ') + content.count('for ') + content.count('while '),
            "classes_count": len(parse_result.classes),
            "methods_count": len(parse_result.functions),
            "lines_of_code": len(content.split('\n'))
        }
        
        return analysis
    
    def _analyze_generic(self, parse_result: ParseResult, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generic analysis for unsupported languages"""
        analysis["functions"] = parse_result.functions
        analysis["classes"] = parse_result.classes
        analysis["imports"] = parse_result.imports
        analysis["variables"] = parse_result.variables
        
        # Basic metrics
        content = parse_result.content
        lines = content.split('\n')
        analysis["complexity_metrics"] = {
            "lines_of_code": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "comment_lines": len([line for line in lines if line.strip().startswith('#') or line.strip().startswith('//')])
        }
        
        return analysis
    
    # Helper methods
    def _get_decorator_name(self, decorator: ast.AST) -> str:
        """Get decorator name from AST node"""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{self._get_attr_name(decorator.value)}.{decorator.attr}"
        else:
            return str(decorator)
    
    def _get_base_name(self, base: ast.AST) -> str:
        """Get base class name from AST node"""
        if isinstance(base, ast.Name):
            return base.id
        elif isinstance(base, ast.Attribute):
            return f"{self._get_attr_name(base.value)}.{base.attr}"
        else:
            return str(base)
    
    def _get_call_name(self, call: ast.Call) -> Optional[str]:
        """Get function call name from AST node"""
        if isinstance(call.func, ast.Name):
            return call.func.id
        elif isinstance(call.func, ast.Attribute):
            return f"{self._get_attr_name(call.func.value)}.{call.func.attr}"
        else:
            return None
    
    def _get_attr_name(self, node: ast.AST) -> str:
        """Get attribute name from AST node"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_attr_name(node.value)}.{node.attr}"
        else:
            return "unknown"
