"""
Code Analysis Package - Multi-language code parsing and analysis

This package provides comprehensive code analysis capabilities including:
- Multi-language parsing (Python, JavaScript, TypeScript, Java, C/C++, etc.)
- Abstract Syntax Tree (AST) analysis
- Dependency graph generation
- Code metrics and complexity analysis
- Symbol extraction and cross-references
"""

from .parser import <PERSON>Parser, LanguageParser
from .ast_analyzer import ASTAnalyzer, ASTNode
from .dependency_graph import DependencyGraph, DependencyAnalyzer
from .metrics import CodeMetrics, ComplexityAnalyzer
from .symbols import SymbolExtractor, Symbol, SymbolType
from .workspace import WorkspaceAnalyzer

__all__ = [
    "CodeParser",
    "LanguageParser", 
    "ASTAnalyzer",
    "ASTNode",
    "DependencyGraph",
    "DependencyAnalyzer",
    "CodeMetrics",
    "ComplexityAnalyzer",
    "SymbolExtractor",
    "Symbol",
    "SymbolType",
    "WorkspaceAnalyzer"
]
