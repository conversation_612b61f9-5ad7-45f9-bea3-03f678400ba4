"""
Workspace Analyzer - Comprehensive workspace-level code analysis

Orchestrates analysis of entire codebases, providing insights into
architecture, quality, dependencies, and patterns across all files.
"""

import logging
import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed

from .parser import <PERSON><PERSON><PERSON><PERSON>, Pa<PERSON><PERSON><PERSON>ult, LanguageType
from .ast_analyzer import ASTAnalyzer
from .dependency_graph import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DependencyGraph
from .metrics import ComplexityAnalyzer, CodeMetrics
from .symbols import SymbolExtractor, Symbol

logger = logging.getLogger(__name__)


@dataclass
class WorkspaceAnalysisResult:
    """Comprehensive workspace analysis results"""
    workspace_path: str
    total_files: int = 0
    analyzed_files: int = 0
    skipped_files: int = 0
    parse_results: List[ParseResult] = field(default_factory=list)
    ast_analyses: List[Dict[str, Any]] = field(default_factory=list)
    code_metrics: List[CodeMetrics] = field(default_factory=list)
    symbols: List[Symbol] = field(default_factory=list)
    dependency_graph: Optional[DependencyGraph] = None
    workspace_metrics: Dict[str, Any] = field(default_factory=dict)
    quality_report: Dict[str, Any] = field(default_factory=dict)
    architecture_insights: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert analysis results to dictionary"""
        return {
            "workspace_path": self.workspace_path,
            "summary": {
                "total_files": self.total_files,
                "analyzed_files": self.analyzed_files,
                "skipped_files": self.skipped_files,
                "languages": self._get_language_summary(),
                "errors_count": len(self.errors)
            },
            "workspace_metrics": self.workspace_metrics,
            "quality_report": self.quality_report,
            "architecture_insights": self.architecture_insights,
            "dependency_graph": self.dependency_graph.to_dict() if self.dependency_graph else None,
            "files": [
                {
                    "file_path": result.file_path,
                    "language": result.language.value,
                    "symbols_count": len([s for s in self.symbols if s.file_path == result.file_path]),
                    "metrics": next((m.to_dict() for m in self.code_metrics if m.file_path == result.file_path), None)
                }
                for result in self.parse_results
            ],
            "errors": self.errors
        }
    
    def _get_language_summary(self) -> Dict[str, int]:
        """Get summary of languages in workspace"""
        languages = {}
        for result in self.parse_results:
            lang = result.language.value
            languages[lang] = languages.get(lang, 0) + 1
        return languages


class WorkspaceAnalyzer:
    """
    Comprehensive workspace-level code analyzer
    
    Provides complete analysis of codebases including:
    - Multi-language parsing and AST analysis
    - Dependency graph construction
    - Code metrics and quality assessment
    - Symbol extraction and cross-referencing
    - Architecture pattern detection
    - Quality and maintainability reporting
    """
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.logger = logging.getLogger("ccw.analysis.workspace")
        
        # Initialize analyzers
        self.parser = CodeParser()
        self.ast_analyzer = ASTAnalyzer()
        self.dependency_analyzer = DependencyAnalyzer()
        self.complexity_analyzer = ComplexityAnalyzer()
        self.symbol_extractor = SymbolExtractor()
        
        # File filters
        self.include_patterns = ['*.py', '*.js', '*.ts', '*.java', '*.c', '*.cpp', '*.cs', '*.go', '*.rs']
        self.exclude_patterns = ['node_modules', '.git', '__pycache__', '.venv', 'venv', 'build', 'dist']
    
    def analyze_workspace(self, workspace_path: str, 
                         include_patterns: Optional[List[str]] = None,
                         exclude_patterns: Optional[List[str]] = None,
                         max_files: Optional[int] = None) -> WorkspaceAnalysisResult:
        """
        Analyze entire workspace
        
        Args:
            workspace_path: Path to workspace root
            include_patterns: File patterns to include
            exclude_patterns: Directory patterns to exclude
            max_files: Maximum number of files to analyze
            
        Returns:
            Comprehensive analysis results
        """
        self.logger.info(f"Starting workspace analysis: {workspace_path}")
        
        # Update patterns if provided
        if include_patterns:
            self.include_patterns = include_patterns
        if exclude_patterns:
            self.exclude_patterns = exclude_patterns
        
        result = WorkspaceAnalysisResult(workspace_path=workspace_path)
        
        try:
            # 1. Discover files
            files = self._discover_files(workspace_path, max_files)
            result.total_files = len(files)
            
            self.logger.info(f"Found {len(files)} files to analyze")
            
            # 2. Parse files (parallel)
            parse_results = self._parse_files_parallel(files)
            result.parse_results = [r for r in parse_results if not r.errors]
            result.analyzed_files = len(result.parse_results)
            result.skipped_files = result.total_files - result.analyzed_files
            
            # Collect parsing errors
            for parse_result in parse_results:
                result.errors.extend(parse_result.errors)
            
            self.logger.info(f"Successfully parsed {result.analyzed_files} files")
            
            # 3. AST Analysis (parallel)
            result.ast_analyses = self._analyze_ast_parallel(result.parse_results)
            
            # 4. Extract symbols
            result.symbols = self._extract_symbols(result.parse_results)
            
            # 5. Build dependency graph
            result.dependency_graph = self.dependency_analyzer.analyze_workspace(result.parse_results)
            
            # 6. Calculate metrics (parallel)
            result.code_metrics = self._calculate_metrics_parallel(result.parse_results)
            
            # 7. Generate workspace-level insights
            result.workspace_metrics = self._calculate_workspace_metrics(result)
            result.quality_report = self._generate_quality_report(result)
            result.architecture_insights = self._analyze_architecture(result)
            
            self.logger.info("Workspace analysis completed successfully")
            
        except Exception as e:
            error_msg = f"Workspace analysis failed: {e}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
        
        return result
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a single file comprehensively"""
        try:
            # Parse file
            parse_result = self.parser.parse_file(file_path)
            
            if parse_result.errors:
                return {"file_path": file_path, "errors": parse_result.errors}
            
            # AST analysis
            ast_analysis = self.ast_analyzer.analyze(parse_result)
            
            # Extract symbols
            symbols = self.symbol_extractor.extract(parse_result)
            
            # Calculate metrics
            metrics = self.complexity_analyzer.analyze(parse_result)
            
            # Build dependency graph for this file
            dependency_graph = self.dependency_analyzer.analyze_file(parse_result)
            
            return {
                "file_path": file_path,
                "language": parse_result.language.value,
                "parse_result": {
                    "functions": parse_result.functions,
                    "classes": parse_result.classes,
                    "imports": parse_result.imports,
                    "variables": parse_result.variables
                },
                "ast_analysis": ast_analysis,
                "symbols": [symbol.to_dict() for symbol in symbols],
                "metrics": metrics.to_dict(),
                "dependency_graph": dependency_graph.to_dict(),
                "errors": []
            }
            
        except Exception as e:
            return {
                "file_path": file_path,
                "errors": [f"Analysis failed: {e}"]
            }
    
    def _discover_files(self, workspace_path: str, max_files: Optional[int] = None) -> List[str]:
        """Discover code files in workspace"""
        files = []
        workspace_path = Path(workspace_path)
        
        def should_exclude_dir(dir_path: Path) -> bool:
            """Check if directory should be excluded"""
            for pattern in self.exclude_patterns:
                if pattern in str(dir_path):
                    return True
            return False
        
        def should_include_file(file_path: Path) -> bool:
            """Check if file should be included"""
            # Check supported extensions
            supported_extensions = self.parser.get_supported_extensions()
            if file_path.suffix.lower() not in supported_extensions:
                return False
            
            # Check include patterns
            for pattern in self.include_patterns:
                if file_path.match(pattern):
                    return True
            
            return False
        
        # Walk directory tree
        for root, dirs, filenames in os.walk(workspace_path):
            root_path = Path(root)
            
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if not should_exclude_dir(root_path / d)]
            
            # Add matching files
            for filename in filenames:
                file_path = root_path / filename
                if should_include_file(file_path):
                    files.append(str(file_path))
                    
                    if max_files and len(files) >= max_files:
                        return files
        
        return files
    
    def _parse_files_parallel(self, files: List[str]) -> List[ParseResult]:
        """Parse files in parallel"""
        parse_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit parsing tasks
            future_to_file = {
                executor.submit(self.parser.parse_file, file_path): file_path 
                for file_path in files
            }
            
            # Collect results
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    parse_results.append(result)
                except Exception as e:
                    # Create error result
                    error_result = ParseResult(
                        file_path=file_path,
                        language=LanguageType.UNKNOWN,
                        content="",
                        errors=[f"Parsing failed: {e}"]
                    )
                    parse_results.append(error_result)
        
        return parse_results
    
    def _analyze_ast_parallel(self, parse_results: List[ParseResult]) -> List[Dict[str, Any]]:
        """Perform AST analysis in parallel"""
        ast_analyses = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit AST analysis tasks
            future_to_result = {
                executor.submit(self.ast_analyzer.analyze, parse_result): parse_result
                for parse_result in parse_results
            }
            
            # Collect results
            for future in as_completed(future_to_result):
                try:
                    analysis = future.result()
                    ast_analyses.append(analysis)
                except Exception as e:
                    parse_result = future_to_result[future]
                    self.logger.error(f"AST analysis failed for {parse_result.file_path}: {e}")
        
        return ast_analyses
    
    def _calculate_metrics_parallel(self, parse_results: List[ParseResult]) -> List[CodeMetrics]:
        """Calculate metrics in parallel"""
        metrics_list = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit metrics calculation tasks
            future_to_result = {
                executor.submit(self.complexity_analyzer.analyze, parse_result): parse_result
                for parse_result in parse_results
            }
            
            # Collect results
            for future in as_completed(future_to_result):
                try:
                    metrics = future.result()
                    metrics_list.append(metrics)
                except Exception as e:
                    parse_result = future_to_result[future]
                    self.logger.error(f"Metrics calculation failed for {parse_result.file_path}: {e}")
        
        return metrics_list
    
    def _extract_symbols(self, parse_results: List[ParseResult]) -> List[Symbol]:
        """Extract symbols from all files"""
        all_symbols = []
        
        # Extract symbols from each file
        for parse_result in parse_results:
            try:
                symbols = self.symbol_extractor.extract(parse_result)
                all_symbols.extend(symbols)
            except Exception as e:
                self.logger.error(f"Symbol extraction failed for {parse_result.file_path}: {e}")
        
        # Find cross-references
        try:
            all_symbols = self.symbol_extractor.find_symbol_references(all_symbols, parse_results)
        except Exception as e:
            self.logger.error(f"Symbol reference analysis failed: {e}")
        
        return all_symbols
    
    def _calculate_workspace_metrics(self, result: WorkspaceAnalysisResult) -> Dict[str, Any]:
        """Calculate workspace-level metrics"""
        if not result.code_metrics:
            return {}
        
        # Use complexity analyzer to calculate workspace metrics
        workspace_metrics = self.complexity_analyzer.calculate_workspace_metrics(result.code_metrics)
        
        # Add dependency metrics
        if result.dependency_graph:
            dependency_metrics = result.dependency_graph.calculate_coupling_metrics()
            workspace_metrics["dependency_metrics"] = dependency_metrics
        
        # Add symbol metrics
        symbol_metrics = self._calculate_symbol_metrics(result.symbols)
        workspace_metrics["symbol_metrics"] = symbol_metrics
        
        return workspace_metrics
    
    def _calculate_symbol_metrics(self, symbols: List[Symbol]) -> Dict[str, Any]:
        """Calculate symbol-related metrics"""
        if not symbols:
            return {}
        
        # Count symbols by type
        symbol_counts = {}
        for symbol in symbols:
            symbol_type = symbol.symbol_type.value
            symbol_counts[symbol_type] = symbol_counts.get(symbol_type, 0) + 1
        
        # Find most referenced symbols
        most_referenced = sorted(
            [(symbol.name, len(symbol.references)) for symbol in symbols],
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # Calculate visibility distribution
        visibility_counts = {}
        for symbol in symbols:
            visibility = symbol.visibility.value
            visibility_counts[visibility] = visibility_counts.get(visibility, 0) + 1
        
        return {
            "total_symbols": len(symbols),
            "symbol_counts": symbol_counts,
            "most_referenced": most_referenced,
            "visibility_distribution": visibility_counts
        }
    
    def _generate_quality_report(self, result: WorkspaceAnalysisResult) -> Dict[str, Any]:
        """Generate code quality report"""
        if not result.code_metrics:
            return {}
        
        # Calculate quality scores
        avg_maintainability = sum(m.maintainability_index for m in result.code_metrics) / len(result.code_metrics)
        avg_comment_ratio = sum(m.quality.comment_ratio for m in result.code_metrics) / len(result.code_metrics)
        avg_complexity = sum(m.complexity.cyclomatic_complexity for m in result.code_metrics) / len(result.code_metrics)
        
        # Find problematic files
        high_complexity_files = [
            (m.file_path, m.complexity.cyclomatic_complexity)
            for m in result.code_metrics
            if m.complexity.cyclomatic_complexity > 10
        ]
        
        low_maintainability_files = [
            (m.file_path, m.maintainability_index)
            for m in result.code_metrics
            if m.maintainability_index < 50
        ]
        
        # Quality grade
        quality_grade = self._calculate_quality_grade(avg_maintainability, avg_comment_ratio, avg_complexity)
        
        return {
            "overall_grade": quality_grade,
            "average_maintainability": avg_maintainability,
            "average_comment_ratio": avg_comment_ratio,
            "average_complexity": avg_complexity,
            "high_complexity_files": high_complexity_files,
            "low_maintainability_files": low_maintainability_files,
            "recommendations": self._generate_recommendations(result)
        }
    
    def _analyze_architecture(self, result: WorkspaceAnalysisResult) -> Dict[str, Any]:
        """Analyze architectural patterns and insights"""
        insights = {}
        
        if result.dependency_graph:
            # Circular dependencies
            circular_deps = result.dependency_graph.find_circular_dependencies()
            insights["circular_dependencies"] = circular_deps
            
            # Highly coupled components
            highly_coupled = result.dependency_graph.get_highly_coupled_nodes()
            insights["highly_coupled_components"] = highly_coupled[:10]
            
            # Architecture patterns
            insights["patterns"] = self._detect_architecture_patterns(result)
        
        return insights
    
    def _calculate_quality_grade(self, maintainability: float, comment_ratio: float, complexity: float) -> str:
        """Calculate overall quality grade"""
        score = 0
        
        # Maintainability (40% weight)
        if maintainability >= 80:
            score += 40
        elif maintainability >= 60:
            score += 30
        elif maintainability >= 40:
            score += 20
        else:
            score += 10
        
        # Comment ratio (30% weight)
        if comment_ratio >= 0.2:
            score += 30
        elif comment_ratio >= 0.1:
            score += 20
        elif comment_ratio >= 0.05:
            score += 10
        
        # Complexity (30% weight)
        if complexity <= 5:
            score += 30
        elif complexity <= 10:
            score += 20
        elif complexity <= 15:
            score += 10
        
        # Convert to grade
        if score >= 80:
            return "A"
        elif score >= 70:
            return "B"
        elif score >= 60:
            return "C"
        elif score >= 50:
            return "D"
        else:
            return "F"
    
    def _generate_recommendations(self, result: WorkspaceAnalysisResult) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        if result.code_metrics:
            avg_complexity = sum(m.complexity.cyclomatic_complexity for m in result.code_metrics) / len(result.code_metrics)
            avg_comment_ratio = sum(m.quality.comment_ratio for m in result.code_metrics) / len(result.code_metrics)
            
            if avg_complexity > 10:
                recommendations.append("Consider refactoring complex functions to reduce cyclomatic complexity")
            
            if avg_comment_ratio < 0.1:
                recommendations.append("Add more comments and documentation to improve code readability")
        
        if result.dependency_graph:
            circular_deps = result.dependency_graph.find_circular_dependencies()
            if circular_deps:
                recommendations.append(f"Resolve {len(circular_deps)} circular dependencies to improve maintainability")
        
        return recommendations
    
    def _detect_architecture_patterns(self, result: WorkspaceAnalysisResult) -> List[str]:
        """Detect common architectural patterns"""
        patterns = []
        
        # Simple pattern detection based on file structure and symbols
        file_paths = [r.file_path for r in result.parse_results]
        
        # MVC pattern
        if any('controller' in path.lower() for path in file_paths) and \
           any('model' in path.lower() for path in file_paths) and \
           any('view' in path.lower() for path in file_paths):
            patterns.append("MVC (Model-View-Controller)")
        
        # Repository pattern
        if any('repository' in path.lower() for path in file_paths):
            patterns.append("Repository Pattern")
        
        # Factory pattern
        factory_symbols = [s for s in result.symbols if 'factory' in s.name.lower()]
        if factory_symbols:
            patterns.append("Factory Pattern")
        
        return patterns
