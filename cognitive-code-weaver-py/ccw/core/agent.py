"""
Base Agent Class - Foundation for all agents in the system

Provides the core interface and functionality that all agents must implement.
Supports plugin architecture and modular design.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class AgentStatus(Enum):
    """Agent execution status"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    DISABLED = "disabled"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AgentTask:
    """Represents a task for an agent to execute"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: str = ""
    description: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    created_at: float = field(default_factory=time.time)
    timeout: Optional[float] = None
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentContext:
    """Context information passed to agents"""
    session_id: str
    user_query: str = ""
    workspace_path: str = ""
    file_context: List[str] = field(default_factory=list)
    previous_results: Dict[str, Any] = field(default_factory=dict)
    configuration: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentResult:
    """Result returned by agent execution"""
    agent_id: str
    task_id: str
    status: AgentStatus
    data: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    execution_time: float = 0.0
    confidence: float = 1.0
    suggestions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class Agent(ABC):
    """
    Base class for all agents in the Cognitive Code Weaver system.
    
    Provides core functionality for:
    - Task execution
    - Status management
    - Error handling
    - Performance monitoring
    - Plugin integration
    """
    
    def __init__(self, agent_id: str, config: Dict[str, Any] = None):
        self.agent_id = agent_id
        self.config = config or {}
        self.status = AgentStatus.IDLE
        self.logger = logging.getLogger(f"ccw.agents.{agent_id}")
        self.metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0
        }
        self.capabilities = self._define_capabilities()
        self.dependencies = self._define_dependencies()
        
        # Plugin hooks
        self.pre_execute_hooks = []
        self.post_execute_hooks = []
        
        self.logger.info(f"Agent {agent_id} initialized with capabilities: {self.capabilities}")
    
    @abstractmethod
    async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Execute a task with the given context.
        
        Args:
            task: The task to execute
            context: Execution context and environment
            
        Returns:
            AgentResult with execution results
        """
        pass
    
    @abstractmethod
    def _define_capabilities(self) -> List[str]:
        """
        Define what this agent can do.
        
        Returns:
            List of capability strings
        """
        pass
    
    def _define_dependencies(self) -> List[str]:
        """
        Define what other agents or services this agent depends on.
        
        Returns:
            List of dependency identifiers
        """
        return []
    
    async def execute_with_monitoring(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Execute a task with full monitoring and error handling.
        
        Args:
            task: The task to execute
            context: Execution context
            
        Returns:
            AgentResult with execution results
        """
        start_time = time.time()
        self.status = AgentStatus.RUNNING
        
        try:
            self.logger.info(f"Starting task {task.id} of type {task.type}")
            
            # Run pre-execute hooks
            for hook in self.pre_execute_hooks:
                await hook(task, context)
            
            # Execute the main task
            result = await self._execute_with_timeout(task, context)
            
            # Update metrics
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            self._update_metrics(execution_time, success=True)
            
            # Run post-execute hooks
            for hook in self.post_execute_hooks:
                await hook(task, context, result)
            
            self.status = AgentStatus.COMPLETED
            self.logger.info(f"Completed task {task.id} in {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.status = AgentStatus.FAILED
            self._update_metrics(execution_time, success=False)
            
            error_msg = f"Task {task.id} failed: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=error_msg,
                execution_time=execution_time
            )
    
    async def _execute_with_timeout(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Execute task with optional timeout"""
        if task.timeout:
            try:
                return await asyncio.wait_for(
                    self.execute(task, context),
                    timeout=task.timeout
                )
            except asyncio.TimeoutError:
                raise Exception(f"Task {task.id} timed out after {task.timeout}s")
        else:
            return await self.execute(task, context)
    
    def _update_metrics(self, execution_time: float, success: bool):
        """Update agent performance metrics"""
        if success:
            self.metrics["tasks_completed"] += 1
        else:
            self.metrics["tasks_failed"] += 1
        
        self.metrics["total_execution_time"] += execution_time
        total_tasks = self.metrics["tasks_completed"] + self.metrics["tasks_failed"]
        
        if total_tasks > 0:
            self.metrics["average_execution_time"] = (
                self.metrics["total_execution_time"] / total_tasks
            )
    
    def can_handle_task(self, task: AgentTask) -> bool:
        """
        Check if this agent can handle the given task type.
        
        Args:
            task: Task to check
            
        Returns:
            True if agent can handle the task
        """
        return task.type in self.capabilities
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current agent status and metrics.
        
        Returns:
            Status dictionary
        """
        return {
            "agent_id": self.agent_id,
            "status": self.status.value,
            "capabilities": self.capabilities,
            "dependencies": self.dependencies,
            "metrics": self.metrics.copy(),
            "config": self.config.copy()
        }
    
    def add_pre_execute_hook(self, hook):
        """Add a hook to run before task execution"""
        self.pre_execute_hooks.append(hook)
    
    def add_post_execute_hook(self, hook):
        """Add a hook to run after task execution"""
        self.post_execute_hooks.append(hook)
    
    def enable(self):
        """Enable the agent"""
        self.status = AgentStatus.IDLE
        self.logger.info(f"Agent {self.agent_id} enabled")
    
    def disable(self):
        """Disable the agent"""
        self.status = AgentStatus.DISABLED
        self.logger.info(f"Agent {self.agent_id} disabled")
    
    def is_available(self) -> bool:
        """Check if agent is available for new tasks"""
        return self.status in [AgentStatus.IDLE, AgentStatus.COMPLETED]
    
    def __str__(self) -> str:
        return f"Agent({self.agent_id}, status={self.status.value})"
    
    def __repr__(self) -> str:
        return (f"Agent(agent_id='{self.agent_id}', "
                f"status={self.status.value}, "
                f"capabilities={self.capabilities})")


class PluginAgent(Agent):
    """
    Base class for plugin-based agents that can be dynamically loaded.
    """
    
    def __init__(self, agent_id: str, config: Dict[str, Any] = None, plugin_config: Dict[str, Any] = None):
        super().__init__(agent_id, config)
        self.plugin_config = plugin_config or {}
        self.plugin_metadata = self._load_plugin_metadata()
    
    def _load_plugin_metadata(self) -> Dict[str, Any]:
        """Load metadata about this plugin agent"""
        return {
            "version": "1.0.0",
            "author": "Unknown",
            "description": "Plugin agent",
            "requirements": []
        }
    
    def validate_plugin(self) -> bool:
        """Validate that the plugin is properly configured"""
        required_capabilities = self.plugin_metadata.get("required_capabilities", [])
        return all(cap in self.capabilities for cap in required_capabilities)


# Utility functions for agent management
def create_agent_task(task_type: str, description: str = "", data: Dict[str, Any] = None, 
                     priority: TaskPriority = TaskPriority.NORMAL) -> AgentTask:
    """Utility function to create an agent task"""
    return AgentTask(
        type=task_type,
        description=description,
        data=data or {},
        priority=priority
    )


def create_agent_context(session_id: str, user_query: str = "", workspace_path: str = "",
                        **kwargs) -> AgentContext:
    """Utility function to create an agent context"""
    return AgentContext(
        session_id=session_id,
        user_query=user_query,
        workspace_path=workspace_path,
        **kwargs
    )
