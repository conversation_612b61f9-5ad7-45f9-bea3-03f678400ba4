"""
Agent Registry - Central management system for all agents

Provides registration, discovery, and lifecycle management for agents.
Supports plugin architecture and dynamic agent loading.
"""

import asyncio
import importlib
import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Callable
from collections import defaultdict
import inspect

from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus

logger = logging.getLogger(__name__)


class AgentRegistryError(Exception):
    """Base exception for agent registry errors"""
    pass


class AgentNotFoundError(AgentRegistryError):
    """Raised when an agent is not found in the registry"""
    pass


class AgentRegistrationError(AgentRegistryError):
    """Raised when agent registration fails"""
    pass


class AgentRegistry:
    """
    Central registry for managing all agents in the system.
    
    Features:
    - Agent registration and discovery
    - Plugin loading and management
    - Agent lifecycle management
    - Task routing and load balancing
    - Dependency resolution
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.agents: Dict[str, Agent] = {}
        self.agent_classes: Dict[str, Type[Agent]] = {}
        self.agent_factories: Dict[str, Callable] = {}
        self.capabilities_map: Dict[str, List[str]] = defaultdict(list)
        self.dependency_graph: Dict[str, List[str]] = {}
        self.plugins_loaded: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger("ccw.registry")
        
        # Plugin directories
        self.plugin_dirs = self.config.get("plugin_dirs", ["./plugins"])
        
        self.logger.info("Agent registry initialized")
    
    def register_agent_class(self, agent_id: str, agent_class: Type[Agent], 
                           factory: Optional[Callable] = None) -> None:
        """
        Register an agent class for later instantiation.
        
        Args:
            agent_id: Unique identifier for the agent
            agent_class: Agent class to register
            factory: Optional factory function for creating instances
        """
        if not issubclass(agent_class, Agent):
            raise AgentRegistrationError(f"Class {agent_class} is not a subclass of Agent")
        
        self.agent_classes[agent_id] = agent_class
        if factory:
            self.agent_factories[agent_id] = factory
        
        # Extract capabilities from class if available
        if hasattr(agent_class, '_default_capabilities'):
            capabilities = agent_class._default_capabilities
            self.capabilities_map[agent_id] = capabilities
        
        self.logger.info(f"Registered agent class: {agent_id}")
    
    def register_agent_instance(self, agent: Agent) -> None:
        """
        Register an already instantiated agent.
        
        Args:
            agent: Agent instance to register
        """
        if not isinstance(agent, Agent):
            raise AgentRegistrationError(f"Object {agent} is not an Agent instance")
        
        agent_id = agent.agent_id
        self.agents[agent_id] = agent
        self.capabilities_map[agent_id] = agent.capabilities
        self.dependency_graph[agent_id] = agent.dependencies
        
        self.logger.info(f"Registered agent instance: {agent_id}")
    
    def create_agent(self, agent_id: str, config: Dict[str, Any] = None) -> Agent:
        """
        Create an agent instance from a registered class.
        
        Args:
            agent_id: ID of the agent to create
            config: Configuration for the agent
            
        Returns:
            Created agent instance
        """
        if agent_id not in self.agent_classes:
            raise AgentNotFoundError(f"Agent class {agent_id} not found in registry")
        
        agent_class = self.agent_classes[agent_id]
        agent_config = config or {}
        
        # Use factory if available
        if agent_id in self.agent_factories:
            factory = self.agent_factories[agent_id]
            agent = factory(agent_config)
        else:
            agent = agent_class(agent_id, agent_config)
        
        # Register the instance
        self.register_agent_instance(agent)
        
        return agent
    
    def get_agent(self, agent_id: str) -> Agent:
        """
        Get an agent instance by ID.
        
        Args:
            agent_id: ID of the agent to retrieve
            
        Returns:
            Agent instance
        """
        if agent_id not in self.agents:
            # Try to create it if class is registered
            if agent_id in self.agent_classes:
                return self.create_agent(agent_id)
            else:
                raise AgentNotFoundError(f"Agent {agent_id} not found")
        
        return self.agents[agent_id]
    
    def find_agents_by_capability(self, capability: str) -> List[Agent]:
        """
        Find all agents that have a specific capability.
        
        Args:
            capability: Capability to search for
            
        Returns:
            List of agents with the capability
        """
        matching_agents = []
        for agent_id, capabilities in self.capabilities_map.items():
            if capability in capabilities:
                try:
                    agent = self.get_agent(agent_id)
                    if agent.is_available():
                        matching_agents.append(agent)
                except AgentNotFoundError:
                    continue
        
        return matching_agents
    
    def find_best_agent_for_task(self, task: AgentTask) -> Optional[Agent]:
        """
        Find the best agent to handle a specific task.
        
        Args:
            task: Task to find an agent for
            
        Returns:
            Best agent for the task, or None if no suitable agent found
        """
        capable_agents = self.find_agents_by_capability(task.type)
        
        if not capable_agents:
            return None
        
        # Simple selection: return first available agent
        # TODO: Implement more sophisticated selection (load balancing, performance, etc.)
        for agent in capable_agents:
            if agent.can_handle_task(task) and agent.is_available():
                return agent
        
        return None
    
    def list_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        List all registered agents and their status.
        
        Returns:
            Dictionary of agent information
        """
        agent_info = {}
        
        # Include instantiated agents
        for agent_id, agent in self.agents.items():
            agent_info[agent_id] = agent.get_status()
        
        # Include registered classes not yet instantiated
        for agent_id, agent_class in self.agent_classes.items():
            if agent_id not in agent_info:
                agent_info[agent_id] = {
                    "agent_id": agent_id,
                    "status": "not_instantiated",
                    "class": agent_class.__name__,
                    "capabilities": getattr(agent_class, '_default_capabilities', [])
                }
        
        return agent_info
    
    def enable_agent(self, agent_id: str) -> None:
        """Enable a specific agent"""
        agent = self.get_agent(agent_id)
        agent.enable()
        self.logger.info(f"Enabled agent: {agent_id}")
    
    def disable_agent(self, agent_id: str) -> None:
        """Disable a specific agent"""
        agent = self.get_agent(agent_id)
        agent.disable()
        self.logger.info(f"Disabled agent: {agent_id}")
    
    def remove_agent(self, agent_id: str) -> None:
        """Remove an agent from the registry"""
        if agent_id in self.agents:
            del self.agents[agent_id]
        if agent_id in self.agent_classes:
            del self.agent_classes[agent_id]
        if agent_id in self.agent_factories:
            del self.agent_factories[agent_id]
        if agent_id in self.capabilities_map:
            del self.capabilities_map[agent_id]
        if agent_id in self.dependency_graph:
            del self.dependency_graph[agent_id]
        
        self.logger.info(f"Removed agent: {agent_id}")
    
    def load_plugins(self) -> None:
        """Load all plugins from configured directories"""
        for plugin_dir in self.plugin_dirs:
            self._load_plugins_from_directory(plugin_dir)
    
    def _load_plugins_from_directory(self, plugin_dir: str) -> None:
        """Load plugins from a specific directory"""
        plugin_path = Path(plugin_dir)
        if not plugin_path.exists():
            self.logger.warning(f"Plugin directory does not exist: {plugin_dir}")
            return
        
        # Add plugin directory to Python path
        if str(plugin_path) not in sys.path:
            sys.path.insert(0, str(plugin_path))
        
        # Find and load Python files
        for plugin_file in plugin_path.glob("*.py"):
            if plugin_file.name.startswith("__"):
                continue
            
            try:
                self._load_plugin_file(plugin_file)
            except Exception as e:
                self.logger.error(f"Failed to load plugin {plugin_file}: {e}")
    
    def _load_plugin_file(self, plugin_file: Path) -> None:
        """Load a specific plugin file"""
        module_name = plugin_file.stem
        
        try:
            # Import the module
            spec = importlib.util.spec_from_file_location(module_name, plugin_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Look for agent classes and registration functions
            self._register_plugin_agents(module, module_name)
            
            self.plugins_loaded[module_name] = {
                "file": str(plugin_file),
                "module": module,
                "loaded_at": asyncio.get_event_loop().time()
            }
            
            self.logger.info(f"Loaded plugin: {module_name}")
            
        except Exception as e:
            self.logger.error(f"Error loading plugin {plugin_file}: {e}")
            raise
    
    def _register_plugin_agents(self, module, module_name: str) -> None:
        """Register agents found in a plugin module"""
        # Look for Agent subclasses
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, Agent) and 
                obj != Agent):
                
                agent_id = f"{module_name}.{name}"
                self.register_agent_class(agent_id, obj)
        
        # Look for registration function
        if hasattr(module, 'register_agents'):
            module.register_agents(self)
    
    async def shutdown_all_agents(self) -> None:
        """Shutdown all agents gracefully"""
        self.logger.info("Shutting down all agents...")
        
        for agent_id, agent in self.agents.items():
            try:
                if hasattr(agent, 'shutdown'):
                    await agent.shutdown()
                agent.disable()
            except Exception as e:
                self.logger.error(f"Error shutting down agent {agent_id}: {e}")
        
        self.logger.info("All agents shut down")
    
    def get_dependency_order(self) -> List[str]:
        """
        Get agents in dependency order (dependencies first).
        
        Returns:
            List of agent IDs in dependency order
        """
        # Simple topological sort
        visited = set()
        order = []
        
        def visit(agent_id: str):
            if agent_id in visited:
                return
            visited.add(agent_id)
            
            # Visit dependencies first
            for dep in self.dependency_graph.get(agent_id, []):
                if dep in self.dependency_graph:
                    visit(dep)
            
            order.append(agent_id)
        
        for agent_id in self.dependency_graph:
            visit(agent_id)
        
        return order


# Global registry instance
agent_registry = AgentRegistry()


# Decorator for easy agent registration
def register_agent(agent_id: str, capabilities: List[str] = None):
    """
    Decorator to register an agent class.
    
    Args:
        agent_id: Unique identifier for the agent
        capabilities: List of capabilities this agent provides
    """
    def decorator(agent_class: Type[Agent]):
        if capabilities:
            agent_class._default_capabilities = capabilities
        agent_registry.register_agent_class(agent_id, agent_class)
        return agent_class
    
    return decorator


# Utility functions
def get_agent(agent_id: str) -> Agent:
    """Get an agent from the global registry"""
    return agent_registry.get_agent(agent_id)


def find_agents_for_task(task: AgentTask) -> List[Agent]:
    """Find agents capable of handling a task"""
    return agent_registry.find_agents_by_capability(task.type)


def create_agent(agent_id: str, config: Dict[str, Any] = None) -> Agent:
    """Create an agent instance"""
    return agent_registry.create_agent(agent_id, config)
