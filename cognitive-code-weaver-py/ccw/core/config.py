"""
Configuration System - Centralized configuration management

Supports YAML, JSON, environment variables, and command-line arguments.
Provides validation, defaults, and dynamic configuration updates.
"""

import os
import logging
import yaml
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class ConfigFormat(Enum):
    """Supported configuration formats"""
    YAML = "yaml"
    JSON = "json"
    ENV = "env"


@dataclass
class ConfigSchema:
    """Schema definition for configuration validation"""
    required_fields: List[str] = field(default_factory=list)
    optional_fields: Dict[str, Any] = field(default_factory=dict)
    field_types: Dict[str, type] = field(default_factory=dict)
    validators: Dict[str, callable] = field(default_factory=dict)


class ConfigError(Exception):
    """Base exception for configuration errors"""
    pass


class ConfigValidationError(ConfigError):
    """Raised when configuration validation fails"""
    pass


class Config:
    """
    Centralized configuration management system.
    
    Features:
    - Multiple configuration sources (files, env vars, CLI args)
    - Hierarchical configuration with overrides
    - Validation and type checking
    - Dynamic configuration updates
    - Environment-specific configurations
    """
    
    def __init__(self, config_file: Optional[str] = None, 
                 config_dict: Optional[Dict[str, Any]] = None,
                 schema: Optional[ConfigSchema] = None):
        self.config_data: Dict[str, Any] = {}
        self.config_sources: List[str] = []
        self.schema = schema
        self.logger = logging.getLogger("ccw.config")
        
        # Load default configuration
        self._load_defaults()
        
        # Load from dictionary if provided
        if config_dict:
            self.update_config(config_dict, source="dict")
        
        # Load from file if provided
        if config_file:
            self.load_from_file(config_file)
        
        # Load from environment variables
        self._load_from_environment()
        
        # Validate configuration
        if self.schema:
            self.validate()
        
        self.logger.info(f"Configuration loaded from sources: {self.config_sources}")
    
    def _load_defaults(self):
        """Load default configuration values"""
        defaults = {
            "llm": {
                "provider": "openai",
                "model": "gpt-4",
                "api_key": "",
                "base_url": "",
                "timeout": 30.0,
                "max_retries": 3,
                "temperature": 0.7
            },
            "database": {
                "type": "memory",
                "url": "",
                "username": "",
                "password": "",
                "database": "ccw"
            },
            "analysis": {
                "max_file_size": 1048576,  # 1MB
                "exclude_patterns": [
                    "*/node_modules/*",
                    "*/dist/*",
                    "*/build/*",
                    "*/.git/*",
                    "*/coverage/*",
                    "*/__pycache__/*",
                    "*.pyc"
                ],
                "include_patterns": [
                    "**/*.py",
                    "**/*.js",
                    "**/*.ts",
                    "**/*.jsx",
                    "**/*.tsx",
                    "**/*.java",
                    "**/*.c",
                    "**/*.cpp",
                    "**/*.cs",
                    "**/*.go",
                    "**/*.rb",
                    "**/*.php"
                ],
                "languages": ["python", "javascript", "typescript", "java", "c", "cpp"]
            },
            "agents": {
                "enabled": [
                    "master_agent",
                    "cognitive_agent", 
                    "planner",
                    "code_reader",
                    "reasoner",
                    "bug_detector"
                ],
                "plugins_dir": "./plugins",
                "max_concurrent_tasks": 5,
                "task_timeout": 300.0
            },
            "semantic": {
                "enable_topic_modeling": True,
                "min_concept_frequency": 2,
                "embedding_model": "sentence-transformers/all-MiniLM-L6-v2",
                "max_concepts": 1000
            },
            "retrieval": {
                "max_results": 10,
                "semantic_weight": 0.4,
                "structural_weight": 0.3,
                "contextual_weight": 0.3,
                "similarity_threshold": 0.7
            },
            "ui": {
                "auto_analyze_on_startup": False,
                "show_progress": True,
                "output_format": "text"
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            },
            "performance": {
                "enable_monitoring": True,
                "metrics_retention_days": 30,
                "cache_size": 1000,
                "enable_caching": True
            }
        }
        
        self.config_data = defaults
        self.config_sources.append("defaults")
    
    def load_from_file(self, config_file: str) -> None:
        """
        Load configuration from a file.
        
        Args:
            config_file: Path to configuration file
        """
        config_path = Path(config_file)
        
        if not config_path.exists():
            self.logger.warning(f"Configuration file not found: {config_file}")
            return
        
        try:
            with open(config_path, 'r') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    file_config = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    raise ConfigError(f"Unsupported configuration file format: {config_path.suffix}")
            
            if file_config:
                self.update_config(file_config, source=str(config_path))
                self.logger.info(f"Loaded configuration from: {config_file}")
        
        except Exception as e:
            self.logger.error(f"Error loading configuration file {config_file}: {e}")
            raise ConfigError(f"Failed to load configuration file: {e}")
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        env_config = {}
        
        # Map environment variables to configuration keys
        env_mappings = {
            "CCW_LLM_PROVIDER": ("llm", "provider"),
            "CCW_LLM_MODEL": ("llm", "model"),
            "CCW_LLM_API_KEY": ("llm", "api_key"),
            "CCW_LLM_BASE_URL": ("llm", "base_url"),
            "CCW_DATABASE_TYPE": ("database", "type"),
            "CCW_DATABASE_URL": ("database", "url"),
            "CCW_DATABASE_USERNAME": ("database", "username"),
            "CCW_DATABASE_PASSWORD": ("database", "password"),
            "CCW_LOG_LEVEL": ("logging", "level"),
            "CCW_LOG_FILE": ("logging", "file"),
            "CCW_PLUGINS_DIR": ("agents", "plugins_dir"),
            "CCW_MAX_FILE_SIZE": ("analysis", "max_file_size"),
            "CCW_OUTPUT_FORMAT": ("ui", "output_format")
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                if section not in env_config:
                    env_config[section] = {}
                
                # Type conversion
                env_config[section][key] = self._convert_env_value(value, section, key)
        
        if env_config:
            self.update_config(env_config, source="environment")
    
    def _convert_env_value(self, value: str, section: str, key: str) -> Any:
        """Convert environment variable value to appropriate type"""
        # Boolean values
        if value.lower() in ['true', 'false']:
            return value.lower() == 'true'
        
        # Numeric values
        if key in ['max_file_size', 'timeout', 'max_retries', 'max_concurrent_tasks']:
            try:
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            except ValueError:
                pass
        
        # List values (comma-separated)
        if key in ['enabled', 'exclude_patterns', 'include_patterns', 'languages']:
            return [item.strip() for item in value.split(',')]
        
        return value
    
    def update_config(self, updates: Dict[str, Any], source: str = "update") -> None:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of configuration updates
            source: Source of the updates for tracking
        """
        self._deep_update(self.config_data, updates)
        if source not in self.config_sources:
            self.config_sources.append(source)
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """Recursively update nested dictionaries"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'llm.provider')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'llm.provider')
            value: Value to set
        """
        keys = key.split('.')
        config = self.config_data
        
        # Navigate to the parent dictionary
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get an entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Section configuration dictionary
        """
        return self.config_data.get(section, {}).copy()
    
    def validate(self) -> None:
        """Validate configuration against schema"""
        if not self.schema:
            return
        
        errors = []
        
        # Check required fields
        for field in self.schema.required_fields:
            if self.get(field) is None:
                errors.append(f"Required field missing: {field}")
        
        # Check field types
        for field, expected_type in self.schema.field_types.items():
            value = self.get(field)
            if value is not None and not isinstance(value, expected_type):
                errors.append(f"Field {field} has wrong type: expected {expected_type}, got {type(value)}")
        
        # Run custom validators
        for field, validator in self.schema.validators.items():
            value = self.get(field)
            if value is not None:
                try:
                    if not validator(value):
                        errors.append(f"Validation failed for field: {field}")
                except Exception as e:
                    errors.append(f"Validator error for field {field}: {e}")
        
        if errors:
            raise ConfigValidationError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def save_to_file(self, config_file: str, format: ConfigFormat = ConfigFormat.YAML) -> None:
        """
        Save current configuration to a file.
        
        Args:
            config_file: Path to save configuration
            format: Configuration format
        """
        config_path = Path(config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w') as f:
                if format == ConfigFormat.YAML:
                    yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
                elif format == ConfigFormat.JSON:
                    json.dump(self.config_data, f, indent=2)
                else:
                    raise ConfigError(f"Unsupported format for saving: {format}")
            
            self.logger.info(f"Configuration saved to: {config_file}")
        
        except Exception as e:
            self.logger.error(f"Error saving configuration to {config_file}: {e}")
            raise ConfigError(f"Failed to save configuration: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary"""
        return self.config_data.copy()
    
    def get_env_vars(self) -> Dict[str, str]:
        """Get configuration as environment variables"""
        env_vars = {}
        
        def flatten_dict(d: Dict[str, Any], prefix: str = "CCW"):
            for key, value in d.items():
                env_key = f"{prefix}_{key.upper()}"
                if isinstance(value, dict):
                    flatten_dict(value, env_key)
                else:
                    env_vars[env_key] = str(value)
        
        flatten_dict(self.config_data)
        return env_vars
    
    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access"""
        return self.get(key)
    
    def __setitem__(self, key: str, value: Any) -> None:
        """Allow dictionary-style setting"""
        self.set(key, value)
    
    def __contains__(self, key: str) -> bool:
        """Check if key exists in configuration"""
        return self.get(key) is not None


# Global configuration instance
config = Config()


# Utility functions
def load_config(config_file: str) -> Config:
    """Load configuration from file"""
    return Config(config_file=config_file)


def get_config() -> Config:
    """Get the global configuration instance"""
    return config


def init_config(config_file: Optional[str] = None, 
               config_dict: Optional[Dict[str, Any]] = None) -> Config:
    """Initialize global configuration"""
    global config
    config = Config(config_file=config_file, config_dict=config_dict)
    return config
