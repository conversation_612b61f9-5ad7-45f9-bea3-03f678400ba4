"""
Message Bus - Inter-agent communication system

Provides asynchronous messaging, event handling, and coordination between agents.
Supports publish-subscribe patterns and direct messaging.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Set
from enum import Enum
import uuid
import json

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of messages in the system"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    EVENT = "event"
    NOTIFICATION = "notification"
    QUERY = "query"
    COMMAND = "command"
    STATUS_UPDATE = "status_update"


class MessagePriority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Message:
    """Represents a message in the system"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: MessageType = MessageType.EVENT
    sender: str = ""
    recipient: Optional[str] = None  # None for broadcast
    topic: str = ""
    payload: Dict[str, Any] = field(default_factory=dict)
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl: Optional[float] = None  # Time to live in seconds
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "type": self.type.value,
            "sender": self.sender,
            "recipient": self.recipient,
            "topic": self.topic,
            "payload": self.payload,
            "priority": self.priority.value,
            "timestamp": self.timestamp,
            "correlation_id": self.correlation_id,
            "reply_to": self.reply_to,
            "ttl": self.ttl,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary"""
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            type=MessageType(data.get("type", "event")),
            sender=data.get("sender", ""),
            recipient=data.get("recipient"),
            topic=data.get("topic", ""),
            payload=data.get("payload", {}),
            priority=MessagePriority(data.get("priority", 2)),
            timestamp=data.get("timestamp", time.time()),
            correlation_id=data.get("correlation_id"),
            reply_to=data.get("reply_to"),
            ttl=data.get("ttl"),
            metadata=data.get("metadata", {})
        )
    
    def is_expired(self) -> bool:
        """Check if message has expired"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl


class MessageHandler:
    """Base class for message handlers"""
    
    def __init__(self, handler_id: str):
        self.handler_id = handler_id
        self.subscriptions: Set[str] = set()
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """
        Handle an incoming message.
        
        Args:
            message: The message to handle
            
        Returns:
            Optional response message
        """
        pass
    
    def subscribe_to_topic(self, topic: str):
        """Subscribe to a topic"""
        self.subscriptions.add(topic)
    
    def unsubscribe_from_topic(self, topic: str):
        """Unsubscribe from a topic"""
        self.subscriptions.discard(topic)


class MessageBus:
    """
    Central message bus for inter-agent communication.
    
    Features:
    - Publish-subscribe messaging
    - Direct messaging between agents
    - Message queuing and delivery
    - Event handling and notifications
    - Message persistence and replay
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.handlers: Dict[str, MessageHandler] = {}
        self.topic_subscribers: Dict[str, Set[str]] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.message_history: List[Message] = []
        self.running = False
        self.worker_task: Optional[asyncio.Task] = None
        self.logger = logging.getLogger("ccw.message_bus")
        
        # Configuration
        self.max_history_size = self.config.get("max_history_size", 1000)
        self.enable_persistence = self.config.get("enable_persistence", False)
        self.persistence_file = self.config.get("persistence_file", "messages.json")
        
        self.logger.info("Message bus initialized")
    
    def register_handler(self, handler: MessageHandler):
        """Register a message handler"""
        self.handlers[handler.handler_id] = handler
        self.logger.info(f"Registered message handler: {handler.handler_id}")
    
    def unregister_handler(self, handler_id: str):
        """Unregister a message handler"""
        if handler_id in self.handlers:
            handler = self.handlers[handler_id]
            # Remove from all topic subscriptions
            for topic in list(handler.subscriptions):
                self.unsubscribe_from_topic(handler_id, topic)
            del self.handlers[handler_id]
            self.logger.info(f"Unregistered message handler: {handler_id}")
    
    def subscribe_to_topic(self, handler_id: str, topic: str):
        """Subscribe a handler to a topic"""
        if handler_id not in self.handlers:
            raise ValueError(f"Handler {handler_id} not registered")
        
        if topic not in self.topic_subscribers:
            self.topic_subscribers[topic] = set()
        
        self.topic_subscribers[topic].add(handler_id)
        self.handlers[handler_id].subscribe_to_topic(topic)
        
        self.logger.debug(f"Handler {handler_id} subscribed to topic: {topic}")
    
    def unsubscribe_from_topic(self, handler_id: str, topic: str):
        """Unsubscribe a handler from a topic"""
        if topic in self.topic_subscribers:
            self.topic_subscribers[topic].discard(handler_id)
            if not self.topic_subscribers[topic]:
                del self.topic_subscribers[topic]
        
        if handler_id in self.handlers:
            self.handlers[handler_id].unsubscribe_from_topic(topic)
        
        self.logger.debug(f"Handler {handler_id} unsubscribed from topic: {topic}")
    
    async def publish(self, message: Message) -> None:
        """
        Publish a message to the bus.
        
        Args:
            message: Message to publish
        """
        # Add to queue for processing
        await self.message_queue.put(message)
        self.logger.debug(f"Published message {message.id} to topic {message.topic}")
    
    async def send_direct_message(self, sender: str, recipient: str, 
                                 message_type: MessageType, payload: Dict[str, Any],
                                 correlation_id: Optional[str] = None) -> None:
        """
        Send a direct message to a specific handler.
        
        Args:
            sender: ID of the sender
            recipient: ID of the recipient
            message_type: Type of message
            payload: Message payload
            correlation_id: Optional correlation ID for request-response
        """
        message = Message(
            type=message_type,
            sender=sender,
            recipient=recipient,
            payload=payload,
            correlation_id=correlation_id
        )
        
        await self.publish(message)
    
    async def broadcast_event(self, sender: str, topic: str, event_data: Dict[str, Any]) -> None:
        """
        Broadcast an event to all subscribers of a topic.
        
        Args:
            sender: ID of the sender
            topic: Topic to broadcast to
            event_data: Event data
        """
        message = Message(
            type=MessageType.EVENT,
            sender=sender,
            topic=topic,
            payload=event_data
        )
        
        await self.publish(message)
    
    async def request_response(self, sender: str, recipient: str, 
                              request_data: Dict[str, Any], 
                              timeout: float = 30.0) -> Optional[Message]:
        """
        Send a request and wait for a response.
        
        Args:
            sender: ID of the sender
            recipient: ID of the recipient
            request_data: Request data
            timeout: Timeout in seconds
            
        Returns:
            Response message or None if timeout
        """
        correlation_id = str(uuid.uuid4())
        
        # Create response future
        response_future = asyncio.Future()
        self._pending_responses[correlation_id] = response_future
        
        # Send request
        await self.send_direct_message(
            sender=sender,
            recipient=recipient,
            message_type=MessageType.QUERY,
            payload=request_data,
            correlation_id=correlation_id
        )
        
        try:
            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            self.logger.warning(f"Request {correlation_id} timed out")
            return None
        finally:
            # Clean up
            self._pending_responses.pop(correlation_id, None)
    
    async def start(self):
        """Start the message bus worker"""
        if self.running:
            return
        
        self.running = True
        self._pending_responses: Dict[str, asyncio.Future] = {}
        
        # Load persisted messages if enabled
        if self.enable_persistence:
            self._load_persisted_messages()
        
        # Start worker task
        self.worker_task = asyncio.create_task(self._message_worker())
        self.logger.info("Message bus started")
    
    async def stop(self):
        """Stop the message bus worker"""
        if not self.running:
            return
        
        self.running = False
        
        # Cancel worker task
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        
        # Persist messages if enabled
        if self.enable_persistence:
            self._persist_messages()
        
        self.logger.info("Message bus stopped")
    
    async def _message_worker(self):
        """Main message processing worker"""
        while self.running:
            try:
                # Get message from queue
                message = await asyncio.wait_for(
                    self.message_queue.get(), 
                    timeout=1.0
                )
                
                # Check if message has expired
                if message.is_expired():
                    self.logger.debug(f"Message {message.id} expired, discarding")
                    continue
                
                # Process the message
                await self._process_message(message)
                
                # Add to history
                self._add_to_history(message)
                
            except asyncio.TimeoutError:
                # No message received, continue
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
    
    async def _process_message(self, message: Message):
        """Process a single message"""
        # Handle direct messages
        if message.recipient:
            await self._deliver_direct_message(message)
        else:
            # Handle topic-based messages
            await self._deliver_topic_message(message)
    
    async def _deliver_direct_message(self, message: Message):
        """Deliver a message to a specific recipient"""
        if message.recipient not in self.handlers:
            self.logger.warning(f"Recipient {message.recipient} not found for message {message.id}")
            return
        
        handler = self.handlers[message.recipient]
        
        try:
            response = await handler.handle_message(message)
            
            # Handle response if this was a request
            if response and message.correlation_id:
                response.correlation_id = message.correlation_id
                response.recipient = message.sender
                await self.publish(response)
            
        except Exception as e:
            self.logger.error(f"Error delivering message {message.id} to {message.recipient}: {e}")
    
    async def _deliver_topic_message(self, message: Message):
        """Deliver a message to all topic subscribers"""
        if message.topic not in self.topic_subscribers:
            self.logger.debug(f"No subscribers for topic: {message.topic}")
            return
        
        subscribers = self.topic_subscribers[message.topic].copy()
        
        # Deliver to all subscribers
        for handler_id in subscribers:
            if handler_id in self.handlers:
                handler = self.handlers[handler_id]
                try:
                    await handler.handle_message(message)
                except Exception as e:
                    self.logger.error(f"Error delivering message {message.id} to {handler_id}: {e}")
    
    def _add_to_history(self, message: Message):
        """Add message to history"""
        self.message_history.append(message)
        
        # Trim history if too large
        if len(self.message_history) > self.max_history_size:
            self.message_history = self.message_history[-self.max_history_size:]
    
    def _persist_messages(self):
        """Persist messages to file"""
        if not self.enable_persistence:
            return
        
        try:
            messages_data = [msg.to_dict() for msg in self.message_history]
            with open(self.persistence_file, 'w') as f:
                json.dump(messages_data, f, indent=2)
            self.logger.info(f"Persisted {len(messages_data)} messages")
        except Exception as e:
            self.logger.error(f"Error persisting messages: {e}")
    
    def _load_persisted_messages(self):
        """Load persisted messages from file"""
        if not self.enable_persistence:
            return
        
        try:
            with open(self.persistence_file, 'r') as f:
                messages_data = json.load(f)
            
            self.message_history = [Message.from_dict(data) for data in messages_data]
            self.logger.info(f"Loaded {len(self.message_history)} persisted messages")
        except FileNotFoundError:
            self.logger.info("No persisted messages found")
        except Exception as e:
            self.logger.error(f"Error loading persisted messages: {e}")
    
    def get_message_history(self, topic: Optional[str] = None, 
                           sender: Optional[str] = None,
                           limit: Optional[int] = None) -> List[Message]:
        """
        Get message history with optional filtering.
        
        Args:
            topic: Filter by topic
            sender: Filter by sender
            limit: Limit number of messages
            
        Returns:
            List of messages
        """
        messages = self.message_history
        
        if topic:
            messages = [msg for msg in messages if msg.topic == topic]
        
        if sender:
            messages = [msg for msg in messages if msg.sender == sender]
        
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message bus statistics"""
        return {
            "handlers_count": len(self.handlers),
            "topics_count": len(self.topic_subscribers),
            "queue_size": self.message_queue.qsize(),
            "history_size": len(self.message_history),
            "running": self.running
        }


# Global message bus instance
message_bus = MessageBus()
