"""
Cognitive Code Weaver - Python Edition

A highly modular, AI-powered code analysis and reasoning system.
"""

__version__ = "0.1.0"
__author__ = "Cognitive Code Weaver Team"
__email__ = "<EMAIL>"

from ccw.core.agent import Agent
from ccw.core.registry import AgentRegistry, agent_registry
from ccw.core.config import Config
from ccw.core.message_bus import MessageBus

# Core exports
__all__ = [
    "Agent",
    "AgentRegistry", 
    "agent_registry",
    "Config",
    "MessageBus",
]

# Version info
VERSION_INFO = {
    "version": __version__,
    "python_requires": ">=3.8",
    "description": "AI-powered code analysis and reasoning system",
    "features": [
        "Multi-agent architecture",
        "Plugin-based extensibility", 
        "Multi-language code analysis",
        "Knowledge graph integration",
        "CLI and API interfaces"
    ]
}
