"""
LLM Client - Main interface for LLM interactions

Provides a unified interface for interacting with different LLM providers
with automatic provider selection, fallback handling, and response caching.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass, field

from ccw.core.config import config
from .providers.base import (
    LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMCapability,
    LLMProviderError, LLMRateLimitError, LLMAuthenticationError,
    MessageRole, create_message, create_system_message, create_user_message
)
from .providers.openai_provider import OpenAIProvider
from .providers.anthropic_provider import AnthropicProvider
from .providers.local_provider import LocalProvider

logger = logging.getLogger(__name__)


@dataclass
class LLMClientConfig:
    """Configuration for LLM client"""
    primary_provider: str = "openai"
    fallback_providers: List[str] = field(default_factory=list)
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1 hour
    max_retries: int = 3
    retry_delay: float = 1.0
    enable_fallback: bool = True
    response_timeout: float = 60.0


class LLMClient:
    """
    Main LLM client that provides a unified interface to multiple providers.
    
    Features:
    - Multiple provider support (OpenAI, Anthropic, Local)
    - Automatic fallback handling
    - Response caching
    - Load balancing
    - Usage tracking and cost monitoring
    - Streaming support
    """
    
    def __init__(self, client_config: Optional[LLMClientConfig] = None):
        self.client_config = client_config or LLMClientConfig()
        self.providers: Dict[str, LLMProvider] = {}
        self.response_cache: Dict[str, Dict[str, Any]] = {}
        self.usage_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "provider_usage": {}
        }
        
        self.logger = logging.getLogger("ccw.llm.client")
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all configured LLM providers"""
        try:
            # Get LLM configuration from global config
            llm_config = config.get_section("llm")
            
            # Create provider configurations
            provider_configs = self._create_provider_configs(llm_config)
            
            # Initialize each provider
            for provider_name, provider_config in provider_configs.items():
                try:
                    provider = self._create_provider(provider_name, provider_config)
                    self.providers[provider_name] = provider
                    self.usage_stats["provider_usage"][provider_name] = {
                        "requests": 0,
                        "tokens": 0,
                        "cost": 0.0
                    }
                    self.logger.info(f"Initialized {provider_name} provider")
                except Exception as e:
                    self.logger.error(f"Failed to initialize {provider_name} provider: {e}")
            
            if not self.providers:
                raise LLMProviderError("No LLM providers could be initialized", "client")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM client: {e}")
            raise
    
    def _create_provider_configs(self, llm_config: Dict[str, Any]) -> Dict[str, LLMConfig]:
        """Create provider configurations from global config"""
        configs = {}
        
        # Primary provider
        primary_provider = llm_config.get("provider", "openai")
        configs[primary_provider] = LLMConfig(
            provider=primary_provider,
            model=llm_config.get("model", "gpt-4"),
            api_key=llm_config.get("api_key", ""),
            base_url=llm_config.get("base_url"),
            temperature=llm_config.get("temperature", 0.7),
            max_tokens=llm_config.get("max_tokens"),
            timeout=llm_config.get("timeout", 30.0),
            max_retries=llm_config.get("max_retries", 3),
            additional_params=llm_config.get("additional_params", {})
        )
        
        # Fallback providers (if configured)
        fallback_providers = self.client_config.fallback_providers
        for provider_name in fallback_providers:
            if provider_name != primary_provider:
                # Use same config but different provider
                fallback_config = LLMConfig(
                    provider=provider_name,
                    model=self._get_fallback_model(provider_name),
                    api_key=llm_config.get("api_key", ""),
                    base_url=llm_config.get("base_url"),
                    temperature=llm_config.get("temperature", 0.7),
                    max_tokens=llm_config.get("max_tokens"),
                    timeout=llm_config.get("timeout", 30.0),
                    max_retries=llm_config.get("max_retries", 3)
                )
                configs[provider_name] = fallback_config
        
        return configs
    
    def _create_provider(self, provider_name: str, provider_config: LLMConfig) -> LLMProvider:
        """Create a provider instance"""
        if provider_name == "openai":
            return OpenAIProvider(provider_config)
        elif provider_name == "anthropic":
            return AnthropicProvider(provider_config)
        elif provider_name == "local":
            return LocalProvider(provider_config)
        else:
            raise LLMProviderError(f"Unknown provider: {provider_name}", provider_name)
    
    def _get_fallback_model(self, provider_name: str) -> str:
        """Get default model for fallback provider"""
        default_models = {
            "openai": "gpt-3.5-turbo",
            "anthropic": "claude-3-haiku-20240307",
            "local": "llama2:7b"
        }
        return default_models.get(provider_name, "gpt-3.5-turbo")
    
    async def generate_response(self, messages: Union[List[LLMMessage], str], 
                               provider: Optional[str] = None,
                               **kwargs) -> LLMResponse:
        """
        Generate a response using the specified or primary provider.
        
        Args:
            messages: List of messages or a single string
            provider: Specific provider to use (optional)
            **kwargs: Additional parameters
            
        Returns:
            LLM response
        """
        # Convert string to messages if needed
        if isinstance(messages, str):
            messages = [create_user_message(messages)]
        
        # Check cache first
        if self.client_config.enable_caching:
            cache_key = self._generate_cache_key(messages, kwargs)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                self.logger.debug("Returning cached response")
                return cached_response
        
        # Determine provider to use
        provider_name = provider or self.client_config.primary_provider
        
        # Track request
        self.usage_stats["total_requests"] += 1
        
        try:
            # Try primary provider
            response = await self._generate_with_provider(provider_name, messages, **kwargs)
            
            # Cache response
            if self.client_config.enable_caching:
                self._cache_response(cache_key, response)
            
            # Update stats
            self._update_usage_stats(provider_name, response, success=True)
            self.usage_stats["successful_requests"] += 1
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error with {provider_name} provider: {e}")
            
            # Try fallback if enabled and available
            if (self.client_config.enable_fallback and 
                provider_name == self.client_config.primary_provider and
                self.client_config.fallback_providers):
                
                for fallback_provider in self.client_config.fallback_providers:
                    if fallback_provider in self.providers:
                        try:
                            self.logger.info(f"Trying fallback provider: {fallback_provider}")
                            response = await self._generate_with_provider(
                                fallback_provider, messages, **kwargs
                            )
                            
                            # Cache response
                            if self.client_config.enable_caching:
                                self._cache_response(cache_key, response)
                            
                            # Update stats
                            self._update_usage_stats(fallback_provider, response, success=True)
                            self.usage_stats["successful_requests"] += 1
                            
                            return response
                            
                        except Exception as fallback_error:
                            self.logger.error(f"Fallback provider {fallback_provider} also failed: {fallback_error}")
                            continue
            
            # All providers failed
            self.usage_stats["failed_requests"] += 1
            raise LLMProviderError(f"All providers failed. Last error: {e}", "client")
    
    async def generate_stream(self, messages: Union[List[LLMMessage], str],
                             provider: Optional[str] = None,
                             **kwargs) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response.
        
        Args:
            messages: List of messages or a single string
            provider: Specific provider to use (optional)
            **kwargs: Additional parameters
            
        Yields:
            Chunks of response text
        """
        # Convert string to messages if needed
        if isinstance(messages, str):
            messages = [create_user_message(messages)]
        
        # Determine provider to use
        provider_name = provider or self.client_config.primary_provider
        
        if provider_name not in self.providers:
            raise LLMProviderError(f"Provider {provider_name} not available", provider_name)
        
        provider_instance = self.providers[provider_name]
        
        # Check if provider supports streaming
        if not provider_instance.supports_capability(LLMCapability.STREAMING):
            raise LLMProviderError(f"Provider {provider_name} does not support streaming", provider_name)
        
        try:
            async for chunk in provider_instance.generate_stream(messages, **kwargs):
                yield chunk
        except Exception as e:
            self.logger.error(f"Streaming error with {provider_name}: {e}")
            raise
    
    async def generate_embeddings(self, texts: List[str],
                                 provider: Optional[str] = None,
                                 **kwargs) -> List[List[float]]:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: List of texts to embed
            provider: Specific provider to use (optional)
            **kwargs: Additional parameters
            
        Returns:
            List of embedding vectors
        """
        # Determine provider to use
        provider_name = provider or self.client_config.primary_provider
        
        if provider_name not in self.providers:
            raise LLMProviderError(f"Provider {provider_name} not available", provider_name)
        
        provider_instance = self.providers[provider_name]
        
        # Check if provider supports embeddings
        if not provider_instance.supports_capability(LLMCapability.EMBEDDINGS):
            raise LLMProviderError(f"Provider {provider_name} does not support embeddings", provider_name)
        
        try:
            return await provider_instance.generate_embeddings(texts, **kwargs)
        except Exception as e:
            self.logger.error(f"Embeddings error with {provider_name}: {e}")
            raise
    
    async def _generate_with_provider(self, provider_name: str, 
                                     messages: List[LLMMessage],
                                     **kwargs) -> LLMResponse:
        """Generate response with a specific provider"""
        if provider_name not in self.providers:
            raise LLMProviderError(f"Provider {provider_name} not available", provider_name)
        
        provider = self.providers[provider_name]
        return await provider.generate_response(messages, **kwargs)
    
    def _generate_cache_key(self, messages: List[LLMMessage], kwargs: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        import hashlib
        
        # Create a string representation of the request
        request_str = ""
        for msg in messages:
            request_str += f"{msg.role.value}:{msg.content}|"
        
        # Add relevant kwargs
        for key in ["temperature", "max_tokens", "model"]:
            if key in kwargs:
                request_str += f"{key}:{kwargs[key]}|"
        
        # Generate hash
        return hashlib.md5(request_str.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[LLMResponse]:
        """Get cached response if available and not expired"""
        if cache_key in self.response_cache:
            cached_data = self.response_cache[cache_key]
            if time.time() - cached_data["timestamp"] < self.client_config.cache_ttl:
                return cached_data["response"]
            else:
                # Remove expired cache entry
                del self.response_cache[cache_key]
        
        return None
    
    def _cache_response(self, cache_key: str, response: LLMResponse):
        """Cache response"""
        self.response_cache[cache_key] = {
            "response": response,
            "timestamp": time.time()
        }
        
        # Clean up old cache entries if cache is getting large
        if len(self.response_cache) > 1000:
            self._cleanup_cache()
    
    def _cleanup_cache(self):
        """Clean up expired cache entries"""
        current_time = time.time()
        expired_keys = []
        
        for key, data in self.response_cache.items():
            if current_time - data["timestamp"] > self.client_config.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.response_cache[key]
    
    def _update_usage_stats(self, provider_name: str, response: LLMResponse, success: bool):
        """Update usage statistics"""
        if provider_name in self.usage_stats["provider_usage"]:
            provider_stats = self.usage_stats["provider_usage"][provider_name]
            provider_stats["requests"] += 1
            
            if success and response.usage:
                tokens = response.usage.get("total_tokens", 0)
                provider_stats["tokens"] += tokens
                self.usage_stats["total_tokens"] += tokens
                
                cost = response.metadata.get("cost", 0.0)
                provider_stats["cost"] += cost
                self.usage_stats["total_cost"] += cost
    
    async def validate_providers(self) -> Dict[str, bool]:
        """Validate all configured providers"""
        results = {}
        
        for provider_name, provider in self.providers.items():
            try:
                is_valid = await provider.validate_connection()
                results[provider_name] = is_valid
                self.logger.info(f"Provider {provider_name} validation: {'✓' if is_valid else '✗'}")
            except Exception as e:
                results[provider_name] = False
                self.logger.error(f"Provider {provider_name} validation failed: {e}")
        
        return results
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())
    
    def get_provider_info(self, provider_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific provider"""
        if provider_name in self.providers:
            provider = self.providers[provider_name]
            return {
                "name": provider_name,
                "model": provider.config.model,
                "capabilities": [cap.value for cap in provider.capabilities],
                "metrics": provider.get_metrics(),
                "model_info": provider.get_model_info()
            }
        return None
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return self.usage_stats.copy()
    
    def clear_cache(self):
        """Clear response cache"""
        self.response_cache.clear()
        self.logger.info("Response cache cleared")
    
    async def shutdown(self):
        """Shutdown all providers gracefully"""
        for provider_name, provider in self.providers.items():
            try:
                if hasattr(provider, 'shutdown'):
                    await provider.shutdown()
                self.logger.info(f"Provider {provider_name} shut down")
            except Exception as e:
                self.logger.error(f"Error shutting down provider {provider_name}: {e}")


# Global LLM client instance
llm_client = LLMClient()
