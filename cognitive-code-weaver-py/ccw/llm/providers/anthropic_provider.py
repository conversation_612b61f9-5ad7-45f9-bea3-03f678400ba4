"""
Anthropic Provider - Implementation for Anthropic's <PERSON> models

Supports <PERSON> 3 (Haiku, Sonnet, Opus) and Claude 2 models with
streaming and advanced reasoning capabilities.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, AsyncGenerator

try:
    import anthropic
    from anthropic import AsyncAnthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

from .base import (
    LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMCapability,
    LLMProviderError, LLMRateLimitError, LLMAuthenticationError,
    MessageRole
)

logger = logging.getLogger(__name__)


class AnthropicProvider(LLMProvider):
    """
    Anthropic provider implementation supporting Claude models.
    
    Features:
    - <PERSON> 3 (Hai<PERSON>, Sonnet, Opus)
    - Claude 2 and <PERSON> Instant
    - Streaming responses
    - Large context windows
    - Advanced reasoning capabilities
    """
    
    def __init__(self, config: LLMConfig):
        if not ANTHROPIC_AVAILABLE:
            raise LLMProviderError(
                "Anthropic library not available. Install with: pip install anthropic",
                "anthropic"
            )
        
        super().__init__(config)
        self.client: Optional[AsyncAnthropic] = None
        
        # Model pricing (per million tokens)
        self.pricing = {
            "claude-3-opus-20240229": {"input": 15.0, "output": 75.0},
            "claude-3-sonnet-20240229": {"input": 3.0, "output": 15.0},
            "claude-3-haiku-20240307": {"input": 0.25, "output": 1.25},
            "claude-2.1": {"input": 8.0, "output": 24.0},
            "claude-2.0": {"input": 8.0, "output": 24.0},
            "claude-instant-1.2": {"input": 0.8, "output": 2.4},
        }
    
    def _initialize_provider(self) -> None:
        """Initialize Anthropic client and capabilities"""
        try:
            self.client = AsyncAnthropic(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            
            # Set capabilities
            self.capabilities = [
                LLMCapability.TEXT_GENERATION,
                LLMCapability.STREAMING,
                LLMCapability.CODE_GENERATION
            ]
            
            # Claude 3 models support image understanding
            if "claude-3" in self.config.model:
                self.capabilities.append(LLMCapability.IMAGE_UNDERSTANDING)
            
            self.logger.info(f"Anthropic provider initialized with model {self.config.model}")
            
        except Exception as e:
            raise LLMProviderError(f"Failed to initialize Anthropic provider: {e}", "anthropic")
    
    async def generate_response(self, messages: List[LLMMessage], 
                               **kwargs) -> LLMResponse:
        """Generate response using Anthropic API"""
        start_time = time.time()
        
        try:
            # Convert messages to Anthropic format
            anthropic_messages, system_message = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.config.model,
                "messages": anthropic_messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens or 4096),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "stream": False
            }
            
            # Add system message if present
            if system_message:
                request_params["system"] = system_message
            
            # Add additional parameters
            request_params.update(self.config.additional_params)
            request_params.update(kwargs)
            
            # Remove None values
            request_params = {k: v for k, v in request_params.items() if v is not None}
            
            # Make API call with retry logic
            response = await self._retry_with_backoff(
                self._make_message_completion, **request_params
            )
            
            # Process response
            llm_response = self._process_response(response, start_time)
            self._update_metrics(llm_response, success=True)
            
            return llm_response
            
        except Exception as e:
            response_time = time.time() - start_time
            error_response = LLMResponse(
                content="",
                model=self.config.model,
                provider="anthropic",
                response_time=response_time
            )
            self._update_metrics(error_response, success=False)
            
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"Anthropic API error: {e}", "anthropic")
    
    async def generate_stream(self, messages: List[LLMMessage], 
                             **kwargs) -> AsyncGenerator[str, None]:
        """Generate streaming response using Anthropic API"""
        try:
            # Convert messages to Anthropic format
            anthropic_messages, system_message = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.config.model,
                "messages": anthropic_messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens or 4096),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "stream": True
            }
            
            # Add system message if present
            if system_message:
                request_params["system"] = system_message
            
            # Add additional parameters
            request_params.update(self.config.additional_params)
            request_params.update(kwargs)
            
            # Remove None values
            request_params = {k: v for k, v in request_params.items() if v is not None}
            
            # Make streaming API call
            async with self.client.messages.stream(**request_params) as stream:
                async for text in stream.text_stream:
                    yield text
                    
        except Exception as e:
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"Anthropic streaming error: {e}", "anthropic")
    
    async def _make_message_completion(self, **params) -> Any:
        """Make message completion API call with error handling"""
        try:
            return await self.client.messages.create(**params)
        except anthropic.RateLimitError as e:
            raise LLMRateLimitError(str(e), "anthropic")
        except anthropic.AuthenticationError as e:
            raise LLMAuthenticationError(str(e), "anthropic")
        except anthropic.APIError as e:
            raise LLMProviderError(f"Anthropic API error: {e}", "anthropic")
        except Exception as e:
            raise LLMProviderError(f"Unexpected Anthropic error: {e}", "anthropic")
    
    def _convert_messages(self, messages: List[LLMMessage]) -> tuple[List[Dict[str, Any]], Optional[str]]:
        """Convert LLMMessage objects to Anthropic format"""
        anthropic_messages = []
        system_message = None
        
        for message in messages:
            if message.role == MessageRole.SYSTEM:
                # Anthropic handles system messages separately
                system_message = message.content
            else:
                anthropic_msg = {
                    "role": self._convert_role(message.role),
                    "content": message.content
                }
                anthropic_messages.append(anthropic_msg)
        
        return anthropic_messages, system_message
    
    def _convert_role(self, role: MessageRole) -> str:
        """Convert MessageRole to Anthropic role format"""
        if role == MessageRole.USER:
            return "user"
        elif role == MessageRole.ASSISTANT:
            return "assistant"
        else:
            # Anthropic doesn't have function role, treat as user
            return "user"
    
    def _process_response(self, response: Any, start_time: float) -> LLMResponse:
        """Process Anthropic API response into LLMResponse format"""
        content = ""
        if response.content:
            # Extract text content from response
            for content_block in response.content:
                if hasattr(content_block, 'text'):
                    content += content_block.text
        
        # Calculate cost
        usage = response.usage
        cost = self._calculate_cost(usage) if usage else 0.0
        
        # Update total cost metric
        self.metrics["total_cost"] += cost
        
        return LLMResponse(
            content=content,
            model=response.model,
            provider="anthropic",
            usage={
                "prompt_tokens": usage.input_tokens if usage else 0,
                "completion_tokens": usage.output_tokens if usage else 0,
                "total_tokens": (usage.input_tokens + usage.output_tokens) if usage else 0
            },
            finish_reason=response.stop_reason,
            metadata={
                "cost": cost,
                "model_version": response.model,
                "stop_sequence": response.stop_sequence
            },
            response_time=time.time() - start_time
        )
    
    def _calculate_cost(self, usage: Any) -> float:
        """Calculate cost based on token usage"""
        model_key = self.config.model
        
        # Find matching pricing model
        pricing = None
        for price_model, price_info in self.pricing.items():
            if price_model in model_key:
                pricing = price_info
                break
        
        if not pricing:
            # Default pricing for unknown models
            pricing = {"input": 8.0, "output": 24.0}
        
        input_cost = (usage.input_tokens / 1_000_000) * pricing["input"]
        output_cost = (usage.output_tokens / 1_000_000) * pricing["output"]
        
        return input_cost + output_cost
    
    async def validate_connection(self) -> bool:
        """Validate Anthropic API connection"""
        try:
            # Make a simple test request
            test_messages = [{"role": "user", "content": "Hello"}]
            response = await self.client.messages.create(
                model=self.config.model,
                messages=test_messages,
                max_tokens=10
            )
            return bool(response.content)
        except Exception as e:
            self.logger.error(f"Anthropic connection validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available Anthropic models"""
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0",
            "claude-instant-1.2"
        ]
    
    def get_model_limits(self) -> Dict[str, int]:
        """Get token limits for different models"""
        return {
            "claude-3-opus-20240229": 200000,
            "claude-3-sonnet-20240229": 200000,
            "claude-3-haiku-20240307": 200000,
            "claude-2.1": 200000,
            "claude-2.0": 100000,
            "claude-instant-1.2": 100000
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed information about the current model"""
        base_info = super().get_model_info()
        
        # Add Anthropic-specific information
        model_limits = self.get_model_limits()
        base_info.update({
            "context_window": model_limits.get(self.config.model, 100000),
            "supports_system_messages": True,
            "supports_streaming": True,
            "supports_images": "claude-3" in self.config.model
        })
        
        return base_info
