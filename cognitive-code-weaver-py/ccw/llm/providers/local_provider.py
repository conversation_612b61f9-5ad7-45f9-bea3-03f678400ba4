"""
Local Provider - Implementation for local LLM models

Supports running local models through various backends like Ollama,
llama.cpp, or Hugging Face Transformers.
"""

import asyncio
import logging
import time
import json
from typing import Any, Dict, List, Optional, AsyncGenerator

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

from .base import (
    LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMCapability,
    LLMProviderError, LLMRateLimitError, LLMAuthenticationError,
    MessageRole
)

logger = logging.getLogger(__name__)


class LocalProvider(LLMProvider):
    """
    Local provider implementation for running local LLM models.
    
    Supports:
    - Ollama (recommended)
    - llama.cpp server
    - Custom HTTP endpoints
    - Hugging Face Transformers (future)
    """
    
    def __init__(self, config: LLMConfig):
        if not HTTPX_AVAILABLE:
            raise LLMProviderError(
                "httpx library not available. Install with: pip install httpx",
                "local"
            )
        
        super().__init__(config)
        self.client: Optional[httpx.AsyncClient] = None
        self.backend_type = config.additional_params.get("backend", "ollama")
        
        # Default endpoints for different backends
        self.default_endpoints = {
            "ollama": "http://localhost:11434",
            "llamacpp": "http://localhost:8080",
            "custom": config.base_url or "http://localhost:8000"
        }
    
    def _initialize_provider(self) -> None:
        """Initialize local provider and capabilities"""
        try:
            # Set base URL based on backend type
            if not self.config.base_url:
                self.config.base_url = self.default_endpoints.get(
                    self.backend_type, 
                    "http://localhost:11434"
                )
            
            # Initialize HTTP client
            self.client = httpx.AsyncClient(
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            
            # Set capabilities (most local models support these)
            self.capabilities = [
                LLMCapability.TEXT_GENERATION,
                LLMCapability.STREAMING,
                LLMCapability.CODE_GENERATION
            ]
            
            self.logger.info(f"Local provider initialized with backend {self.backend_type}")
            
        except Exception as e:
            raise LLMProviderError(f"Failed to initialize local provider: {e}", "local")
    
    async def generate_response(self, messages: List[LLMMessage], 
                               **kwargs) -> LLMResponse:
        """Generate response using local model"""
        start_time = time.time()
        
        try:
            if self.backend_type == "ollama":
                response = await self._generate_ollama_response(messages, **kwargs)
            elif self.backend_type == "llamacpp":
                response = await self._generate_llamacpp_response(messages, **kwargs)
            else:
                response = await self._generate_custom_response(messages, **kwargs)
            
            self._update_metrics(response, success=True)
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            error_response = LLMResponse(
                content="",
                model=self.config.model,
                provider="local",
                response_time=response_time
            )
            self._update_metrics(error_response, success=False)
            
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"Local model error: {e}", "local")
    
    async def generate_stream(self, messages: List[LLMMessage], 
                             **kwargs) -> AsyncGenerator[str, None]:
        """Generate streaming response using local model"""
        try:
            if self.backend_type == "ollama":
                async for chunk in self._stream_ollama_response(messages, **kwargs):
                    yield chunk
            elif self.backend_type == "llamacpp":
                async for chunk in self._stream_llamacpp_response(messages, **kwargs):
                    yield chunk
            else:
                async for chunk in self._stream_custom_response(messages, **kwargs):
                    yield chunk
                    
        except Exception as e:
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"Local streaming error: {e}", "local")
    
    async def _generate_ollama_response(self, messages: List[LLMMessage], 
                                       **kwargs) -> LLMResponse:
        """Generate response using Ollama API"""
        start_time = time.time()
        
        # Convert messages to Ollama format
        prompt = self._convert_messages_to_prompt(messages)
        
        # Prepare request
        request_data = {
            "model": self.config.model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": kwargs.get("temperature", self.config.temperature),
                "num_predict": kwargs.get("max_tokens", self.config.max_tokens or -1)
            }
        }
        
        # Make API call
        response = await self.client.post("/api/generate", json=request_data)
        response.raise_for_status()
        
        result = response.json()
        
        return LLMResponse(
            content=result.get("response", ""),
            model=self.config.model,
            provider="local",
            usage={
                "prompt_tokens": result.get("prompt_eval_count", 0),
                "completion_tokens": result.get("eval_count", 0),
                "total_tokens": result.get("prompt_eval_count", 0) + result.get("eval_count", 0)
            },
            finish_reason="stop" if result.get("done", False) else "length",
            metadata={
                "backend": "ollama",
                "eval_duration": result.get("eval_duration", 0),
                "load_duration": result.get("load_duration", 0)
            },
            response_time=time.time() - start_time
        )
    
    async def _stream_ollama_response(self, messages: List[LLMMessage], 
                                     **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using Ollama API"""
        prompt = self._convert_messages_to_prompt(messages)
        
        request_data = {
            "model": self.config.model,
            "prompt": prompt,
            "stream": True,
            "options": {
                "temperature": kwargs.get("temperature", self.config.temperature),
                "num_predict": kwargs.get("max_tokens", self.config.max_tokens or -1)
            }
        }
        
        async with self.client.stream("POST", "/api/generate", json=request_data) as response:
            response.raise_for_status()
            
            async for line in response.aiter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        if "response" in data:
                            yield data["response"]
                    except json.JSONDecodeError:
                        continue
    
    async def _generate_llamacpp_response(self, messages: List[LLMMessage], 
                                         **kwargs) -> LLMResponse:
        """Generate response using llama.cpp server"""
        start_time = time.time()
        
        prompt = self._convert_messages_to_prompt(messages)
        
        request_data = {
            "prompt": prompt,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "n_predict": kwargs.get("max_tokens", self.config.max_tokens or 512),
            "stream": False
        }
        
        response = await self.client.post("/completion", json=request_data)
        response.raise_for_status()
        
        result = response.json()
        
        return LLMResponse(
            content=result.get("content", ""),
            model=self.config.model,
            provider="local",
            usage={
                "prompt_tokens": result.get("tokens_evaluated", 0),
                "completion_tokens": result.get("tokens_predicted", 0),
                "total_tokens": result.get("tokens_evaluated", 0) + result.get("tokens_predicted", 0)
            },
            finish_reason="stop",
            metadata={
                "backend": "llamacpp",
                "generation_settings": result.get("generation_settings", {})
            },
            response_time=time.time() - start_time
        )
    
    async def _stream_llamacpp_response(self, messages: List[LLMMessage], 
                                       **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using llama.cpp server"""
        prompt = self._convert_messages_to_prompt(messages)
        
        request_data = {
            "prompt": prompt,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "n_predict": kwargs.get("max_tokens", self.config.max_tokens or 512),
            "stream": True
        }
        
        async with self.client.stream("POST", "/completion", json=request_data) as response:
            response.raise_for_status()
            
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])  # Remove "data: " prefix
                        if "content" in data:
                            yield data["content"]
                    except json.JSONDecodeError:
                        continue
    
    async def _generate_custom_response(self, messages: List[LLMMessage], 
                                       **kwargs) -> LLMResponse:
        """Generate response using custom endpoint"""
        start_time = time.time()
        
        # Convert to standard format
        request_data = {
            "messages": [msg.to_dict() for msg in messages],
            "model": self.config.model,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "stream": False
        }
        
        response = await self.client.post("/v1/chat/completions", json=request_data)
        response.raise_for_status()
        
        result = response.json()
        choice = result["choices"][0]
        
        return LLMResponse(
            content=choice["message"]["content"],
            model=result.get("model", self.config.model),
            provider="local",
            usage=result.get("usage", {}),
            finish_reason=choice.get("finish_reason", "stop"),
            metadata={"backend": "custom"},
            response_time=time.time() - start_time
        )
    
    async def _stream_custom_response(self, messages: List[LLMMessage], 
                                     **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using custom endpoint"""
        request_data = {
            "messages": [msg.to_dict() for msg in messages],
            "model": self.config.model,
            "temperature": kwargs.get("temperature", self.config.temperature),
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "stream": True
        }
        
        async with self.client.stream("POST", "/v1/chat/completions", json=request_data) as response:
            response.raise_for_status()
            
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        if data.get("choices") and data["choices"][0].get("delta", {}).get("content"):
                            yield data["choices"][0]["delta"]["content"]
                    except json.JSONDecodeError:
                        continue
    
    def _convert_messages_to_prompt(self, messages: List[LLMMessage]) -> str:
        """Convert messages to a single prompt string"""
        prompt_parts = []
        
        for message in messages:
            if message.role == MessageRole.SYSTEM:
                prompt_parts.append(f"System: {message.content}")
            elif message.role == MessageRole.USER:
                prompt_parts.append(f"Human: {message.content}")
            elif message.role == MessageRole.ASSISTANT:
                prompt_parts.append(f"Assistant: {message.content}")
        
        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)
    
    async def validate_connection(self) -> bool:
        """Validate connection to local model server"""
        try:
            if self.backend_type == "ollama":
                response = await self.client.get("/api/tags")
            elif self.backend_type == "llamacpp":
                response = await self.client.get("/health")
            else:
                response = await self.client.get("/v1/models")
            
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Local provider connection validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available local models"""
        # This would typically query the local server
        return [
            "llama2:7b",
            "llama2:13b", 
            "codellama:7b",
            "mistral:7b",
            "neural-chat:7b"
        ]
    
    def get_model_limits(self) -> Dict[str, int]:
        """Get token limits for local models"""
        # Most local models have similar limits
        return {
            "default": 4096,
            "llama2:7b": 4096,
            "llama2:13b": 4096,
            "codellama:7b": 16384,
            "mistral:7b": 8192
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.client:
            await self.client.aclose()
