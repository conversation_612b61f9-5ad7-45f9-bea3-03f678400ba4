"""
Base LLM Provider - Abstract interface for all LLM providers

Defines the common interface that all LLM providers must implement.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from enum import Enum

logger = logging.getLogger(__name__)


class MessageRole(Enum):
    """Message roles in LLM conversations"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"


class LLMCapability(Enum):
    """LLM provider capabilities"""
    TEXT_GENERATION = "text_generation"
    FUNCTION_CALLING = "function_calling"
    STREAMING = "streaming"
    EMBEDDINGS = "embeddings"
    IMAGE_UNDERSTANDING = "image_understanding"
    CODE_GENERATION = "code_generation"


@dataclass
class LLMMessage:
    """Represents a message in an LLM conversation"""
    role: MessageRole
    content: str
    name: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary format"""
        result = {
            "role": self.role.value,
            "content": self.content
        }
        
        if self.name:
            result["name"] = self.name
        if self.function_call:
            result["function_call"] = self.function_call
        if self.metadata:
            result["metadata"] = self.metadata
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMMessage':
        """Create message from dictionary"""
        return cls(
            role=MessageRole(data["role"]),
            content=data["content"],
            name=data.get("name"),
            function_call=data.get("function_call"),
            metadata=data.get("metadata", {})
        )


@dataclass
class LLMResponse:
    """Response from an LLM provider"""
    content: str
    model: str
    provider: str
    usage: Dict[str, int] = field(default_factory=dict)
    finish_reason: Optional[str] = None
    function_calls: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    response_time: float = 0.0
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert response to dictionary"""
        return {
            "content": self.content,
            "model": self.model,
            "provider": self.provider,
            "usage": self.usage,
            "finish_reason": self.finish_reason,
            "function_calls": self.function_calls,
            "metadata": self.metadata,
            "response_time": self.response_time,
            "timestamp": self.timestamp
        }


@dataclass
class LLMConfig:
    """Configuration for LLM providers"""
    provider: str
    model: str
    api_key: str
    base_url: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    stream: bool = False
    additional_params: Dict[str, Any] = field(default_factory=dict)


class LLMError(Exception):
    """Base exception for LLM-related errors"""
    pass


class LLMProviderError(LLMError):
    """Error from LLM provider"""
    def __init__(self, message: str, provider: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class LLMRateLimitError(LLMProviderError):
    """Rate limit exceeded error"""
    def __init__(self, message: str, provider: str, retry_after: Optional[float] = None):
        super().__init__(message, provider, "rate_limit")
        self.retry_after = retry_after


class LLMAuthenticationError(LLMProviderError):
    """Authentication error"""
    def __init__(self, message: str, provider: str):
        super().__init__(message, provider, "authentication")


class LLMProvider(ABC):
    """
    Abstract base class for all LLM providers.
    
    Provides a common interface for interacting with different LLM services
    including OpenAI, Anthropic, local models, etc.
    """
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = logging.getLogger(f"ccw.llm.{config.provider}")
        self.capabilities: List[LLMCapability] = []
        self.metrics = {
            "requests_made": 0,
            "requests_failed": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0
        }
        
        # Initialize provider-specific settings
        self._initialize_provider()
    
    @abstractmethod
    def _initialize_provider(self) -> None:
        """Initialize provider-specific settings and capabilities"""
        pass
    
    @abstractmethod
    async def generate_response(self, messages: List[LLMMessage], 
                               **kwargs) -> LLMResponse:
        """
        Generate a response from the LLM.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional provider-specific parameters
            
        Returns:
            LLM response
        """
        pass
    
    @abstractmethod
    async def generate_stream(self, messages: List[LLMMessage], 
                             **kwargs) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response from the LLM.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional provider-specific parameters
            
        Yields:
            Chunks of response text
        """
        pass
    
    async def generate_embeddings(self, texts: List[str], 
                                 **kwargs) -> List[List[float]]:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: List of texts to embed
            **kwargs: Additional parameters
            
        Returns:
            List of embedding vectors
        """
        if LLMCapability.EMBEDDINGS not in self.capabilities:
            raise LLMProviderError(
                f"Provider {self.config.provider} does not support embeddings",
                self.config.provider
            )
        
        # Default implementation - should be overridden by providers that support embeddings
        raise NotImplementedError("Embeddings not implemented for this provider")
    
    async def validate_connection(self) -> bool:
        """
        Validate the connection to the LLM provider.
        
        Returns:
            True if connection is valid
        """
        try:
            test_messages = [
                LLMMessage(role=MessageRole.USER, content="Hello")
            ]
            response = await self.generate_response(test_messages)
            return bool(response.content)
        except Exception as e:
            self.logger.error(f"Connection validation failed: {e}")
            return False
    
    def supports_capability(self, capability: LLMCapability) -> bool:
        """Check if provider supports a specific capability"""
        return capability in self.capabilities
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "provider": self.config.provider,
            "model": self.config.model,
            "capabilities": [cap.value for cap in self.capabilities],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get provider usage metrics"""
        return self.metrics.copy()
    
    def _update_metrics(self, response: LLMResponse, success: bool = True):
        """Update provider metrics"""
        self.metrics["requests_made"] += 1
        
        if not success:
            self.metrics["requests_failed"] += 1
        
        if response.usage:
            total_tokens = response.usage.get("total_tokens", 0)
            self.metrics["total_tokens"] += total_tokens
        
        # Update average response time
        total_requests = self.metrics["requests_made"]
        current_avg = self.metrics["average_response_time"]
        self.metrics["average_response_time"] = (
            (current_avg * (total_requests - 1) + response.response_time) / total_requests
        )
    
    async def _retry_with_backoff(self, func, *args, **kwargs):
        """Retry function with exponential backoff"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except LLMRateLimitError as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    # Use retry_after if provided, otherwise exponential backoff
                    delay = e.retry_after or (self.config.retry_delay * (2 ** attempt))
                    self.logger.warning(f"Rate limited, retrying in {delay}s (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                else:
                    break
            except LLMProviderError as e:
                last_exception = e
                if attempt < self.config.max_retries and e.error_code != "authentication":
                    delay = self.config.retry_delay * (2 ** attempt)
                    self.logger.warning(f"Provider error, retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    break
            except Exception as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    delay = self.config.retry_delay * (2 ** attempt)
                    self.logger.warning(f"Unexpected error, retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    break
        
        # If we get here, all retries failed
        raise last_exception
    
    def __str__(self) -> str:
        return f"{self.config.provider}({self.config.model})"
    
    def __repr__(self) -> str:
        return f"LLMProvider(provider='{self.config.provider}', model='{self.config.model}')"


# Utility functions
def create_message(role: Union[str, MessageRole], content: str, **kwargs) -> LLMMessage:
    """Utility function to create an LLM message"""
    if isinstance(role, str):
        role = MessageRole(role)
    
    return LLMMessage(role=role, content=content, **kwargs)


def create_system_message(content: str) -> LLMMessage:
    """Create a system message"""
    return LLMMessage(role=MessageRole.SYSTEM, content=content)


def create_user_message(content: str) -> LLMMessage:
    """Create a user message"""
    return LLMMessage(role=MessageRole.USER, content=content)


def create_assistant_message(content: str) -> LLMMessage:
    """Create an assistant message"""
    return LLMMessage(role=MessageRole.ASSISTANT, content=content)
