"""
OpenAI Provider - Implementation for OpenAI's GPT models

Supports GPT-3.5, GPT-4, and other OpenAI models with function calling,
streaming, and embeddings.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, AsyncGenerator

try:
    import openai
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from .base import (
    LLMProvider, LLMMessage, LLMResponse, LLMConfig, LLMCapability,
    LLMProviderError, LLMRateLimitError, LLMAuthenticationError,
    MessageRole
)

logger = logging.getLogger(__name__)


class OpenAIProvider(LLMProvider):
    """
    OpenAI provider implementation supporting GPT models.
    
    Features:
    - GPT-3.5 and GPT-4 models
    - Function calling
    - Streaming responses
    - Embeddings
    - Image understanding (GPT-4V)
    """
    
    def __init__(self, config: LLMConfig):
        if not OPENAI_AVAILABLE:
            raise LLMProviderError(
                "OpenAI library not available. Install with: pip install openai",
                "openai"
            )
        
        super().__init__(config)
        self.client: Optional[AsyncOpenAI] = None
        
        # Model pricing (tokens per dollar)
        self.pricing = {
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-32k": {"input": 0.06, "output": 0.12},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-4-turbo-preview": {"input": 0.01, "output": 0.03},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            "gpt-3.5-turbo-16k": {"input": 0.003, "output": 0.004},
        }
    
    def _initialize_provider(self) -> None:
        """Initialize OpenAI client and capabilities"""
        try:
            self.client = AsyncOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            
            # Set capabilities based on model
            self.capabilities = [
                LLMCapability.TEXT_GENERATION,
                LLMCapability.STREAMING,
                LLMCapability.CODE_GENERATION
            ]
            
            # Add function calling for supported models
            if any(model in self.config.model for model in ["gpt-4", "gpt-3.5-turbo"]):
                self.capabilities.append(LLMCapability.FUNCTION_CALLING)
            
            # Add image understanding for GPT-4V
            if "vision" in self.config.model or "gpt-4v" in self.config.model:
                self.capabilities.append(LLMCapability.IMAGE_UNDERSTANDING)
            
            # Embeddings are available through separate models
            self.capabilities.append(LLMCapability.EMBEDDINGS)
            
            self.logger.info(f"OpenAI provider initialized with model {self.config.model}")
            
        except Exception as e:
            raise LLMProviderError(f"Failed to initialize OpenAI provider: {e}", "openai")
    
    async def generate_response(self, messages: List[LLMMessage], 
                               **kwargs) -> LLMResponse:
        """Generate response using OpenAI API"""
        start_time = time.time()
        
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.config.model,
                "messages": openai_messages,
                "temperature": kwargs.get("temperature", self.config.temperature),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "stream": False
            }
            
            # Add additional parameters
            request_params.update(self.config.additional_params)
            request_params.update(kwargs)
            
            # Remove None values
            request_params = {k: v for k, v in request_params.items() if v is not None}
            
            # Make API call with retry logic
            response = await self._retry_with_backoff(
                self._make_chat_completion, **request_params
            )
            
            # Process response
            llm_response = self._process_response(response, start_time)
            self._update_metrics(llm_response, success=True)
            
            return llm_response
            
        except Exception as e:
            response_time = time.time() - start_time
            error_response = LLMResponse(
                content="",
                model=self.config.model,
                provider="openai",
                response_time=response_time
            )
            self._update_metrics(error_response, success=False)
            
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"OpenAI API error: {e}", "openai")
    
    async def generate_stream(self, messages: List[LLMMessage], 
                             **kwargs) -> AsyncGenerator[str, None]:
        """Generate streaming response using OpenAI API"""
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self.config.model,
                "messages": openai_messages,
                "temperature": kwargs.get("temperature", self.config.temperature),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "stream": True
            }
            
            # Add additional parameters
            request_params.update(self.config.additional_params)
            request_params.update(kwargs)
            
            # Remove None values
            request_params = {k: v for k, v in request_params.items() if v is not None}
            
            # Make streaming API call
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            if isinstance(e, (LLMProviderError, LLMRateLimitError, LLMAuthenticationError)):
                raise
            else:
                raise LLMProviderError(f"OpenAI streaming error: {e}", "openai")
    
    async def generate_embeddings(self, texts: List[str], 
                                 model: str = "text-embedding-ada-002",
                                 **kwargs) -> List[List[float]]:
        """Generate embeddings using OpenAI's embedding models"""
        try:
            response = await self.client.embeddings.create(
                model=model,
                input=texts,
                **kwargs
            )
            
            return [embedding.embedding for embedding in response.data]
            
        except Exception as e:
            raise LLMProviderError(f"OpenAI embeddings error: {e}", "openai")
    
    async def _make_chat_completion(self, **params) -> Any:
        """Make chat completion API call with error handling"""
        try:
            return await self.client.chat.completions.create(**params)
        except openai.RateLimitError as e:
            raise LLMRateLimitError(str(e), "openai")
        except openai.AuthenticationError as e:
            raise LLMAuthenticationError(str(e), "openai")
        except openai.APIError as e:
            raise LLMProviderError(f"OpenAI API error: {e}", "openai")
        except Exception as e:
            raise LLMProviderError(f"Unexpected OpenAI error: {e}", "openai")
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Convert LLMMessage objects to OpenAI format"""
        openai_messages = []
        
        for message in messages:
            openai_msg = {
                "role": message.role.value,
                "content": message.content
            }
            
            if message.name:
                openai_msg["name"] = message.name
            
            if message.function_call:
                openai_msg["function_call"] = message.function_call
            
            openai_messages.append(openai_msg)
        
        return openai_messages
    
    def _process_response(self, response: Any, start_time: float) -> LLMResponse:
        """Process OpenAI API response into LLMResponse format"""
        choice = response.choices[0]
        message = choice.message
        
        # Calculate cost
        usage = response.usage
        cost = self._calculate_cost(usage) if usage else 0.0
        
        # Update total cost metric
        self.metrics["total_cost"] += cost
        
        return LLMResponse(
            content=message.content or "",
            model=response.model,
            provider="openai",
            usage={
                "prompt_tokens": usage.prompt_tokens if usage else 0,
                "completion_tokens": usage.completion_tokens if usage else 0,
                "total_tokens": usage.total_tokens if usage else 0
            },
            finish_reason=choice.finish_reason,
            function_calls=self._extract_function_calls(message),
            metadata={
                "cost": cost,
                "model_version": response.model,
                "system_fingerprint": getattr(response, "system_fingerprint", None)
            },
            response_time=time.time() - start_time
        )
    
    def _extract_function_calls(self, message: Any) -> List[Dict[str, Any]]:
        """Extract function calls from OpenAI message"""
        function_calls = []
        
        if hasattr(message, "function_call") and message.function_call:
            function_calls.append({
                "name": message.function_call.name,
                "arguments": message.function_call.arguments
            })
        
        if hasattr(message, "tool_calls") and message.tool_calls:
            for tool_call in message.tool_calls:
                if tool_call.type == "function":
                    function_calls.append({
                        "id": tool_call.id,
                        "name": tool_call.function.name,
                        "arguments": tool_call.function.arguments
                    })
        
        return function_calls
    
    def _calculate_cost(self, usage: Any) -> float:
        """Calculate cost based on token usage"""
        model_key = self.config.model
        
        # Find matching pricing model
        pricing = None
        for price_model, price_info in self.pricing.items():
            if price_model in model_key:
                pricing = price_info
                break
        
        if not pricing:
            # Default pricing for unknown models
            pricing = {"input": 0.01, "output": 0.03}
        
        input_cost = (usage.prompt_tokens / 1000) * pricing["input"]
        output_cost = (usage.completion_tokens / 1000) * pricing["output"]
        
        return input_cost + output_cost
    
    async def validate_connection(self) -> bool:
        """Validate OpenAI API connection"""
        try:
            # Try to list models to validate connection
            models = await self.client.models.list()
            return len(models.data) > 0
        except Exception as e:
            self.logger.error(f"OpenAI connection validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available OpenAI models"""
        return [
            "gpt-4",
            "gpt-4-32k",
            "gpt-4-turbo",
            "gpt-4-turbo-preview",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-instruct"
        ]
    
    def get_model_limits(self) -> Dict[str, int]:
        """Get token limits for different models"""
        return {
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gpt-4-turbo": 128000,
            "gpt-4-turbo-preview": 128000,
            "gpt-3.5-turbo": 4096,
            "gpt-3.5-turbo-16k": 16384,
            "gpt-3.5-turbo-instruct": 4096
        }
