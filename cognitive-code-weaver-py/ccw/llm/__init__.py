"""
LLM Integration Package - Provider-agnostic LLM client system

This package provides a unified interface for interacting with various
Large Language Model providers including OpenAI, Anthropic, and local models.
"""

from .client import LLMClient
from .providers.base import LLMProvider, LLMMessage, LLMResponse
from .providers.openai_provider import OpenAIProvider
from .providers.anthropic_provider import AnthropicProvider
from .providers.local_provider import LocalProvider

__all__ = [
    "LLMClient",
    "LLMProvider", 
    "LLMMessage",
    "LLMResponse",
    "OpenAIProvider",
    "AnthropicProvider", 
    "LocalProvider"
]
