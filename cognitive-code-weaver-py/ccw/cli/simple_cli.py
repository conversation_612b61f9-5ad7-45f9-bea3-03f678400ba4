#!/usr/bin/env python3
"""
Simple CLI Interface for Cognitive Code Weaver

A simplified CLI that works without complex dependencies.
"""

import argparse
import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def print_banner():
    """Print CLI banner"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║              Cognitive Code Weaver                      ║")
    print("║         AI-Powered Code Analysis & Reasoning           ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()


def print_version():
    """Print version information"""
    print("Cognitive Code Weaver v0.1.0")
    print()
    print("Components:")
    print("• Multi-Agent System")
    print("• LLM Integration (OpenAI, Anthropic, Local)")
    print("• Advanced Code Analysis")
    print("• Knowledge Graph")
    print("• Interactive CLI")
    print()
    print(f"Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"Platform: {sys.platform}")


def analyze_file(file_path: str, output: Optional[str] = None, format: str = "text"):
    """Analyze a single file"""
    print(f"🔍 Analyzing file: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File does not exist: {file_path}")
        return False
    
    try:
        # Simple file analysis
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Basic metrics
        lines = content.split('\n')
        total_lines = len(lines)
        non_empty_lines = len([line for line in lines if line.strip()])
        comment_lines = len([line for line in lines if line.strip().startswith('#')])
        
        # Simple analysis result
        result = {
            "file_path": file_path,
            "total_lines": total_lines,
            "non_empty_lines": non_empty_lines,
            "comment_lines": comment_lines,
            "comment_ratio": comment_lines / non_empty_lines if non_empty_lines > 0 else 0,
            "file_size": os.path.getsize(file_path),
            "language": _detect_language(file_path)
        }
        
        # Format output
        if format == "json":
            output_text = json.dumps(result, indent=2)
        else:
            output_text = f"""
📄 File Analysis Results

File: {result['file_path']}
Language: {result['language']}
Total Lines: {result['total_lines']}
Non-empty Lines: {result['non_empty_lines']}
Comment Lines: {result['comment_lines']}
Comment Ratio: {result['comment_ratio']:.3f}
File Size: {_format_file_size(result['file_size'])}
"""
        
        if output:
            with open(output, 'w') as f:
                f.write(output_text)
            print(f"✅ Results saved to: {output}")
        else:
            print(output_text)
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False


def analyze_workspace(workspace_path: str, output: Optional[str] = None, format: str = "text", max_files: Optional[int] = None):
    """Analyze a workspace"""
    print(f"🏢 Analyzing workspace: {workspace_path}")
    
    if not os.path.exists(workspace_path):
        print(f"❌ Workspace does not exist: {workspace_path}")
        return False
    
    try:
        # Find all code files
        code_extensions = {'.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.rb', '.go', '.rs', '.php'}
        files = []
        
        for root, dirs, filenames in os.walk(workspace_path):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for filename in filenames:
                if any(filename.endswith(ext) for ext in code_extensions):
                    files.append(os.path.join(root, filename))
        
        if max_files:
            files = files[:max_files]
        
        print(f"Found {len(files)} code files")
        
        # Analyze files
        total_lines = 0
        total_files = len(files)
        languages = {}
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                lines = len(content.split('\n'))
                total_lines += lines
                
                lang = _detect_language(file_path)
                languages[lang] = languages.get(lang, 0) + 1
                
            except Exception as e:
                print(f"⚠️  Error analyzing {file_path}: {e}")
        
        # Create result
        result = {
            "workspace_path": workspace_path,
            "total_files": total_files,
            "total_lines": total_lines,
            "languages": languages,
            "average_file_size": total_lines / total_files if total_files > 0 else 0
        }
        
        # Format output
        if format == "json":
            output_text = json.dumps(result, indent=2)
        else:
            output_text = f"""
🏢 Workspace Analysis Results

Workspace: {result['workspace_path']}
Total Files: {result['total_files']}
Total Lines: {result['total_lines']:,}
Average File Size: {result['average_file_size']:.1f} lines

Languages:
"""
            for lang, count in result['languages'].items():
                output_text += f"  {lang}: {count} files\n"
        
        if output:
            with open(output, 'w') as f:
                f.write(output_text)
            print(f"✅ Results saved to: {output}")
        else:
            print(output_text)
        
        return True
        
    except Exception as e:
        print(f"❌ Workspace analysis failed: {e}")
        return False


def _detect_language(file_path: str) -> str:
    """Detect programming language from file extension"""
    ext = Path(file_path).suffix.lower()
    
    language_map = {
        '.py': 'Python',
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.java': 'Java',
        '.cpp': 'C++',
        '.c': 'C',
        '.h': 'C/C++ Header',
        '.cs': 'C#',
        '.rb': 'Ruby',
        '.go': 'Go',
        '.rs': 'Rust',
        '.php': 'PHP',
        '.html': 'HTML',
        '.css': 'CSS',
        '.sql': 'SQL',
        '.sh': 'Shell',
        '.yaml': 'YAML',
        '.yml': 'YAML',
        '.json': 'JSON',
        '.xml': 'XML',
        '.md': 'Markdown'
    }
    
    return language_map.get(ext, 'Unknown')


def _format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def interactive_mode():
    """Start interactive mode"""
    print_banner()
    print("Interactive Mode - Type 'help' for commands, 'exit' to quit")
    print()
    
    while True:
        try:
            user_input = input("ccw> ").strip()
            
            if not user_input:
                continue
            
            parts = user_input.split()
            cmd = parts[0].lower()
            
            if cmd in ['exit', 'quit', 'q']:
                break
            elif cmd == 'help':
                print("""
Available Commands:
• analyze file <path> - Analyze a single file
• analyze workspace <path> - Analyze a workspace
• version - Show version information
• help - Show this help
• exit - Exit interactive mode

Examples:
• analyze file ./src/main.py
• analyze workspace ./src
• version
""")
            elif cmd == 'version':
                print_version()
            elif cmd == 'analyze' and len(parts) >= 3:
                if parts[1] == 'file':
                    analyze_file(parts[2])
                elif parts[1] == 'workspace':
                    analyze_workspace(parts[2])
                else:
                    print("❌ Invalid analyze command. Use 'analyze file <path>' or 'analyze workspace <path>'")
            else:
                print("❌ Unknown command. Type 'help' for available commands.")
                
        except KeyboardInterrupt:
            print("\nUse 'exit' to quit")
        except EOFError:
            break
    
    print("Goodbye!")


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="Cognitive Code Weaver - AI-powered code analysis and reasoning system",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Version command
    version_parser = subparsers.add_parser('version', help='Show version information')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze code')
    analyze_subparsers = analyze_parser.add_subparsers(dest='analyze_type', help='Analysis type')
    
    # Analyze file
    file_parser = analyze_subparsers.add_parser('file', help='Analyze a single file')
    file_parser.add_argument('path', help='File path to analyze')
    file_parser.add_argument('--output', '-o', help='Output file')
    file_parser.add_argument('--format', '-f', default='text', choices=['text', 'json'], help='Output format')
    
    # Analyze workspace
    workspace_parser = analyze_subparsers.add_parser('workspace', help='Analyze a workspace')
    workspace_parser.add_argument('path', nargs='?', default='.', help='Workspace path to analyze')
    workspace_parser.add_argument('--output', '-o', help='Output file')
    workspace_parser.add_argument('--format', '-f', default='text', choices=['text', 'json'], help='Output format')
    workspace_parser.add_argument('--max-files', type=int, help='Maximum files to analyze')
    
    # Interactive command
    interactive_parser = subparsers.add_parser('interactive', help='Start interactive mode')
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        print_banner()
        parser.print_help()
        return
    
    # Execute commands
    if args.command == 'version':
        print_version()
    elif args.command == 'analyze':
        if args.analyze_type == 'file':
            analyze_file(args.path, args.output, args.format)
        elif args.analyze_type == 'workspace':
            analyze_workspace(args.path, args.output, args.format, args.max_files)
        else:
            analyze_parser.print_help()
    elif args.command == 'interactive':
        interactive_mode()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
