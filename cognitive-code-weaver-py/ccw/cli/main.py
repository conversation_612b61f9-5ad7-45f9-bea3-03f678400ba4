"""
Main CLI Interface for Cognitive Code Weaver

Provides a rich command-line interface for interacting with the system.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.logging import <PERSON><PERSON>andler
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel

from ccw.core.config import config, init_config
from ccw.core.registry import agent_registry
from ccw.core.message_bus import message_bus
from ccw.core.agent import create_agent_task, create_agent_context
from ccw.agents.master_agent import MasterAgent
from ccw.llm.client import llm_client
from ccw.analysis.workspace import WorkspaceAnalyzer
from ccw.analysis.parser import CodeParser

# Initialize CLI app
app = typer.Typer(
    name="ccw",
    help="Cognitive Code Weaver - AI-powered code analysis and reasoning system",
    add_completion=False,
    no_args_is_help=True
)

# Rich console for beautiful output
console = Console()

# Global state
master_agent: Optional[MasterAgent] = None


def setup_logging(level: str = "INFO"):
    """Setup logging with Rich handler"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(message)s",
        datefmt="[%X]",
        handlers=[RichHandler(console=console, rich_tracebacks=True)]
    )


async def initialize_system(config_file: Optional[str] = None):
    """Initialize the Cognitive Code Weaver system"""
    global master_agent
    
    # Initialize configuration
    if config_file:
        init_config(config_file=config_file)
    else:
        init_config()
    
    # Setup logging
    setup_logging(config.get("logging.level", "INFO"))
    
    # Start message bus
    await message_bus.start()
    
    # Load plugins
    agent_registry.load_plugins()
    
    # Create and register master agent
    master_agent = MasterAgent()
    
    console.print("[green]✓[/green] Cognitive Code Weaver initialized successfully")


async def shutdown_system():
    """Shutdown the system gracefully"""
    global master_agent
    
    if master_agent:
        await master_agent.shutdown()
    
    await agent_registry.shutdown_all_agents()
    await message_bus.stop()
    
    console.print("[yellow]System shutdown complete[/yellow]")


@app.command()
def init(
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file path"),
    force: bool = typer.Option(False, "--force", "-f", help="Force initialization even if config exists")
):
    """Initialize Cognitive Code Weaver configuration"""
    
    config_path = Path(config_file) if config_file else Path("config/ccw.yaml")
    
    if config_path.exists() and not force:
        console.print(f"[yellow]Configuration file already exists: {config_path}[/yellow]")
        console.print("Use --force to overwrite")
        return
    
    # Create config directory
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save default configuration
    from ccw.core.config import Config
    default_config = Config()
    default_config.save_to_file(str(config_path))
    
    console.print(f"[green]✓[/green] Configuration initialized: {config_path}")
    console.print("\n[bold]Next steps:[/bold]")
    console.print("1. Edit the configuration file to set your API keys")
    console.print("2. Run 'ccw status' to check system status")
    console.print("3. Run 'ccw analyze <path>' to analyze a codebase")


@app.command()
def analyze(
    path: str = typer.Argument(..., help="Path to analyze (file or directory)"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file for results"),
    format: str = typer.Option("text", "--format", "-f", help="Output format (text, json, yaml)"),
    max_files: Optional[int] = typer.Option(None, "--max-files", help="Maximum files to analyze"),
    include_metrics: bool = typer.Option(True, "--metrics/--no-metrics", help="Include code metrics"),
    include_dependencies: bool = typer.Option(True, "--deps/--no-deps", help="Include dependency analysis"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Analyze code using advanced code analysis system"""

    async def run_analysis():
        await initialize_system(config_file)

        try:
            console.print(f"[cyan]🔍 Analyzing: {path}[/cyan]")

            # Check if path exists
            if not os.path.exists(path):
                console.print(f"[red]✗ Path does not exist: {path}[/red]")
                return

            # Initialize workspace analyzer
            workspace_analyzer = WorkspaceAnalyzer()

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console
            ) as progress:

                if os.path.isfile(path):
                    # Single file analysis
                    analysis_task = progress.add_task("Analyzing file...", total=1)

                    result = workspace_analyzer.analyze_file(path)

                    progress.update(analysis_task, advance=1, description="File analysis complete!")

                    # Display results
                    _display_file_analysis_results(result, format, output)

                else:
                    # Workspace analysis
                    analysis_task = progress.add_task("Analyzing workspace...", total=None)

                    result = workspace_analyzer.analyze_workspace(
                        path,
                        max_files=max_files
                    )

                    progress.update(analysis_task, description="Workspace analysis complete!")

                    # Display results
                    _display_workspace_analysis_results(result, format, output, include_metrics, include_dependencies)

        except Exception as e:
            console.print(f"[red]✗ Analysis failed: {e}[/red]")
            import traceback
            console.print(f"[red]{traceback.format_exc()}[/red]")
        finally:
            await shutdown_system()

    asyncio.run(run_analysis())


@app.command()
def ask(
    question: str = typer.Argument(..., help="Question to ask about the codebase"),
    workspace: Optional[str] = typer.Option(None, "--workspace", "-w", help="Workspace path"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Ask a question about the codebase"""
    
    async def run_query():
        await initialize_system(config_file)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task = progress.add_task("Processing question...", total=None)
            
            try:
                # Create query task
                query_task = create_agent_task(
                    task_type="query_orchestration",
                    description="Process user question",
                    data={"query": question}
                )
                
                # Create context
                context = create_agent_context(
                    session_id=f"query_{int(asyncio.get_event_loop().time())}",
                    user_query=question,
                    workspace_path=workspace or "."
                )
                
                # Execute query
                result = await master_agent.execute_with_monitoring(query_task, context)
                
                progress.update(task, description="Question processed!")
                
                # Display results
                if result.status.value == "completed":
                    console.print("\n[green]✓ Question processed successfully[/green]")
                    _display_query_results(result.data)
                else:
                    console.print(f"[red]✗ Query failed: {result.error}[/red]")
                    
            except Exception as e:
                console.print(f"[red]✗ Error processing question: {e}[/red]")
            finally:
                await shutdown_system()
    
    asyncio.run(run_query())


@app.command()
def status(
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Show system status"""
    
    async def show_status():
        await initialize_system(config_file)
        
        try:
            # Get system status
            status_task = create_agent_task(
                task_type="system_management",
                description="Get system status",
                data={"command": "status"}
            )
            
            context = create_agent_context(
                session_id=f"status_{int(asyncio.get_event_loop().time())}"
            )
            
            result = await master_agent.execute_with_monitoring(status_task, context)
            
            if result.status.value == "completed":
                _display_system_status(result.data)
            else:
                console.print(f"[red]✗ Failed to get status: {result.error}[/red]")
                
        except Exception as e:
            console.print(f"[red]✗ Error getting status: {e}[/red]")
        finally:
            await shutdown_system()
    
    asyncio.run(show_status())


@app.command()
def agents(
    action: str = typer.Argument("list", help="Action: list, enable, disable"),
    agent_name: Optional[str] = typer.Option(None, "--agent", "-a", help="Agent name"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Manage agents"""
    
    async def manage_agents():
        await initialize_system(config_file)
        
        try:
            if action == "list":
                agents_info = agent_registry.list_agents()
                _display_agents_list(agents_info)
            
            elif action == "enable" and agent_name:
                agent_registry.enable_agent(agent_name)
                console.print(f"[green]✓ Agent '{agent_name}' enabled[/green]")
            
            elif action == "disable" and agent_name:
                agent_registry.disable_agent(agent_name)
                console.print(f"[yellow]Agent '{agent_name}' disabled[/yellow]")
            
            else:
                console.print("[red]Invalid action or missing agent name[/red]")
                
        except Exception as e:
            console.print(f"[red]✗ Error managing agents: {e}[/red]")
        finally:
            await shutdown_system()
    
    asyncio.run(manage_agents())


@app.command()
def llm(
    action: str = typer.Argument("test", help="Action: test, providers, validate"),
    text: Optional[str] = typer.Option(None, "--text", "-t", help="Text to process"),
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="Specific provider to use"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Test and manage LLM integration"""

    async def run_llm_command():
        await initialize_system(config_file)

        try:
            if action == "test":
                await _test_llm_basic(text or "Hello, how are you?", provider)
            elif action == "providers":
                await _show_llm_providers()
            elif action == "validate":
                await _validate_llm_providers()
            elif action == "stream":
                await _test_llm_streaming(text or "Write a Python function to calculate fibonacci numbers", provider)
            else:
                console.print(f"[red]Unknown action: {action}[/red]")
                console.print("Available actions: test, providers, validate, stream")

        except Exception as e:
            console.print(f"[red]✗ LLM command failed: {e}[/red]")
        finally:
            await shutdown_system()

    asyncio.run(run_llm_command())


@app.command()
def interactive(
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Start interactive mode"""

    async def run_interactive():
        await initialize_system(config_file)

        console.print("[bold blue]Cognitive Code Weaver - Interactive Mode[/bold blue]")
        console.print("Type 'help' for commands, 'exit' to quit\n")

        try:
            while True:
                try:
                    user_input = console.input("[bold cyan]ccw>[/bold cyan] ")

                    if user_input.lower() in ['exit', 'quit', 'q']:
                        break
                    elif user_input.lower() == 'help':
                        _show_interactive_help()
                    elif user_input.strip():
                        await _process_interactive_command(user_input)

                except KeyboardInterrupt:
                    console.print("\n[yellow]Use 'exit' to quit[/yellow]")
                except EOFError:
                    break

        finally:
            await shutdown_system()

    asyncio.run(run_interactive())


@app.command()
def knowledge(
    action: str = typer.Argument("query", help="Action: query, build, export, stats"),
    query: Optional[str] = typer.Option(None, "--query", "-q", help="Knowledge graph query"),
    workspace: Optional[str] = typer.Option(None, "--workspace", "-w", help="Workspace path"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("text", "--format", "-f", help="Output format (text, json, yaml)"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Manage knowledge graph operations"""

    async def run_knowledge_command():
        await initialize_system(config_file)

        try:
            if action == "query" and query:
                await _query_knowledge_graph(query, workspace, format, output)
            elif action == "build":
                await _build_knowledge_graph(workspace or ".")
            elif action == "export":
                await _export_knowledge_graph(workspace or ".", format, output)
            elif action == "stats":
                await _show_knowledge_stats(workspace or ".")
            else:
                console.print(f"[red]Invalid action or missing parameters[/red]")
                console.print("Available actions: query, build, export, stats")

        except Exception as e:
            console.print(f"[red]✗ Knowledge command failed: {e}[/red]")
        finally:
            await shutdown_system()

    asyncio.run(run_knowledge_command())


@app.command()
def debug(
    workspace: str = typer.Argument(".", help="Workspace path to debug"),
    issue_type: Optional[str] = typer.Option(None, "--type", "-t", help="Issue type to focus on"),
    severity: str = typer.Option("all", "--severity", "-s", help="Severity level (low, medium, high, all)"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Debug and analyze code issues"""

    async def run_debug():
        await initialize_system(config_file)

        try:
            console.print(f"[cyan]🐛 Debugging workspace: {workspace}[/cyan]")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:

                task = progress.add_task("Running debug analysis...", total=None)

                # Create debug task
                debug_task = create_agent_task(
                    task_type="debug_analysis",
                    description="Debug code issues",
                    data={
                        "workspace_path": workspace,
                        "issue_type": issue_type,
                        "severity": severity
                    }
                )

                context = create_agent_context(
                    session_id=f"debug_{int(asyncio.get_event_loop().time())}",
                    workspace_path=workspace
                )

                result = await master_agent.execute_with_monitoring(debug_task, context)

                progress.update(task, description="Debug analysis complete!")

                if result.status.value == "completed":
                    _display_debug_results(result.data, output)
                else:
                    console.print(f"[red]✗ Debug analysis failed: {result.error}[/red]")

        except Exception as e:
            console.print(f"[red]✗ Debug command failed: {e}[/red]")
        finally:
            await shutdown_system()

    asyncio.run(run_debug())


@app.command()
def metrics(
    workspace: str = typer.Argument(".", help="Workspace path"),
    metric_type: str = typer.Option("all", "--type", "-t", help="Metric type (complexity, quality, size, all)"),
    threshold: Optional[float] = typer.Option(None, "--threshold", help="Threshold for filtering"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("text", "--format", "-f", help="Output format (text, json, yaml)"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Configuration file")
):
    """Calculate and display code metrics"""

    async def run_metrics():
        await initialize_system(config_file)

        try:
            console.print(f"[cyan]📊 Calculating metrics for: {workspace}[/cyan]")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:

                task = progress.add_task("Calculating metrics...", total=None)

                # Create metrics task
                metrics_task = create_agent_task(
                    task_type="metrics_calculation",
                    description="Calculate code metrics",
                    data={
                        "workspace_path": workspace,
                        "metric_type": metric_type,
                        "threshold": threshold
                    }
                )

                context = create_agent_context(
                    session_id=f"metrics_{int(asyncio.get_event_loop().time())}",
                    workspace_path=workspace
                )

                result = await master_agent.execute_with_monitoring(metrics_task, context)

                progress.update(task, description="Metrics calculation complete!")

                if result.status.value == "completed":
                    _display_metrics_results(result.data, format, output)
                else:
                    console.print(f"[red]✗ Metrics calculation failed: {result.error}[/red]")

        except Exception as e:
            console.print(f"[red]✗ Metrics command failed: {e}[/red]")
        finally:
            await shutdown_system()

    asyncio.run(run_metrics())


def _display_file_analysis_results(result: dict, format: str, output: Optional[str]):
    """Display single file analysis results"""
    if "errors" in result and result["errors"]:
        console.print(f"[red]✗ Analysis failed: {result['errors'][0]}[/red]")
        return

    if format == "json":
        import json
        output_text = json.dumps(result, indent=2)
    elif format == "yaml":
        import yaml
        output_text = yaml.dump(result, default_flow_style=False)
    else:
        # Text format
        output_text = f"""
📄 File Analysis Results

File: {result['file_path']}
Language: {result['language']}

📊 Metrics:
  Lines of Code: {result['metrics']['size']['lines_of_code']}
  Complexity: {result['metrics']['complexity']['cyclomatic_complexity']}
  Maintainability: {result['metrics']['maintainability_index']:.2f}
  Comment Ratio: {result['metrics']['quality']['comment_ratio']:.3f}

🔤 Symbols Found: {len(result['symbols'])}
  Functions: {len([s for s in result['symbols'] if s['type'] == 'function'])}
  Classes: {len([s for s in result['symbols'] if s['type'] == 'class'])}
  Variables: {len([s for s in result['symbols'] if s['type'] == 'variable'])}

🔗 Dependencies: {len(result['dependency_graph']['edges'])} edges
"""

    if output:
        with open(output, 'w') as f:
            f.write(output_text)
        console.print(f"[green]✓ Results saved to: {output}[/green]")
    else:
        console.print(output_text)


def _display_workspace_analysis_results(result, format: str, output: Optional[str],
                                       include_metrics: bool, include_dependencies: bool):
    """Display workspace analysis results"""
    if format == "json":
        import json
        output_text = json.dumps(result.to_dict(), indent=2)
    elif format == "yaml":
        import yaml
        output_text = yaml.dump(result.to_dict(), default_flow_style=False)
    else:
        # Text format
        summary = result.to_dict()["summary"]
        workspace_metrics = result.workspace_metrics
        quality_report = result.quality_report

        output_text = f"""
🏢 Workspace Analysis Results

📁 Workspace: {result.workspace_path}

📊 Summary:
  Total Files: {summary['total_files']}
  Analyzed Files: {summary['analyzed_files']}
  Skipped Files: {summary['skipped_files']}
  Languages: {', '.join(summary['languages'].keys())}
"""

        if include_metrics and workspace_metrics:
            output_text += f"""
📈 Workspace Metrics:
  Total Lines of Code: {workspace_metrics.get('total_lines_of_code', 0):,}
  Total Functions: {workspace_metrics.get('total_functions', 0)}
  Total Classes: {workspace_metrics.get('total_classes', 0)}
  Average Complexity: {workspace_metrics.get('average_complexity', 0):.2f}
  Average Maintainability: {workspace_metrics.get('average_maintainability', 0):.2f}
"""

        if quality_report:
            output_text += f"""
🎯 Quality Report:
  Overall Grade: {quality_report.get('overall_grade', 'N/A')}
  Average Comment Ratio: {quality_report.get('average_comment_ratio', 0):.3f}
  High Complexity Files: {len(quality_report.get('high_complexity_files', []))}
  Low Maintainability Files: {len(quality_report.get('low_maintainability_files', []))}
"""

        if include_dependencies and result.dependency_graph:
            dep_metrics = result.dependency_graph.calculate_coupling_metrics()
            output_text += f"""
🔗 Dependency Analysis:
  Total Nodes: {dep_metrics['total_nodes']}
  Total Edges: {dep_metrics['total_edges']}
  Circular Dependencies: {dep_metrics['circular_dependencies']}
  Coupling Ratio: {dep_metrics['coupling_ratio']:.3f}
"""

        if result.errors:
            output_text += f"""
⚠️  Errors ({len(result.errors)}):
"""
            for error in result.errors[:5]:  # Show first 5 errors
                output_text += f"  • {error}\n"

    if output:
        with open(output, 'w') as f:
            f.write(output_text)
        console.print(f"[green]✓ Results saved to: {output}[/green]")
    else:
        console.print(output_text)


def _display_analysis_results(data: dict):
    """Display analysis results in a formatted way (legacy)"""
    console.print("\n[bold]Analysis Results:[/bold]")

    # Create a panel with results
    content = []
    if "summary" in data:
        content.append(f"Summary: {data['summary']}")
    if "files_analyzed" in data:
        content.append(f"Files analyzed: {data['files_analyzed']}")
    if "issues_found" in data:
        content.append(f"Issues found: {data['issues_found']}")

    panel = Panel("\n".join(content), title="Analysis Summary", border_style="green")
    console.print(panel)


def _display_query_results(data: dict):
    """Display query results in a formatted way"""
    console.print("\n[bold]Answer:[/bold]")
    
    answer = data.get("answer", "No answer provided")
    console.print(Panel(answer, border_style="blue"))
    
    if data.get("suggestions"):
        console.print("\n[bold]Suggestions:[/bold]")
        for suggestion in data["suggestions"]:
            console.print(f"• {suggestion}")
    
    if data.get("confidence"):
        confidence = data["confidence"]
        color = "green" if confidence > 0.8 else "yellow" if confidence > 0.5 else "red"
        console.print(f"\n[{color}]Confidence: {confidence:.2%}[/{color}]")


def _display_system_status(data: dict):
    """Display system status in a formatted table"""
    table = Table(title="System Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details")
    
    table.add_row("System", data.get("system_status", "unknown"), "")
    table.add_row("Active Queries", str(data.get("active_queries", 0)), "")
    table.add_row("Registered Agents", str(data.get("registered_agents", 0)), "")
    table.add_row("Available Agents", str(data.get("available_agents", 0)), "")
    
    console.print(table)


def _display_agents_list(agents_info: dict):
    """Display list of agents in a formatted table"""
    table = Table(title="Registered Agents")
    table.add_column("Agent ID", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Capabilities")
    
    for agent_id, info in agents_info.items():
        status = info.get("status", "unknown")
        capabilities = ", ".join(info.get("capabilities", []))
        table.add_row(agent_id, status, capabilities)
    
    console.print(table)


def _show_interactive_help():
    """Show help for interactive mode"""
    help_text = """
[bold]Available Commands:[/bold]

• [cyan]analyze <path>[/cyan] - Analyze a codebase
• [cyan]ask <question>[/cyan] - Ask a question about code
• [cyan]status[/cyan] - Show system status
• [cyan]agents[/cyan] - List all agents
• [cyan]llm <text>[/cyan] - Test LLM with text
• [cyan]knowledge query <query>[/cyan] - Query knowledge graph
• [cyan]debug <path>[/cyan] - Debug code issues
• [cyan]metrics <path>[/cyan] - Calculate code metrics
• [cyan]help[/cyan] - Show this help
• [cyan]exit[/cyan] - Exit interactive mode

[bold]Examples:[/bold]
• analyze ./src
• ask "How does authentication work?"
• llm "Explain this code"
• knowledge query "find all classes"
• debug ./src
• metrics ./src
• status
"""
    console.print(help_text)


async def _test_llm_basic(text: str, provider: Optional[str]):
    """Test basic LLM text generation"""
    console.print(f"[cyan]Testing LLM with text: {text[:50]}...[/cyan]")

    try:
        from ccw.llm.providers.base import create_user_message

        response = await llm_client.generate_response(
            text,
            provider=provider,
            max_tokens=200,
            temperature=0.7
        )

        console.print(f"\n[green]✓ LLM Response:[/green]")
        console.print(Panel(response.content, title=f"Response from {response.provider}", border_style="green"))

        console.print(f"\n[blue]Response Details:[/blue]")
        console.print(f"  Model: {response.model}")
        console.print(f"  Provider: {response.provider}")
        console.print(f"  Tokens: {response.usage.get('total_tokens', 'N/A')}")
        console.print(f"  Response Time: {response.response_time:.2f}s")

        if response.metadata.get('cost'):
            console.print(f"  Cost: ${response.metadata['cost']:.4f}")

    except Exception as e:
        console.print(f"[red]✗ LLM test failed: {e}[/red]")


async def _test_llm_streaming(text: str, provider: Optional[str]):
    """Test LLM streaming"""
    console.print(f"[cyan]Testing LLM streaming with: {text[:50]}...[/cyan]")

    try:
        console.print(f"\n[green]Streaming Response:[/green]")

        response_text = ""
        async for chunk in llm_client.generate_stream(
            text,
            provider=provider,
            max_tokens=300
        ):
            response_text += chunk
            console.print(chunk, end="", flush=True)

        console.print(f"\n\n[blue]Streaming complete ({len(response_text)} characters)[/blue]")

    except Exception as e:
        console.print(f"[red]✗ LLM streaming test failed: {e}[/red]")


async def _show_llm_providers():
    """Show available LLM providers"""
    console.print("[cyan]Available LLM Providers:[/cyan]")

    providers = llm_client.get_available_providers()

    table = Table(title="LLM Providers")
    table.add_column("Provider", style="cyan")
    table.add_column("Model", style="green")
    table.add_column("Capabilities")
    table.add_column("Status")

    for provider_name in providers:
        info = llm_client.get_provider_info(provider_name)
        if info:
            capabilities = ", ".join(info['capabilities'])
            table.add_row(
                provider_name,
                info['model'],
                capabilities,
                "Available"
            )

    console.print(table)

    # Show usage statistics
    stats = llm_client.get_usage_stats()
    console.print(f"\n[blue]Usage Statistics:[/blue]")
    console.print(f"  Total Requests: {stats['total_requests']}")
    console.print(f"  Successful: {stats['successful_requests']}")
    console.print(f"  Failed: {stats['failed_requests']}")
    console.print(f"  Total Tokens: {stats['total_tokens']}")
    console.print(f"  Total Cost: ${stats['total_cost']:.4f}")


async def _validate_llm_providers():
    """Validate LLM provider connections"""
    console.print("[cyan]Validating LLM Providers...[/cyan]")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("Validating providers...", total=None)

        validation_results = await llm_client.validate_providers()

        progress.update(task, description="Validation complete!")

    console.print("\n[blue]Validation Results:[/blue]")
    for provider, is_valid in validation_results.items():
        status = "[green]✓ Valid[/green]" if is_valid else "[red]✗ Invalid[/red]"
        console.print(f"  {provider}: {status}")


async def _process_interactive_command(command: str):
    """Process a command in interactive mode"""
    parts = command.strip().split()
    if not parts:
        return

    cmd = parts[0].lower()

    if cmd == "analyze" and len(parts) > 1:
        path = parts[1]
        console.print(f"[yellow]Analyzing {path}...[/yellow]")
        # Create simplified analysis task
        try:
            workspace_analyzer = WorkspaceAnalyzer()
            if os.path.isfile(path):
                result = workspace_analyzer.analyze_file(path)
                _display_file_analysis_results(result, "text", None)
            else:
                result = workspace_analyzer.analyze_workspace(path, max_files=50)
                _display_workspace_analysis_results(result, "text", None, True, True)
        except Exception as e:
            console.print(f"[red]Analysis error: {e}[/red]")

    elif cmd == "ask" and len(parts) > 1:
        question = " ".join(parts[1:])
        console.print(f"[yellow]Processing question: {question}[/yellow]")
        try:
            query_task = create_agent_task(
                task_type="query_orchestration",
                description="Process user question",
                data={"query": question}
            )

            context = create_agent_context(
                session_id=f"interactive_query_{int(asyncio.get_event_loop().time())}",
                user_query=question,
                workspace_path="."
            )

            result = await master_agent.execute_with_monitoring(query_task, context)

            if result.status.value == "completed":
                _display_query_results(result.data)
            else:
                console.print(f"[red]Query failed: {result.error}[/red]")
        except Exception as e:
            console.print(f"[red]Query error: {e}[/red]")

    elif cmd == "llm" and len(parts) > 1:
        llm_text = " ".join(parts[1:])
        await _test_llm_basic(llm_text, None)

    elif cmd == "knowledge" and len(parts) > 2:
        action = parts[1].lower()
        if action == "query":
            query = " ".join(parts[2:])
            await _query_knowledge_graph(query, ".", "text", None)
        else:
            console.print("[red]Knowledge command requires 'query' action[/red]")

    elif cmd == "debug" and len(parts) > 1:
        path = parts[1]
        console.print(f"[yellow]Debugging {path}...[/yellow]")
        try:
            debug_task = create_agent_task(
                task_type="debug_analysis",
                description="Debug code issues",
                data={"workspace_path": path}
            )

            context = create_agent_context(
                session_id=f"interactive_debug_{int(asyncio.get_event_loop().time())}",
                workspace_path=path
            )

            result = await master_agent.execute_with_monitoring(debug_task, context)

            if result.status.value == "completed":
                _display_debug_results(result.data, None)
            else:
                console.print(f"[red]Debug failed: {result.error}[/red]")
        except Exception as e:
            console.print(f"[red]Debug error: {e}[/red]")

    elif cmd == "metrics" and len(parts) > 1:
        path = parts[1]
        console.print(f"[yellow]Calculating metrics for {path}...[/yellow]")
        try:
            metrics_task = create_agent_task(
                task_type="metrics_calculation",
                description="Calculate code metrics",
                data={"workspace_path": path}
            )

            context = create_agent_context(
                session_id=f"interactive_metrics_{int(asyncio.get_event_loop().time())}",
                workspace_path=path
            )

            result = await master_agent.execute_with_monitoring(metrics_task, context)

            if result.status.value == "completed":
                _display_metrics_results(result.data, "text", None)
            else:
                console.print(f"[red]Metrics calculation failed: {result.error}[/red]")
        except Exception as e:
            console.print(f"[red]Metrics error: {e}[/red]")

    elif cmd == "status":
        console.print("[yellow]Getting system status...[/yellow]")
        try:
            status_task = create_agent_task(
                task_type="system_management",
                description="Get system status",
                data={"command": "status"}
            )

            context = create_agent_context(
                session_id=f"interactive_status_{int(asyncio.get_event_loop().time())}"
            )

            result = await master_agent.execute_with_monitoring(status_task, context)

            if result.status.value == "completed":
                _display_system_status(result.data)
            else:
                console.print(f"[red]Status failed: {result.error}[/red]")
        except Exception as e:
            console.print(f"[red]Status error: {e}[/red]")

    elif cmd == "agents":
        agents_info = agent_registry.list_agents()
        _display_agents_list(agents_info)

    else:
        console.print(f"[red]Unknown command: {cmd}[/red]")
        console.print("Type 'help' for available commands")


async def _query_knowledge_graph(query: str, workspace: Optional[str], format: str, output: Optional[str]):
    """Query the knowledge graph"""
    console.print(f"[cyan]🧠 Querying knowledge graph: {query}[/cyan]")

    try:
        # Create knowledge query task
        kg_task = create_agent_task(
            task_type="knowledge_query",
            description="Query knowledge graph",
            data={
                "query": query,
                "workspace_path": workspace or "."
            }
        )

        context = create_agent_context(
            session_id=f"kg_query_{int(asyncio.get_event_loop().time())}",
            workspace_path=workspace or "."
        )

        result = await master_agent.execute_with_monitoring(kg_task, context)

        if result.status.value == "completed":
            _display_knowledge_results(result.data, format, output)
        else:
            console.print(f"[red]✗ Knowledge query failed: {result.error}[/red]")

    except Exception as e:
        console.print(f"[red]✗ Knowledge query error: {e}[/red]")


async def _build_knowledge_graph(workspace: str):
    """Build knowledge graph for workspace"""
    console.print(f"[cyan]🏗️ Building knowledge graph for: {workspace}[/cyan]")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("Building knowledge graph...", total=None)

        try:
            # Create knowledge build task
            kg_task = create_agent_task(
                task_type="knowledge_build",
                description="Build knowledge graph",
                data={"workspace_path": workspace}
            )

            context = create_agent_context(
                session_id=f"kg_build_{int(asyncio.get_event_loop().time())}",
                workspace_path=workspace
            )

            result = await master_agent.execute_with_monitoring(kg_task, context)

            progress.update(task, description="Knowledge graph built!")

            if result.status.value == "completed":
                console.print("[green]✓ Knowledge graph built successfully[/green]")
                _display_knowledge_stats(result.data)
            else:
                console.print(f"[red]✗ Knowledge graph build failed: {result.error}[/red]")

        except Exception as e:
            console.print(f"[red]✗ Knowledge graph build error: {e}[/red]")


async def _export_knowledge_graph(workspace: str, format: str, output: Optional[str]):
    """Export knowledge graph"""
    console.print(f"[cyan]📤 Exporting knowledge graph from: {workspace}[/cyan]")

    try:
        # Create knowledge export task
        kg_task = create_agent_task(
            task_type="knowledge_export",
            description="Export knowledge graph",
            data={
                "workspace_path": workspace,
                "format": format
            }
        )

        context = create_agent_context(
            session_id=f"kg_export_{int(asyncio.get_event_loop().time())}",
            workspace_path=workspace
        )

        result = await master_agent.execute_with_monitoring(kg_task, context)

        if result.status.value == "completed":
            export_data = result.data.get("export_data", "")

            if output:
                with open(output, 'w') as f:
                    f.write(export_data)
                console.print(f"[green]✓ Knowledge graph exported to: {output}[/green]")
            else:
                console.print(export_data)
        else:
            console.print(f"[red]✗ Knowledge graph export failed: {result.error}[/red]")

    except Exception as e:
        console.print(f"[red]✗ Knowledge graph export error: {e}[/red]")


async def _show_knowledge_stats(workspace: str):
    """Show knowledge graph statistics"""
    console.print(f"[cyan]📈 Knowledge graph statistics for: {workspace}[/cyan]")

    try:
        # Create knowledge stats task
        kg_task = create_agent_task(
            task_type="knowledge_stats",
            description="Get knowledge graph statistics",
            data={"workspace_path": workspace}
        )

        context = create_agent_context(
            session_id=f"kg_stats_{int(asyncio.get_event_loop().time())}",
            workspace_path=workspace
        )

        result = await master_agent.execute_with_monitoring(kg_task, context)

        if result.status.value == "completed":
            _display_knowledge_stats(result.data)
        else:
            console.print(f"[red]✗ Knowledge stats failed: {result.error}[/red]")

    except Exception as e:
        console.print(f"[red]✗ Knowledge stats error: {e}[/red]")


def _display_knowledge_results(data: dict, format: str, output: Optional[str]):
    """Display knowledge query results"""
    if format == "json":
        import json
        output_text = json.dumps(data, indent=2)
    elif format == "yaml":
        import yaml
        output_text = yaml.dump(data, default_flow_style=False)
    else:
        # Text format
        results = data.get("results", [])
        output_text = f"""
🧠 Knowledge Graph Query Results

Query: {data.get('query', 'N/A')}
Results Found: {len(results)}

"""
        for i, result in enumerate(results[:10], 1):  # Show first 10 results
            output_text += f"{i}. {result.get('title', 'Untitled')}\n"
            output_text += f"   Type: {result.get('type', 'Unknown')}\n"
            output_text += f"   Relevance: {result.get('relevance', 0):.2f}\n"
            if result.get('description'):
                output_text += f"   Description: {result['description'][:100]}...\n"
            output_text += "\n"

    if output:
        with open(output, 'w') as f:
            f.write(output_text)
        console.print(f"[green]✓ Results saved to: {output}[/green]")
    else:
        console.print(output_text)


def _display_knowledge_stats(data: dict):
    """Display knowledge graph statistics"""
    table = Table(title="Knowledge Graph Statistics")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")

    stats = data.get("stats", {})
    table.add_row("Total Nodes", str(stats.get("total_nodes", 0)))
    table.add_row("Total Edges", str(stats.get("total_edges", 0)))
    table.add_row("Concepts", str(stats.get("concepts", 0)))
    table.add_row("Relationships", str(stats.get("relationships", 0)))
    table.add_row("Code Entities", str(stats.get("code_entities", 0)))

    console.print(table)


def _display_debug_results(data: dict, output: Optional[str]):
    """Display debug analysis results"""
    issues = data.get("issues", [])

    output_text = f"""
🐛 Debug Analysis Results

Total Issues Found: {len(issues)}

"""

    # Group issues by severity
    severity_groups = {}
    for issue in issues:
        severity = issue.get("severity", "unknown")
        if severity not in severity_groups:
            severity_groups[severity] = []
        severity_groups[severity].append(issue)

    for severity, severity_issues in severity_groups.items():
        output_text += f"\n{severity.upper()} SEVERITY ({len(severity_issues)} issues):\n"
        output_text += "=" * 50 + "\n"

        for issue in severity_issues[:5]:  # Show first 5 issues per severity
            output_text += f"• {issue.get('title', 'Untitled Issue')}\n"
            output_text += f"  File: {issue.get('file', 'Unknown')}\n"
            output_text += f"  Line: {issue.get('line', 'Unknown')}\n"
            output_text += f"  Description: {issue.get('description', 'No description')}\n"
            if issue.get('suggestion'):
                output_text += f"  Suggestion: {issue['suggestion']}\n"
            output_text += "\n"

    if output:
        with open(output, 'w') as f:
            f.write(output_text)
        console.print(f"[green]✓ Debug results saved to: {output}[/green]")
    else:
        console.print(output_text)


def _display_metrics_results(data: dict, format: str, output: Optional[str]):
    """Display metrics calculation results"""
    if format == "json":
        import json
        output_text = json.dumps(data, indent=2)
    elif format == "yaml":
        import yaml
        output_text = yaml.dump(data, default_flow_style=False)
    else:
        # Text format
        metrics = data.get("metrics", {})
        output_text = f"""
📊 Code Metrics Results

Workspace: {data.get('workspace_path', 'Unknown')}

"""

        if "complexity" in metrics:
            complexity = metrics["complexity"]
            output_text += f"""
🔄 Complexity Metrics:
  Average Cyclomatic Complexity: {complexity.get('average_cyclomatic', 0):.2f}
  Max Complexity: {complexity.get('max_complexity', 0)}
  High Complexity Files: {len(complexity.get('high_complexity_files', []))}

"""

        if "quality" in metrics:
            quality = metrics["quality"]
            output_text += f"""
🎯 Quality Metrics:
  Overall Grade: {quality.get('overall_grade', 'N/A')}
  Average Maintainability: {quality.get('average_maintainability', 0):.2f}
  Comment Ratio: {quality.get('comment_ratio', 0):.3f}
  Test Coverage: {quality.get('test_coverage', 0):.1f}%

"""

        if "size" in metrics:
            size = metrics["size"]
            output_text += f"""
📏 Size Metrics:
  Total Lines of Code: {size.get('total_loc', 0):,}
  Total Files: {size.get('total_files', 0)}
  Average File Size: {size.get('average_file_size', 0):.0f} lines
  Largest File: {size.get('largest_file', 'N/A')} ({size.get('largest_file_size', 0)} lines)

"""

    if output:
        with open(output, 'w') as f:
            f.write(output_text)
        console.print(f"[green]✓ Metrics saved to: {output}[/green]")
    else:
        console.print(output_text)


def main():
    """Main entry point"""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Unexpected error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
