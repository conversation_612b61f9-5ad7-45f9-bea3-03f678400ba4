"""
Analysis Commands for CLI
"""

import asyncio
import os
from pathlib import Path
from typing import Optional

import typer
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn

from ..config import console, cli_config
from ..utils import format_output, save_output, create_progress_bar, display_error, display_success
from ccw.core.config import init_config
from ccw.core.message_bus import message_bus
from ccw.core.registry import agent_registry
from ccw.core.agent import create_agent_task, create_agent_context
from ccw.agents.master_agent import MasterAgent
from ccw.analysis.workspace import WorkspaceAnalyzer

# Create analysis commands app
analysis_app = typer.Typer(name="analyze", help="Code analysis commands")


async def initialize_system(config_file: Optional[str] = None):
    """Initialize the system for analysis"""
    if config_file:
        init_config(config_file=config_file)
    else:
        init_config()
    
    await message_bus.start()
    agent_registry.load_plugins()
    
    master_agent = MasterAgent()
    return master_agent


async def shutdown_system(master_agent: Optional[MasterAgent] = None):
    """Shutdown the system gracefully"""
    if master_agent:
        await master_agent.shutdown()
    
    await agent_registry.shutdown_all_agents()
    await message_bus.stop()


@analysis_app.command("file")
def analyze_file(
    file_path: str = typer.Argument(..., help="File path to analyze"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("text", "--format", "-f", help="Output format"),
    include_metrics: bool = typer.Option(True, "--metrics/--no-metrics", help="Include metrics"),
    include_ast: bool = typer.Option(False, "--ast/--no-ast", help="Include AST"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Config file")
):
    """Analyze a single file"""
    
    async def run_file_analysis():
        master_agent = None
        try:
            master_agent = await initialize_system(config_file)
            
            console.print(f"[info]🔍 Analyzing file: {file_path}[/info]")
            
            if not os.path.exists(file_path):
                console.print(f"[error]✗ File does not exist: {file_path}[/error]")
                return
            
            workspace_analyzer = WorkspaceAnalyzer()
            
            with create_progress_bar("Analyzing file...") as progress:
                task = progress.add_task("Processing...", total=1)
                
                result = workspace_analyzer.analyze_file(
                    file_path,
                    include_metrics=include_metrics,
                    include_ast=include_ast
                )
                
                progress.update(task, advance=1, description="Analysis complete!")
            
            # Format and display results
            formatted_result = format_output(result, format)
            
            if output:
                if save_output(formatted_result, output):
                    display_success(f"Analysis results saved to {output}")
            else:
                console.print(formatted_result)
                
        except Exception as e:
            display_error(e, "File analysis failed")
        finally:
            if master_agent:
                await shutdown_system(master_agent)
    
    asyncio.run(run_file_analysis())


@analysis_app.command("workspace")
def analyze_workspace(
    workspace_path: str = typer.Argument(".", help="Workspace path to analyze"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("text", "--format", "-f", help="Output format"),
    max_files: Optional[int] = typer.Option(None, "--max-files", help="Maximum files to analyze"),
    include_metrics: bool = typer.Option(True, "--metrics/--no-metrics", help="Include metrics"),
    include_dependencies: bool = typer.Option(True, "--deps/--no-deps", help="Include dependencies"),
    parallel: bool = typer.Option(True, "--parallel/--sequential", help="Parallel processing"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Config file")
):
    """Analyze an entire workspace"""
    
    async def run_workspace_analysis():
        master_agent = None
        try:
            master_agent = await initialize_system(config_file)
            
            console.print(f"[info]🏢 Analyzing workspace: {workspace_path}[/info]")
            
            if not os.path.exists(workspace_path):
                console.print(f"[error]✗ Workspace does not exist: {workspace_path}[/error]")
                return
            
            workspace_analyzer = WorkspaceAnalyzer()
            
            with create_progress_bar("Analyzing workspace...") as progress:
                task = progress.add_task("Processing files...", total=None)
                
                result = workspace_analyzer.analyze_workspace(
                    workspace_path,
                    max_files=max_files,
                    include_metrics=include_metrics,
                    include_dependencies=include_dependencies,
                    parallel=parallel
                )
                
                progress.update(task, description="Workspace analysis complete!")
            
            # Format and display results
            formatted_result = format_output(result.to_dict(), format)
            
            if output:
                if save_output(formatted_result, output):
                    display_success(f"Analysis results saved to {output}")
            else:
                console.print(formatted_result)
                
        except Exception as e:
            display_error(e, "Workspace analysis failed")
        finally:
            if master_agent:
                await shutdown_system(master_agent)
    
    asyncio.run(run_workspace_analysis())


@analysis_app.command("compare")
def compare_workspaces(
    workspace1: str = typer.Argument(..., help="First workspace path"),
    workspace2: str = typer.Argument(..., help="Second workspace path"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("text", "--format", "-f", help="Output format"),
    metric_focus: str = typer.Option("all", "--focus", help="Metric focus (complexity, quality, size, all)"),
    config_file: Optional[str] = typer.Option(None, "--config", "-c", help="Config file")
):
    """Compare two workspaces"""
    
    async def run_comparison():
        master_agent = None
        try:
            master_agent = await initialize_system(config_file)
            
            console.print(f"[info]⚖️  Comparing workspaces:[/info]")
            console.print(f"  Workspace 1: {workspace1}")
            console.print(f"  Workspace 2: {workspace2}")
            
            # Verify both workspaces exist
            for ws in [workspace1, workspace2]:
                if not os.path.exists(ws):
                    console.print(f"[error]✗ Workspace does not exist: {ws}[/error]")
                    return
            
            workspace_analyzer = WorkspaceAnalyzer()
            
            with create_progress_bar("Comparing workspaces...") as progress:
                task1 = progress.add_task("Analyzing workspace 1...", total=1)
                
                result1 = workspace_analyzer.analyze_workspace(workspace1)
                progress.update(task1, advance=1)
                
                task2 = progress.add_task("Analyzing workspace 2...", total=1)
                result2 = workspace_analyzer.analyze_workspace(workspace2)
                progress.update(task2, advance=1)
                
                task3 = progress.add_task("Generating comparison...", total=1)
                
                # Create comparison data
                comparison = {
                    "workspace1": {
                        "path": workspace1,
                        "metrics": result1.workspace_metrics,
                        "summary": result1.to_dict()["summary"]
                    },
                    "workspace2": {
                        "path": workspace2,
                        "metrics": result2.workspace_metrics,
                        "summary": result2.to_dict()["summary"]
                    },
                    "comparison": _generate_comparison_metrics(result1, result2, metric_focus)
                }
                
                progress.update(task3, advance=1, description="Comparison complete!")
            
            # Format and display results
            formatted_result = format_output(comparison, format)
            
            if output:
                if save_output(formatted_result, output):
                    display_success(f"Comparison results saved to {output}")
            else:
                console.print(formatted_result)
                
        except Exception as e:
            display_error(e, "Workspace comparison failed")
        finally:
            if master_agent:
                await shutdown_system(master_agent)
    
    asyncio.run(run_comparison())


def _generate_comparison_metrics(result1, result2, focus: str) -> dict:
    """Generate comparison metrics between two workspace results"""
    comparison = {}
    
    metrics1 = result1.workspace_metrics or {}
    metrics2 = result2.workspace_metrics or {}
    
    # Compare basic metrics
    for metric in ["total_lines_of_code", "total_functions", "total_classes", 
                   "average_complexity", "average_maintainability"]:
        val1 = metrics1.get(metric, 0)
        val2 = metrics2.get(metric, 0)
        
        if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
            diff = val2 - val1
            percent_change = (diff / val1 * 100) if val1 != 0 else 0
            
            comparison[metric] = {
                "workspace1": val1,
                "workspace2": val2,
                "difference": diff,
                "percent_change": percent_change
            }
    
    # Compare summary data
    summary1 = result1.to_dict()["summary"]
    summary2 = result2.to_dict()["summary"]
    
    comparison["file_counts"] = {
        "workspace1": summary1.get("total_files", 0),
        "workspace2": summary2.get("total_files", 0),
        "difference": summary2.get("total_files", 0) - summary1.get("total_files", 0)
    }
    
    return comparison


# Export the analysis commands
analysis_commands = analysis_app
