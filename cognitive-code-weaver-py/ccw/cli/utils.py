"""
CLI Utilities and Helper Functions
"""

import json
import yaml
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.tree import Tree
from rich.syntax import Syntax
from rich.markdown import Markdown

from .config import console


def format_output(data: Any, format_type: str) -> str:
    """Format data according to the specified format"""
    if format_type == "json":
        return json.dumps(data, indent=2, default=str)
    elif format_type == "yaml":
        return yaml.dump(data, default_flow_style=False, allow_unicode=True)
    elif format_type == "csv":
        return _to_csv(data)
    elif format_type == "xml":
        return _to_xml(data)
    else:  # text format
        return _to_text(data)


def _to_csv(data: Any) -> str:
    """Convert data to CSV format"""
    if isinstance(data, dict):
        # Convert dict to list of key-value pairs
        rows = [["Key", "Value"]]
        for key, value in data.items():
            rows.append([str(key), str(value)])
    elif isinstance(data, list) and data and isinstance(data[0], dict):
        # List of dictionaries
        if not data:
            return ""
        headers = list(data[0].keys())
        rows = [headers]
        for item in data:
            rows.append([str(item.get(header, "")) for header in headers])
    else:
        # Simple list or other data
        rows = [["Value"]]
        if isinstance(data, list):
            for item in data:
                rows.append([str(item)])
        else:
            rows.append([str(data)])
    
    # Convert to CSV string
    import io
    output = io.StringIO()
    writer = csv.writer(output)
    writer.writerows(rows)
    return output.getvalue()


def _to_xml(data: Any, root_name: str = "data") -> str:
    """Convert data to XML format"""
    def dict_to_xml(d: dict, parent: ET.Element):
        for key, value in d.items():
            child = ET.SubElement(parent, str(key))
            if isinstance(value, dict):
                dict_to_xml(value, child)
            elif isinstance(value, list):
                for item in value:
                    item_elem = ET.SubElement(child, "item")
                    if isinstance(item, dict):
                        dict_to_xml(item, item_elem)
                    else:
                        item_elem.text = str(item)
            else:
                child.text = str(value)
    
    root = ET.Element(root_name)
    if isinstance(data, dict):
        dict_to_xml(data, root)
    elif isinstance(data, list):
        for i, item in enumerate(data):
            item_elem = ET.SubElement(root, f"item_{i}")
            if isinstance(item, dict):
                dict_to_xml(item, item_elem)
            else:
                item_elem.text = str(item)
    else:
        root.text = str(data)
    
    return ET.tostring(root, encoding='unicode')


def _to_text(data: Any) -> str:
    """Convert data to human-readable text format"""
    if isinstance(data, dict):
        lines = []
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                lines.append(f"{key}:")
                sub_text = _to_text(value)
                for line in sub_text.split('\n'):
                    if line.strip():
                        lines.append(f"  {line}")
            else:
                lines.append(f"{key}: {value}")
        return '\n'.join(lines)
    elif isinstance(data, list):
        lines = []
        for i, item in enumerate(data):
            if isinstance(item, (dict, list)):
                lines.append(f"[{i}]:")
                sub_text = _to_text(item)
                for line in sub_text.split('\n'):
                    if line.strip():
                        lines.append(f"  {line}")
            else:
                lines.append(f"[{i}]: {item}")
        return '\n'.join(lines)
    else:
        return str(data)


def save_output(data: str, output_path: str) -> bool:
    """Save output to file"""
    try:
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(data)
        
        console.print(f"[success]✓ Output saved to: {output_path}[/success]")
        return True
    except Exception as e:
        console.print(f"[error]✗ Failed to save output: {e}[/error]")
        return False


def create_progress_bar(description: str = "Processing...") -> Progress:
    """Create a standardized progress bar"""
    return Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    )


def create_table(title: str, columns: List[str]) -> Table:
    """Create a standardized table"""
    table = Table(title=title, show_header=True, header_style="bold magenta")
    for column in columns:
        table.add_column(column)
    return table


def create_panel(content: str, title: str, style: str = "blue") -> Panel:
    """Create a standardized panel"""
    return Panel(content, title=title, border_style=style)


def create_tree(root_name: str) -> Tree:
    """Create a standardized tree"""
    return Tree(f"[bold blue]{root_name}[/bold blue]")


def display_code(code: str, language: str = "python", theme: str = "monokai") -> None:
    """Display syntax-highlighted code"""
    syntax = Syntax(code, language, theme=theme, line_numbers=True)
    console.print(syntax)


def display_markdown(content: str) -> None:
    """Display markdown content"""
    markdown = Markdown(content)
    console.print(markdown)


def confirm_action(message: str, default: bool = False) -> bool:
    """Ask for user confirmation"""
    default_text = "Y/n" if default else "y/N"
    response = console.input(f"[prompt]{message} ({default_text}): [/prompt]")
    
    if not response.strip():
        return default
    
    return response.lower().startswith('y')


def select_option(options: List[str], message: str = "Select an option") -> Optional[str]:
    """Let user select from a list of options"""
    if not options:
        return None
    
    console.print(f"[prompt]{message}:[/prompt]")
    for i, option in enumerate(options, 1):
        console.print(f"  {i}. {option}")
    
    while True:
        try:
            choice = console.input("[prompt]Enter choice (number): [/prompt]")
            if not choice.strip():
                return None
            
            index = int(choice) - 1
            if 0 <= index < len(options):
                return options[index]
            else:
                console.print("[error]Invalid choice. Please try again.[/error]")
        except ValueError:
            console.print("[error]Please enter a valid number.[/error]")
        except KeyboardInterrupt:
            return None


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{minutes:.0f}m {remaining_seconds:.0f}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{hours:.0f}h {remaining_minutes:.0f}m"


def format_timestamp(timestamp: Optional[datetime] = None) -> str:
    """Format timestamp in human-readable format"""
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime("%Y-%m-%d %H:%M:%S")


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def create_summary_table(data: Dict[str, Any], title: str = "Summary") -> Table:
    """Create a summary table from key-value data"""
    table = create_table(title, ["Property", "Value"])
    
    for key, value in data.items():
        # Format key
        formatted_key = key.replace('_', ' ').title()
        
        # Format value
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                formatted_value = f"{value:.2f}"
            else:
                formatted_value = f"{value:,}"
        elif isinstance(value, bool):
            formatted_value = "✓" if value else "✗"
        elif isinstance(value, list):
            formatted_value = f"{len(value)} items"
        elif isinstance(value, dict):
            formatted_value = f"{len(value)} entries"
        else:
            formatted_value = str(value)
        
        table.add_row(formatted_key, formatted_value)
    
    return table


def display_error(error: Exception, context: str = "") -> None:
    """Display error in a standardized format"""
    error_message = str(error)
    if context:
        error_message = f"{context}: {error_message}"
    
    panel = create_panel(error_message, "Error", "red")
    console.print(panel)


def display_warning(message: str) -> None:
    """Display warning in a standardized format"""
    console.print(f"[warning]⚠️  {message}[/warning]")


def display_info(message: str) -> None:
    """Display info in a standardized format"""
    console.print(f"[info]ℹ️  {message}[/info]")


def display_success(message: str) -> None:
    """Display success in a standardized format"""
    console.print(f"[success]✓ {message}[/success]")


def create_metrics_display(metrics: Dict[str, Any]) -> None:
    """Create a comprehensive metrics display"""
    # Create main metrics table
    main_table = create_table("Code Metrics", ["Metric", "Value", "Status"])
    
    for metric_name, metric_value in metrics.items():
        # Determine status based on metric type and value
        status = "✓"
        if "complexity" in metric_name.lower() and isinstance(metric_value, (int, float)):
            if metric_value > 10:
                status = "⚠️"
            elif metric_value > 20:
                status = "❌"
        elif "maintainability" in metric_name.lower() and isinstance(metric_value, (int, float)):
            if metric_value < 50:
                status = "❌"
            elif metric_value < 70:
                status = "⚠️"
        
        # Format metric name and value
        formatted_name = metric_name.replace('_', ' ').title()
        if isinstance(metric_value, float):
            formatted_value = f"{metric_value:.2f}"
        elif isinstance(metric_value, int):
            formatted_value = f"{metric_value:,}"
        else:
            formatted_value = str(metric_value)
        
        main_table.add_row(formatted_name, formatted_value, status)
    
    console.print(main_table)
