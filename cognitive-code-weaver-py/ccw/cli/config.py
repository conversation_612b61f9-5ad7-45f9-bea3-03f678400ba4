"""
CLI Configuration and Settings
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

import typer
from rich.console import Console
from rich.theme import Theme

# Custom Rich theme for CLI
CLI_THEME = Theme({
    "info": "cyan",
    "warning": "yellow",
    "error": "bold red",
    "success": "bold green",
    "highlight": "bold blue",
    "prompt": "bold cyan",
    "path": "bold magenta",
    "code": "bright_black on white",
    "metric": "bold yellow",
    "agent": "bold green",
    "llm": "bold purple",
})

# Console instance with custom theme
console = Console(theme=CLI_THEME)


@dataclass
class CLIConfig:
    """CLI configuration settings"""
    
    # Output settings
    verbose: bool = False
    quiet: bool = False
    no_color: bool = False
    output_format: str = "text"  # text, json, yaml
    
    # Analysis settings
    max_files: Optional[int] = None
    include_metrics: bool = True
    include_dependencies: bool = True
    
    # LLM settings
    default_provider: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.7
    
    # Interactive settings
    auto_complete: bool = True
    history_size: int = 1000
    
    # Paths
    config_file: Optional[str] = None
    workspace_path: str = "."
    output_dir: Optional[str] = None
    
    # Performance settings
    parallel_analysis: bool = True
    cache_enabled: bool = True
    
    # Debug settings
    debug_mode: bool = False
    log_level: str = "INFO"
    
    # Feature flags
    experimental_features: bool = False
    
    @classmethod
    def from_env(cls) -> "CLIConfig":
        """Create config from environment variables"""
        return cls(
            verbose=os.getenv("CCW_VERBOSE", "false").lower() == "true",
            quiet=os.getenv("CCW_QUIET", "false").lower() == "true",
            no_color=os.getenv("CCW_NO_COLOR", "false").lower() == "true",
            output_format=os.getenv("CCW_OUTPUT_FORMAT", "text"),
            max_files=int(os.getenv("CCW_MAX_FILES", "0")) or None,
            include_metrics=os.getenv("CCW_INCLUDE_METRICS", "true").lower() == "true",
            include_dependencies=os.getenv("CCW_INCLUDE_DEPS", "true").lower() == "true",
            default_provider=os.getenv("CCW_DEFAULT_PROVIDER"),
            max_tokens=int(os.getenv("CCW_MAX_TOKENS", "1000")),
            temperature=float(os.getenv("CCW_TEMPERATURE", "0.7")),
            config_file=os.getenv("CCW_CONFIG_FILE"),
            workspace_path=os.getenv("CCW_WORKSPACE", "."),
            output_dir=os.getenv("CCW_OUTPUT_DIR"),
            parallel_analysis=os.getenv("CCW_PARALLEL", "true").lower() == "true",
            cache_enabled=os.getenv("CCW_CACHE", "true").lower() == "true",
            debug_mode=os.getenv("CCW_DEBUG", "false").lower() == "true",
            log_level=os.getenv("CCW_LOG_LEVEL", "INFO"),
            experimental_features=os.getenv("CCW_EXPERIMENTAL", "false").lower() == "true",
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "verbose": self.verbose,
            "quiet": self.quiet,
            "no_color": self.no_color,
            "output_format": self.output_format,
            "max_files": self.max_files,
            "include_metrics": self.include_metrics,
            "include_dependencies": self.include_dependencies,
            "default_provider": self.default_provider,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "config_file": self.config_file,
            "workspace_path": self.workspace_path,
            "output_dir": self.output_dir,
            "parallel_analysis": self.parallel_analysis,
            "cache_enabled": self.cache_enabled,
            "debug_mode": self.debug_mode,
            "log_level": self.log_level,
            "experimental_features": self.experimental_features,
        }


# Global CLI configuration instance
cli_config = CLIConfig.from_env()


def get_config_path() -> Path:
    """Get the configuration file path"""
    if cli_config.config_file:
        return Path(cli_config.config_file)
    
    # Check common locations
    locations = [
        Path("config/ccw.yaml"),
        Path("ccw.yaml"),
        Path.home() / ".ccw" / "config.yaml",
        Path.home() / ".config" / "ccw" / "config.yaml",
    ]
    
    for location in locations:
        if location.exists():
            return location
    
    # Return default location
    return Path("config/ccw.yaml")


def setup_console(config: CLIConfig) -> Console:
    """Setup console with configuration"""
    return Console(
        theme=CLI_THEME if not config.no_color else None,
        quiet=config.quiet,
        force_terminal=not config.no_color,
        width=None,  # Auto-detect
        legacy_windows=False,
    )


def validate_output_format(format_str: str) -> str:
    """Validate output format"""
    valid_formats = ["text", "json", "yaml", "csv", "xml"]
    if format_str.lower() not in valid_formats:
        raise typer.BadParameter(f"Invalid format. Must be one of: {', '.join(valid_formats)}")
    return format_str.lower()


def validate_log_level(level: str) -> str:
    """Validate log level"""
    valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if level.upper() not in valid_levels:
        raise typer.BadParameter(f"Invalid log level. Must be one of: {', '.join(valid_levels)}")
    return level.upper()


def validate_workspace_path(path: str) -> str:
    """Validate workspace path"""
    workspace_path = Path(path)
    if not workspace_path.exists():
        raise typer.BadParameter(f"Workspace path does not exist: {path}")
    return str(workspace_path.resolve())


def get_output_file(output: Optional[str], default_name: str, format: str) -> Optional[str]:
    """Get output file path with proper extension"""
    if not output:
        return None
    
    output_path = Path(output)
    
    # Add extension if not present
    if not output_path.suffix:
        extensions = {
            "json": ".json",
            "yaml": ".yaml",
            "csv": ".csv",
            "xml": ".xml",
            "text": ".txt"
        }
        extension = extensions.get(format, ".txt")
        output_path = output_path.with_suffix(extension)
    
    # Create parent directories if needed
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    return str(output_path)


def print_banner():
    """Print CLI banner"""
    banner = """
[bold blue]╔══════════════════════════════════════════════════════════════╗[/bold blue]
[bold blue]║[/bold blue]              [bold cyan]Cognitive Code Weaver[/bold cyan]                      [bold blue]║[/bold blue]
[bold blue]║[/bold blue]         [italic]AI-Powered Code Analysis & Reasoning[/italic]           [bold blue]║[/bold blue]
[bold blue]╚══════════════════════════════════════════════════════════════╝[/bold blue]
"""
    console.print(banner)


def print_version():
    """Print version information"""
    version_info = """
[bold]Cognitive Code Weaver[/bold] v0.1.0

[dim]Components:[/dim]
• Multi-Agent System
• LLM Integration (OpenAI, Anthropic, Local)
• Advanced Code Analysis
• Knowledge Graph
• Interactive CLI

[dim]Python:[/dim] {python_version}
[dim]Platform:[/dim] {platform}
""".format(
        python_version=f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
        platform=os.sys.platform
    )
    console.print(version_info)


def print_help_footer():
    """Print help footer with additional information"""
    footer = """
[dim]For more information:[/dim]
• Documentation: https://cognitive-code-weaver.readthedocs.io/
• GitHub: https://github.com/cognitive-code-weaver/cognitive-code-weaver-py
• Issues: https://github.com/cognitive-code-weaver/cognitive-code-weaver-py/issues

[dim]Environment Variables:[/dim]
• CCW_CONFIG_FILE: Configuration file path
• CCW_VERBOSE: Enable verbose output
• CCW_NO_COLOR: Disable colored output
• CCW_DEFAULT_PROVIDER: Default LLM provider
• CCW_MAX_FILES: Maximum files to analyze
• CCW_DEBUG: Enable debug mode

[dim]Examples:[/dim]
• ccw analyze ./src --format json --output results.json
• ccw ask "How does authentication work?" --workspace ./src
• ccw llm test --text "Explain this code" --provider openai
• ccw knowledge query "find all classes" --workspace ./src
• ccw interactive
"""
    console.print(footer)


# Common CLI options as reusable decorators
def common_options(f):
    """Add common CLI options to a command"""
    f = typer.Option(None, "--config", "-c", help="Configuration file path")(f)
    f = typer.Option(False, "--verbose", "-v", help="Enable verbose output")(f)
    f = typer.Option(False, "--quiet", "-q", help="Suppress output")(f)
    f = typer.Option(False, "--no-color", help="Disable colored output")(f)
    return f


def output_options(f):
    """Add output-related options to a command"""
    f = typer.Option(None, "--output", "-o", help="Output file path")(f)
    f = typer.Option("text", "--format", "-f", help="Output format (text, json, yaml)")(f)
    return f


def analysis_options(f):
    """Add analysis-related options to a command"""
    f = typer.Option(None, "--max-files", help="Maximum files to analyze")(f)
    f = typer.Option(True, "--metrics/--no-metrics", help="Include code metrics")(f)
    f = typer.Option(True, "--deps/--no-deps", help="Include dependency analysis")(f)
    return f


def llm_options(f):
    """Add LLM-related options to a command"""
    f = typer.Option(None, "--provider", "-p", help="LLM provider to use")(f)
    f = typer.Option(1000, "--max-tokens", help="Maximum tokens for LLM response")(f)
    f = typer.Option(0.7, "--temperature", help="Temperature for LLM response")(f)
    return f
