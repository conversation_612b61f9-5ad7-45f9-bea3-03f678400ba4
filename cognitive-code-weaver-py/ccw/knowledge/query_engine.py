"""
Query Engine for Knowledge Graph

Provides intelligent querying capabilities for the knowledge graph,
including semantic search, relationship queries, and natural language processing.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ccw.knowledge.knowledge_graph import KnowledgeGraph
from ccw.knowledge.graph_database import GraphNode, GraphRelationship
from ccw.llm.client import llm_client

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """Types of knowledge graph queries"""
    SEMANTIC_SEARCH = "semantic_search"
    RELATIONSHIP_QUERY = "relationship_query"
    PATH_FINDING = "path_finding"
    CONCEPT_EXPLORATION = "concept_exploration"
    PATTERN_MATCHING = "pattern_matching"
    NATURAL_LANGUAGE = "natural_language"


@dataclass
class SemanticQuery:
    """Represents a semantic query to the knowledge graph"""
    query_text: str
    query_type: QueryType
    parameters: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)
    max_results: int = 50
    include_relationships: bool = True
    confidence_threshold: float = 0.5
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class QueryResult:
    """Result of a knowledge graph query"""
    query: SemanticQuery
    nodes: List[GraphNode] = field(default_factory=list)
    relationships: List[GraphRelationship] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0
    total_results: int = 0
    confidence_scores: Dict[str, float] = field(default_factory=dict)


class GraphQueryEngine:
    """Engine for querying the knowledge graph"""
    
    def __init__(self, knowledge_graph: KnowledgeGraph):
        self.knowledge_graph = knowledge_graph
        self.query_patterns = self._load_query_patterns()
        self.semantic_keywords = self._load_semantic_keywords()
    
    async def execute_query(self, query: SemanticQuery) -> QueryResult:
        """Execute a semantic query against the knowledge graph"""
        start_time = datetime.now()
        
        try:
            if query.query_type == QueryType.SEMANTIC_SEARCH:
                result = await self._execute_semantic_search(query)
            elif query.query_type == QueryType.RELATIONSHIP_QUERY:
                result = await self._execute_relationship_query(query)
            elif query.query_type == QueryType.PATH_FINDING:
                result = await self._execute_path_finding(query)
            elif query.query_type == QueryType.CONCEPT_EXPLORATION:
                result = await self._execute_concept_exploration(query)
            elif query.query_type == QueryType.PATTERN_MATCHING:
                result = await self._execute_pattern_matching(query)
            elif query.query_type == QueryType.NATURAL_LANGUAGE:
                result = await self._execute_natural_language_query(query)
            else:
                raise ValueError(f"Unsupported query type: {query.query_type}")
            
            # Calculate execution time
            end_time = datetime.now()
            result.execution_time = (end_time - start_time).total_seconds()
            
            return result
            
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return QueryResult(
                query=query,
                metadata={"error": str(e)},
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _execute_semantic_search(self, query: SemanticQuery) -> QueryResult:
        """Execute semantic search query"""
        search_terms = self._extract_search_terms(query.query_text)
        
        # Search for nodes with matching properties
        matching_nodes = []
        confidence_scores = {}
        
        # Search in concept nodes
        concept_nodes = await self.knowledge_graph.get_nodes_by_type("semantic_concept")
        for node in concept_nodes:
            score = self._calculate_semantic_similarity(search_terms, node)
            if score >= query.confidence_threshold:
                matching_nodes.append(node)
                confidence_scores[node.id] = score
        
        # Search in symbol nodes
        symbol_nodes = await self.knowledge_graph.get_nodes_by_type("code_symbol")
        for node in symbol_nodes:
            score = self._calculate_symbol_similarity(search_terms, node)
            if score >= query.confidence_threshold:
                matching_nodes.append(node)
                confidence_scores[node.id] = score
        
        # Sort by confidence score
        matching_nodes.sort(key=lambda n: confidence_scores.get(n.id, 0), reverse=True)
        
        # Limit results
        matching_nodes = matching_nodes[:query.max_results]
        
        # Get relationships if requested
        relationships = []
        if query.include_relationships:
            for node in matching_nodes:
                node_relationships = await self.knowledge_graph.get_relationships(node.id)
                relationships.extend(node_relationships)
        
        return QueryResult(
            query=query,
            nodes=matching_nodes,
            relationships=relationships,
            total_results=len(matching_nodes),
            confidence_scores=confidence_scores,
            metadata={"search_terms": search_terms}
        )
    
    async def _execute_relationship_query(self, query: SemanticQuery) -> QueryResult:
        """Execute relationship-based query"""
        source_id = query.parameters.get("source_id")
        target_id = query.parameters.get("target_id")
        relationship_type = query.parameters.get("relationship_type")
        
        if not source_id:
            raise ValueError("Relationship query requires source_id parameter")
        
        # Get relationships
        relationships = await self.knowledge_graph.get_relationships(source_id, target_id)
        
        # Filter by relationship type if specified
        if relationship_type:
            relationships = [r for r in relationships if r.relationship_type == relationship_type]
        
        # Get related nodes
        node_ids = set([source_id])
        for rel in relationships:
            node_ids.add(rel.target_id)
        
        nodes = []
        for node_id in node_ids:
            node = await self.knowledge_graph.get_node(node_id)
            if node:
                nodes.append(node)
        
        return QueryResult(
            query=query,
            nodes=nodes,
            relationships=relationships,
            total_results=len(relationships),
            metadata={"relationship_type": relationship_type}
        )
    
    async def _execute_path_finding(self, query: SemanticQuery) -> QueryResult:
        """Execute path finding query"""
        source_id = query.parameters.get("source_id")
        target_id = query.parameters.get("target_id")
        
        if not source_id or not target_id:
            raise ValueError("Path finding query requires source_id and target_id parameters")
        
        # Find shortest path
        path = await self.knowledge_graph.find_shortest_path(source_id, target_id)
        
        if not path:
            return QueryResult(
                query=query,
                metadata={"path_found": False, "message": "No path found between nodes"}
            )
        
        # Get nodes and relationships in the path
        nodes = []
        relationships = []
        
        for node_id in path:
            node = await self.knowledge_graph.get_node(node_id)
            if node:
                nodes.append(node)
        
        # Get relationships between consecutive nodes in path
        for i in range(len(path) - 1):
            source = path[i]
            target = path[i + 1]
            rels = await self.knowledge_graph.get_relationships(source, target)
            relationships.extend(rels)
        
        return QueryResult(
            query=query,
            nodes=nodes,
            relationships=relationships,
            total_results=len(path),
            metadata={"path": path, "path_length": len(path)}
        )
    
    async def _execute_concept_exploration(self, query: SemanticQuery) -> QueryResult:
        """Execute concept exploration query"""
        concept_id = query.parameters.get("concept_id")
        max_depth = query.parameters.get("max_depth", 2)
        
        if not concept_id:
            raise ValueError("Concept exploration query requires concept_id parameter")
        
        # Find related concepts
        related_nodes = await self.knowledge_graph.find_related_concepts(concept_id, max_depth)
        
        # Get the starting concept
        start_node = await self.knowledge_graph.get_node(concept_id)
        if start_node:
            related_nodes.insert(0, start_node)
        
        # Get relationships between all nodes
        relationships = []
        node_ids = [node.id for node in related_nodes]
        
        for node_id in node_ids:
            node_relationships = await self.knowledge_graph.get_relationships(node_id)
            # Only include relationships to other nodes in our result set
            filtered_rels = [r for r in node_relationships if r.target_id in node_ids]
            relationships.extend(filtered_rels)
        
        return QueryResult(
            query=query,
            nodes=related_nodes,
            relationships=relationships,
            total_results=len(related_nodes),
            metadata={"exploration_depth": max_depth, "start_concept": concept_id}
        )
    
    async def _execute_pattern_matching(self, query: SemanticQuery) -> QueryResult:
        """Execute pattern matching query"""
        pattern = query.parameters.get("pattern")
        
        if not pattern:
            raise ValueError("Pattern matching query requires pattern parameter")
        
        # Convert pattern to graph query
        graph_query = self._pattern_to_query(pattern)
        
        # Execute graph query
        results = await self.knowledge_graph.query(graph_query, query.parameters)
        
        # Convert results to nodes and relationships
        nodes = []
        relationships = []
        
        for result in results:
            if "node" in result:
                node_data = result["node"]
                # Convert to GraphNode (simplified)
                node = GraphNode(
                    id=node_data.get("id", ""),
                    label=node_data.get("label", ""),
                    node_type=node_data.get("node_type", ""),
                    properties=node_data.get("properties", {}),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                nodes.append(node)
        
        return QueryResult(
            query=query,
            nodes=nodes,
            relationships=relationships,
            total_results=len(nodes),
            metadata={"pattern": pattern, "graph_query": graph_query}
        )
    
    async def _execute_natural_language_query(self, query: SemanticQuery) -> QueryResult:
        """Execute natural language query using LLM"""
        try:
            # Parse natural language query using LLM
            parsed_query = await self._parse_natural_language_query(query.query_text)
            
            # Convert to structured query
            structured_query = self._convert_to_structured_query(parsed_query, query)
            
            # Execute structured query
            return await self.execute_query(structured_query)
            
        except Exception as e:
            logger.error(f"Natural language query failed: {e}")
            # Fallback to semantic search
            fallback_query = SemanticQuery(
                query_text=query.query_text,
                query_type=QueryType.SEMANTIC_SEARCH,
                max_results=query.max_results,
                confidence_threshold=query.confidence_threshold
            )
            return await self._execute_semantic_search(fallback_query)
    
    def _extract_search_terms(self, query_text: str) -> List[str]:
        """Extract search terms from query text"""
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        # Extract words
        words = re.findall(r'\b\w+\b', query_text.lower())
        
        # Filter stop words and short words
        terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        return terms
    
    def _calculate_semantic_similarity(self, search_terms: List[str], node: GraphNode) -> float:
        """Calculate semantic similarity between search terms and a concept node"""
        if node.node_type != "semantic_concept":
            return 0.0
        
        properties = node.properties
        
        # Get node keywords and description
        keywords = properties.get("keywords", [])
        description = properties.get("description", "")
        name = properties.get("name", "")
        
        # Combine all text for matching
        node_text = " ".join([name, description] + keywords).lower()
        node_words = set(re.findall(r'\b\w+\b', node_text))
        
        # Calculate overlap
        search_words = set(search_terms)
        overlap = search_words & node_words
        
        if not search_words:
            return 0.0
        
        # Calculate similarity score
        similarity = len(overlap) / len(search_words)
        
        # Boost score for exact name matches
        if any(term in name.lower() for term in search_terms):
            similarity += 0.3
        
        return min(1.0, similarity)
    
    def _calculate_symbol_similarity(self, search_terms: List[str], node: GraphNode) -> float:
        """Calculate similarity between search terms and a symbol node"""
        if node.node_type != "code_symbol":
            return 0.0
        
        properties = node.properties
        name = properties.get("name", "").lower()
        symbol_type = properties.get("symbol_type", "").lower()
        file_path = properties.get("file_path", "").lower()
        
        # Check for exact or partial name matches
        score = 0.0
        
        for term in search_terms:
            if term in name:
                score += 0.5
            if term in symbol_type:
                score += 0.3
            if term in file_path:
                score += 0.2
        
        return min(1.0, score)
    
    def _pattern_to_query(self, pattern: str) -> str:
        """Convert a pattern to a graph query"""
        # Simple pattern to Cypher query conversion
        # This is a simplified implementation
        
        if "inherits" in pattern.lower():
            return "MATCH (child)-[:INHERITS]->(parent) RETURN child, parent"
        elif "uses" in pattern.lower():
            return "MATCH (source)-[:USES]->(target) RETURN source, target"
        elif "contains" in pattern.lower():
            return "MATCH (container)-[:CONTAINS]->(contained) RETURN container, contained"
        else:
            # Default pattern
            return f"MATCH (n) WHERE n.name CONTAINS '{pattern}' RETURN n"
    
    async def _parse_natural_language_query(self, query_text: str) -> Dict[str, Any]:
        """Parse natural language query using LLM"""
        prompt = f"""
        Parse the following natural language query about a code knowledge graph:
        
        Query: "{query_text}"
        
        Extract:
        1. Query intent (search, relationship, path, exploration, pattern)
        2. Key entities or concepts mentioned
        3. Relationship types if any
        4. Filters or constraints
        
        Respond in JSON format with 'intent', 'entities', 'relationships', and 'filters'.
        """
        
        response = await llm_client.generate_response([
            {"role": "system", "content": "You are an expert in parsing queries for knowledge graphs."},
            {"role": "user", "content": prompt}
        ])
        
        try:
            import json
            return json.loads(response.content)
        except:
            # Fallback parsing
            return {
                "intent": "search",
                "entities": self._extract_search_terms(query_text),
                "relationships": [],
                "filters": {}
            }
    
    def _convert_to_structured_query(self, parsed_query: Dict[str, Any], original_query: SemanticQuery) -> SemanticQuery:
        """Convert parsed natural language to structured query"""
        intent = parsed_query.get("intent", "search")
        entities = parsed_query.get("entities", [])
        relationships = parsed_query.get("relationships", [])
        filters = parsed_query.get("filters", {})
        
        # Map intent to query type
        intent_mapping = {
            "search": QueryType.SEMANTIC_SEARCH,
            "relationship": QueryType.RELATIONSHIP_QUERY,
            "path": QueryType.PATH_FINDING,
            "exploration": QueryType.CONCEPT_EXPLORATION,
            "pattern": QueryType.PATTERN_MATCHING
        }
        
        query_type = intent_mapping.get(intent, QueryType.SEMANTIC_SEARCH)
        
        # Build parameters
        parameters = {}
        if relationships:
            parameters["relationship_type"] = relationships[0]
        if len(entities) >= 2 and query_type == QueryType.PATH_FINDING:
            parameters["source_id"] = entities[0]
            parameters["target_id"] = entities[1]
        elif entities and query_type == QueryType.CONCEPT_EXPLORATION:
            parameters["concept_id"] = entities[0]
        
        return SemanticQuery(
            query_text=" ".join(entities) if entities else original_query.query_text,
            query_type=query_type,
            parameters=parameters,
            filters=filters,
            max_results=original_query.max_results,
            confidence_threshold=original_query.confidence_threshold
        )
    
    def _load_query_patterns(self) -> Dict[str, str]:
        """Load query patterns for pattern matching"""
        return {
            "inheritance_hierarchy": "MATCH (child)-[:INHERITS*]->(parent) RETURN child, parent",
            "dependency_chain": "MATCH (source)-[:DEPENDS_ON*]->(target) RETURN source, target",
            "usage_patterns": "MATCH (user)-[:USES]->(used) RETURN user, used",
            "containment_tree": "MATCH (container)-[:CONTAINS*]->(contained) RETURN container, contained",
            "related_concepts": "MATCH (concept1)-[:RELATES_TO]-(concept2) RETURN concept1, concept2"
        }
    
    def _load_semantic_keywords(self) -> Dict[str, List[str]]:
        """Load semantic keywords for query enhancement"""
        return {
            "inheritance": ["extends", "inherits", "parent", "child", "subclass", "superclass"],
            "composition": ["contains", "has", "includes", "composed of", "part of"],
            "dependency": ["depends on", "uses", "requires", "imports", "calls"],
            "similarity": ["similar", "related", "like", "comparable", "equivalent"],
            "pattern": ["pattern", "design", "architecture", "structure", "template"]
        }
