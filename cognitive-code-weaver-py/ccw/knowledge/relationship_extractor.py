"""
Relationship Extractor for Knowledge Graph

Extracts and analyzes relationships between code elements, concepts,
and semantic entities to build comprehensive knowledge graphs.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ccw.analysis.parser import Pa<PERSON>R<PERSON>ult
from ccw.analysis.symbols import Symbol, SymbolType
from ccw.analysis.dependency_graph import DependencyGraph
from ccw.knowledge.semantic_analyzer import Semantic<PERSON>oncept, SemanticRelationship

logger = logging.getLogger(__name__)


class RelationshipType(Enum):
    """Types of relationships in the knowledge graph"""
    # Code structure relationships
    INHERITS = "inherits"
    IMPLEMENTS = "implements"
    CONTAINS = "contains"
    USES = "uses"
    CALLS = "calls"
    IMPORTS = "imports"
    DEPENDS_ON = "depends_on"
    
    # Semantic relationships
    RELATES_TO = "relates_to"
    SIMILAR_TO = "similar_to"
    PART_OF = "part_of"
    INSTANCE_OF = "instance_of"
    
    # Functional relationships
    PROCESSES = "processes"
    TRANSFORMS = "transforms"
    VALIDATES = "validates"
    CREATES = "creates"
    MODIFIES = "modifies"
    
    # Architectural relationships
    LAYER_OF = "layer_of"
    COMPONENT_OF = "component_of"
    SERVICE_FOR = "service_for"
    INTERFACE_FOR = "interface_for"
    
    # Business relationships
    BUSINESS_RULE = "business_rule"
    WORKFLOW_STEP = "workflow_step"
    DECISION_POINT = "decision_point"
    DATA_FLOW = "data_flow"


@dataclass
class CodeRelationship:
    """Represents a relationship between code elements"""
    id: str
    source_id: str
    target_id: str
    relationship_type: RelationshipType
    strength: float
    confidence: float
    evidence: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


class RelationshipExtractor:
    """Extracts relationships between code elements and concepts"""
    
    def __init__(self):
        self.relationship_patterns = self._load_relationship_patterns()
        self.semantic_patterns = self._load_semantic_patterns()
    
    def extract_relationships(self, parse_results: List[ParseResult], 
                            symbols: List[Symbol],
                            dependency_graph: DependencyGraph,
                            concepts: List[SemanticConcept]) -> List[CodeRelationship]:
        """Extract all types of relationships"""
        relationships = []
        
        # Extract structural relationships
        relationships.extend(self._extract_structural_relationships(symbols))
        
        # Extract dependency relationships
        relationships.extend(self._extract_dependency_relationships(dependency_graph))
        
        # Extract semantic relationships
        relationships.extend(self._extract_semantic_relationships(concepts, symbols))
        
        # Extract functional relationships
        relationships.extend(self._extract_functional_relationships(parse_results, symbols))
        
        # Extract architectural relationships
        relationships.extend(self._extract_architectural_relationships(symbols, parse_results))
        
        # Deduplicate and merge similar relationships
        relationships = self._merge_relationships(relationships)
        
        return relationships
    
    def _extract_structural_relationships(self, symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract structural relationships from code symbols"""
        relationships = []
        
        # Group symbols by file for context
        symbols_by_file = {}
        for symbol in symbols:
            if symbol.file_path not in symbols_by_file:
                symbols_by_file[symbol.file_path] = []
            symbols_by_file[symbol.file_path].append(symbol)
        
        # Extract inheritance relationships
        for symbol in symbols:
            if symbol.symbol_type == SymbolType.CLASS:
                inheritance_rels = self._extract_inheritance_relationships(symbol, symbols)
                relationships.extend(inheritance_rels)
        
        # Extract containment relationships
        for file_path, file_symbols in symbols_by_file.items():
            containment_rels = self._extract_containment_relationships(file_symbols)
            relationships.extend(containment_rels)
        
        # Extract usage relationships
        for symbol in symbols:
            if symbol.symbol_type == SymbolType.FUNCTION:
                usage_rels = self._extract_usage_relationships(symbol, symbols)
                relationships.extend(usage_rels)
        
        return relationships
    
    def _extract_inheritance_relationships(self, class_symbol: Symbol, all_symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract inheritance relationships for a class"""
        relationships = []
        
        # Check metadata for inheritance information
        if 'extends' in class_symbol.metadata:
            parent_class = class_symbol.metadata['extends']
            
            # Find parent class symbol
            parent_symbol = next((s for s in all_symbols 
                                if s.name == parent_class and s.symbol_type == SymbolType.CLASS), None)
            
            if parent_symbol:
                relationship = CodeRelationship(
                    id=f"inherits_{class_symbol.name}_{parent_class}",
                    source_id=class_symbol.name,
                    target_id=parent_symbol.name,
                    relationship_type=RelationshipType.INHERITS,
                    strength=1.0,
                    confidence=0.9,
                    evidence=[f"Class {class_symbol.name} extends {parent_class}"],
                    metadata={
                        "source_file": class_symbol.file_path,
                        "target_file": parent_symbol.file_path,
                        "line_number": class_symbol.line_number
                    }
                )
                relationships.append(relationship)
        
        # Check for interface implementation
        if 'implements' in class_symbol.metadata:
            interfaces = class_symbol.metadata['implements']
            if isinstance(interfaces, str):
                interfaces = [interfaces]
            
            for interface in interfaces:
                interface_symbol = next((s for s in all_symbols 
                                       if s.name == interface and s.symbol_type == SymbolType.CLASS), None)
                
                if interface_symbol:
                    relationship = CodeRelationship(
                        id=f"implements_{class_symbol.name}_{interface}",
                        source_id=class_symbol.name,
                        target_id=interface_symbol.name,
                        relationship_type=RelationshipType.IMPLEMENTS,
                        strength=1.0,
                        confidence=0.9,
                        evidence=[f"Class {class_symbol.name} implements {interface}"],
                        metadata={
                            "source_file": class_symbol.file_path,
                            "target_file": interface_symbol.file_path
                        }
                    )
                    relationships.append(relationship)
        
        return relationships
    
    def _extract_containment_relationships(self, file_symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract containment relationships within a file"""
        relationships = []
        
        # Find classes and their methods
        classes = [s for s in file_symbols if s.symbol_type == SymbolType.CLASS]
        functions = [s for s in file_symbols if s.symbol_type == SymbolType.FUNCTION]
        
        for cls in classes:
            # Find methods that belong to this class
            class_methods = [f for f in functions if self._is_method_of_class(f, cls)]
            
            for method in class_methods:
                relationship = CodeRelationship(
                    id=f"contains_{cls.name}_{method.name}",
                    source_id=cls.name,
                    target_id=method.name,
                    relationship_type=RelationshipType.CONTAINS,
                    strength=1.0,
                    confidence=0.95,
                    evidence=[f"Method {method.name} is contained in class {cls.name}"],
                    metadata={
                        "file_path": cls.file_path,
                        "class_line": cls.line_number,
                        "method_line": method.line_number
                    }
                )
                relationships.append(relationship)
        
        return relationships
    
    def _extract_usage_relationships(self, function_symbol: Symbol, all_symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract usage relationships for a function"""
        relationships = []
        
        # Analyze function signature and body for usage patterns
        if function_symbol.signature:
            # Extract parameter types and return types
            used_types = self._extract_types_from_signature(function_symbol.signature)
            
            for used_type in used_types:
                type_symbol = next((s for s in all_symbols 
                                  if s.name == used_type and s.symbol_type == SymbolType.CLASS), None)
                
                if type_symbol:
                    relationship = CodeRelationship(
                        id=f"uses_{function_symbol.name}_{used_type}",
                        source_id=function_symbol.name,
                        target_id=type_symbol.name,
                        relationship_type=RelationshipType.USES,
                        strength=0.8,
                        confidence=0.7,
                        evidence=[f"Function {function_symbol.name} uses type {used_type}"],
                        metadata={
                            "usage_context": "parameter_or_return_type",
                            "signature": function_symbol.signature
                        }
                    )
                    relationships.append(relationship)
        
        return relationships
    
    def _extract_dependency_relationships(self, dependency_graph: DependencyGraph) -> List[CodeRelationship]:
        """Extract relationships from dependency graph"""
        relationships = []
        
        # Convert dependency graph edges to relationships
        for source, targets in dependency_graph.dependencies.items():
            for target, dependency_info in targets.items():
                relationship = CodeRelationship(
                    id=f"depends_{source}_{target}",
                    source_id=source,
                    target_id=target,
                    relationship_type=RelationshipType.DEPENDS_ON,
                    strength=dependency_info.get('strength', 1.0),
                    confidence=0.9,
                    evidence=[f"Module {source} depends on {target}"],
                    metadata={
                        "dependency_type": dependency_info.get('type', 'import'),
                        "import_statement": dependency_info.get('import_statement', '')
                    }
                )
                relationships.append(relationship)
        
        return relationships
    
    def _extract_semantic_relationships(self, concepts: List[SemanticConcept], symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract semantic relationships between concepts and code elements"""
        relationships = []
        
        # Link concepts to code elements
        for concept in concepts:
            for source_element in concept.source_elements:
                # Find symbols in the same file
                related_symbols = [s for s in symbols if s.file_path == source_element]
                
                for symbol in related_symbols:
                    # Check if symbol name relates to concept
                    if self._symbol_relates_to_concept(symbol, concept):
                        relationship = CodeRelationship(
                            id=f"concept_{concept.id}_{symbol.name}",
                            source_id=concept.id,
                            target_id=symbol.name,
                            relationship_type=RelationshipType.RELATES_TO,
                            strength=concept.confidence,
                            confidence=0.8,
                            evidence=[f"Concept {concept.name} relates to symbol {symbol.name}"],
                            metadata={
                                "concept_type": concept.concept_type.value,
                                "symbol_type": symbol.symbol_type.value
                            }
                        )
                        relationships.append(relationship)
        
        # Extract relationships between concepts
        for i, concept_a in enumerate(concepts):
            for concept_b in concepts[i+1:]:
                semantic_rel = self._analyze_concept_similarity(concept_a, concept_b)
                if semantic_rel:
                    relationships.append(semantic_rel)
        
        return relationships
    
    def _extract_functional_relationships(self, parse_results: List[ParseResult], symbols: List[Symbol]) -> List[CodeRelationship]:
        """Extract functional relationships based on code analysis"""
        relationships = []
        
        # Analyze function calls and data flow
        for parse_result in parse_results:
            function_calls = self._extract_function_calls(parse_result)
            
            for call in function_calls:
                caller_symbol = next((s for s in symbols 
                                    if s.file_path == parse_result.file_path and 
                                    call['line'] >= s.line_number), None)
                
                callee_symbol = next((s for s in symbols 
                                    if s.name == call['function']), None)
                
                if caller_symbol and callee_symbol:
                    relationship = CodeRelationship(
                        id=f"calls_{caller_symbol.name}_{callee_symbol.name}",
                        source_id=caller_symbol.name,
                        target_id=callee_symbol.name,
                        relationship_type=RelationshipType.CALLS,
                        strength=1.0,
                        confidence=0.8,
                        evidence=[f"Function {caller_symbol.name} calls {callee_symbol.name}"],
                        metadata={
                            "call_line": call['line'],
                            "call_context": call.get('context', '')
                        }
                    )
                    relationships.append(relationship)
        
        return relationships
    
    def _extract_architectural_relationships(self, symbols: List[Symbol], parse_results: List[ParseResult]) -> List[CodeRelationship]:
        """Extract architectural relationships and patterns"""
        relationships = []
        
        # Analyze layered architecture patterns
        layers = self._identify_architectural_layers(symbols)
        
        for layer_name, layer_symbols in layers.items():
            for symbol in layer_symbols:
                # Create layer relationships
                relationship = CodeRelationship(
                    id=f"layer_{symbol.name}_{layer_name}",
                    source_id=symbol.name,
                    target_id=f"layer_{layer_name}",
                    relationship_type=RelationshipType.LAYER_OF,
                    strength=1.0,
                    confidence=0.7,
                    evidence=[f"Symbol {symbol.name} belongs to {layer_name} layer"],
                    metadata={"layer": layer_name}
                )
                relationships.append(relationship)
        
        # Analyze service patterns
        services = self._identify_service_patterns(symbols)
        for service_symbol, served_symbols in services.items():
            for served_symbol in served_symbols:
                relationship = CodeRelationship(
                    id=f"service_{service_symbol.name}_{served_symbol.name}",
                    source_id=service_symbol.name,
                    target_id=served_symbol.name,
                    relationship_type=RelationshipType.SERVICE_FOR,
                    strength=0.8,
                    confidence=0.6,
                    evidence=[f"Service {service_symbol.name} serves {served_symbol.name}"],
                    metadata={"pattern": "service"}
                )
                relationships.append(relationship)
        
        return relationships
    
    def _is_method_of_class(self, function_symbol: Symbol, class_symbol: Symbol) -> bool:
        """Check if a function is a method of a class"""
        # Simple heuristic: method is defined after class and before next class
        return (function_symbol.file_path == class_symbol.file_path and
                function_symbol.line_number > class_symbol.line_number)
    
    def _extract_types_from_signature(self, signature: str) -> List[str]:
        """Extract type names from function signature"""
        # Simple regex-based type extraction
        type_pattern = r'\b([A-Z][a-zA-Z0-9_]*)\b'
        types = re.findall(type_pattern, signature)
        return list(set(types))
    
    def _symbol_relates_to_concept(self, symbol: Symbol, concept: SemanticConcept) -> bool:
        """Check if a symbol relates to a concept"""
        symbol_words = set(re.findall(r'\w+', symbol.name.lower()))
        concept_words = set(word.lower() for word in concept.keywords)
        
        # Check for word overlap
        overlap = symbol_words & concept_words
        return len(overlap) > 0
    
    def _analyze_concept_similarity(self, concept_a: SemanticConcept, concept_b: SemanticConcept) -> Optional[CodeRelationship]:
        """Analyze similarity between two concepts"""
        # Calculate similarity based on keywords and context
        keywords_a = set(word.lower() for word in concept_a.keywords)
        keywords_b = set(word.lower() for word in concept_b.keywords)
        
        overlap = keywords_a & keywords_b
        union = keywords_a | keywords_b
        
        if len(union) > 0:
            similarity = len(overlap) / len(union)
            
            if similarity > 0.3:  # Threshold for similarity
                relationship = CodeRelationship(
                    id=f"similar_{concept_a.id}_{concept_b.id}",
                    source_id=concept_a.id,
                    target_id=concept_b.id,
                    relationship_type=RelationshipType.SIMILAR_TO,
                    strength=similarity,
                    confidence=0.7,
                    evidence=[f"Concepts share {len(overlap)} keywords"],
                    metadata={
                        "similarity_score": similarity,
                        "shared_keywords": list(overlap)
                    }
                )
                return relationship
        
        return None
    
    def _extract_function_calls(self, parse_result: ParseResult) -> List[Dict[str, Any]]:
        """Extract function calls from parsed code"""
        calls = []
        lines = parse_result.content.split('\n')
        
        # Simple regex-based function call extraction
        call_pattern = r'(\w+)\s*\('
        
        for i, line in enumerate(lines):
            matches = re.finditer(call_pattern, line)
            for match in matches:
                calls.append({
                    'function': match.group(1),
                    'line': i + 1,
                    'context': line.strip()
                })
        
        return calls
    
    def _identify_architectural_layers(self, symbols: List[Symbol]) -> Dict[str, List[Symbol]]:
        """Identify architectural layers from symbols"""
        layers = {
            'presentation': [],
            'business': [],
            'data': [],
            'infrastructure': []
        }
        
        layer_patterns = {
            'presentation': ['controller', 'view', 'ui', 'web', 'api', 'endpoint'],
            'business': ['service', 'manager', 'processor', 'handler', 'logic'],
            'data': ['repository', 'dao', 'model', 'entity', 'database'],
            'infrastructure': ['config', 'util', 'helper', 'client', 'adapter']
        }
        
        for symbol in symbols:
            symbol_name_lower = symbol.name.lower()
            
            for layer, patterns in layer_patterns.items():
                if any(pattern in symbol_name_lower for pattern in patterns):
                    layers[layer].append(symbol)
                    break
        
        return layers
    
    def _identify_service_patterns(self, symbols: List[Symbol]) -> Dict[Symbol, List[Symbol]]:
        """Identify service patterns"""
        services = {}
        
        service_symbols = [s for s in symbols if 'service' in s.name.lower()]
        
        for service in service_symbols:
            # Find symbols that might be served by this service
            served = []
            service_domain = service.name.lower().replace('service', '').strip()
            
            for symbol in symbols:
                if (service_domain in symbol.name.lower() and 
                    symbol != service and 
                    symbol.file_path != service.file_path):
                    served.append(symbol)
            
            if served:
                services[service] = served
        
        return services
    
    def _merge_relationships(self, relationships: List[CodeRelationship]) -> List[CodeRelationship]:
        """Merge similar relationships to reduce duplication"""
        merged = []
        relationship_groups = {}
        
        # Group by source, target, and type
        for rel in relationships:
            key = (rel.source_id, rel.target_id, rel.relationship_type)
            if key not in relationship_groups:
                relationship_groups[key] = []
            relationship_groups[key].append(rel)
        
        # Merge groups
        for group in relationship_groups.values():
            if len(group) == 1:
                merged.append(group[0])
            else:
                # Merge multiple relationships
                base_rel = group[0]
                for other in group[1:]:
                    base_rel.evidence.extend(other.evidence)
                    base_rel.strength = max(base_rel.strength, other.strength)
                    base_rel.confidence = max(base_rel.confidence, other.confidence)
                    base_rel.metadata.update(other.metadata)
                
                # Remove duplicate evidence
                base_rel.evidence = list(set(base_rel.evidence))
                
                merged.append(base_rel)
        
        return merged
    
    def _load_relationship_patterns(self) -> Dict[str, RelationshipType]:
        """Load relationship patterns"""
        return {
            'extends': RelationshipType.INHERITS,
            'implements': RelationshipType.IMPLEMENTS,
            'uses': RelationshipType.USES,
            'calls': RelationshipType.CALLS,
            'imports': RelationshipType.IMPORTS,
            'contains': RelationshipType.CONTAINS,
            'depends': RelationshipType.DEPENDS_ON
        }
    
    def _load_semantic_patterns(self) -> Dict[str, RelationshipType]:
        """Load semantic relationship patterns"""
        return {
            'processes': RelationshipType.PROCESSES,
            'transforms': RelationshipType.TRANSFORMS,
            'validates': RelationshipType.VALIDATES,
            'creates': RelationshipType.CREATES,
            'modifies': RelationshipType.MODIFIES
        }
