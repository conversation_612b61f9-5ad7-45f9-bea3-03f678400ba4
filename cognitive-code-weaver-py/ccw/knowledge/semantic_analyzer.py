"""
Semantic Analyzer for Code Understanding

Extracts semantic concepts, relationships, and meaning from code using
NLP techniques, LLM analysis, and pattern recognition.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ccw.analysis.parser import ParseResult
from ccw.analysis.symbols import Symbol, SymbolType
from ccw.llm.client import llm_client

logger = logging.getLogger(__name__)


class ConceptType(Enum):
    """Types of semantic concepts"""
    DOMAIN_CONCEPT = "domain_concept"
    TECHNICAL_CONCEPT = "technical_concept"
    BUSINESS_LOGIC = "business_logic"
    DATA_STRUCTURE = "data_structure"
    ALGORITHM = "algorithm"
    PATTERN = "pattern"
    RESPONSIBILITY = "responsibility"
    ABSTRACTION = "abstraction"


@dataclass
class SemanticConcept:
    """Represents a semantic concept extracted from code"""
    id: str
    name: str
    concept_type: ConceptType
    description: str
    confidence: float
    source_elements: List[str] = field(default_factory=list)  # Source code elements
    keywords: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class SemanticRelationship:
    """Represents a semantic relationship between concepts"""
    source_concept: str
    target_concept: str
    relationship_type: str
    strength: float
    evidence: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)


class ConceptExtractor:
    """Extracts semantic concepts from code elements"""
    
    def __init__(self):
        self.domain_patterns = self._load_domain_patterns()
        self.technical_patterns = self._load_technical_patterns()
        self.business_patterns = self._load_business_patterns()
    
    def extract_concepts(self, parse_result: ParseResult, symbols: List[Symbol]) -> List[SemanticConcept]:
        """Extract semantic concepts from parsed code and symbols"""
        concepts = []
        
        # Extract concepts from different sources
        concepts.extend(self._extract_from_names(symbols))
        concepts.extend(self._extract_from_comments(parse_result))
        concepts.extend(self._extract_from_docstrings(parse_result))
        concepts.extend(self._extract_from_patterns(parse_result, symbols))
        
        # Deduplicate and merge similar concepts
        concepts = self._merge_similar_concepts(concepts)
        
        return concepts
    
    def _extract_from_names(self, symbols: List[Symbol]) -> List[SemanticConcept]:
        """Extract concepts from symbol names"""
        concepts = []
        
        for symbol in symbols:
            # Analyze symbol name for semantic meaning
            name_concepts = self._analyze_name_semantics(symbol.name, symbol)
            concepts.extend(name_concepts)
        
        return concepts
    
    def _extract_from_comments(self, parse_result: ParseResult) -> List[SemanticConcept]:
        """Extract concepts from code comments"""
        concepts = []
        content = parse_result.content
        
        # Find comments
        comment_patterns = {
            'python': r'#\s*(.+)',
            'javascript': r'//\s*(.+)',
            'java': r'//\s*(.+)'
        }
        
        pattern = comment_patterns.get(parse_result.language.value, r'#\s*(.+)')
        comments = re.findall(pattern, content, re.MULTILINE)
        
        for comment in comments:
            comment_concepts = self._analyze_text_for_concepts(comment, ConceptType.DOMAIN_CONCEPT)
            concepts.extend(comment_concepts)
        
        return concepts
    
    def _extract_from_docstrings(self, parse_result: ParseResult) -> List[SemanticConcept]:
        """Extract concepts from docstrings"""
        concepts = []
        
        if parse_result.language.value == 'python':
            # Extract Python docstrings
            docstring_pattern = r'"""(.*?)"""'
            docstrings = re.findall(docstring_pattern, parse_result.content, re.DOTALL)
            
            for docstring in docstrings:
                doc_concepts = self._analyze_text_for_concepts(docstring, ConceptType.BUSINESS_LOGIC)
                concepts.extend(doc_concepts)
        
        return concepts
    
    def _extract_from_patterns(self, parse_result: ParseResult, symbols: List[Symbol]) -> List[SemanticConcept]:
        """Extract concepts from code patterns"""
        concepts = []
        
        # Analyze class hierarchies for patterns
        classes = [s for s in symbols if s.symbol_type == SymbolType.CLASS]
        for cls in classes:
            pattern_concepts = self._analyze_class_patterns(cls, symbols)
            concepts.extend(pattern_concepts)
        
        # Analyze function patterns
        functions = [s for s in symbols if s.symbol_type == SymbolType.FUNCTION]
        for func in functions:
            pattern_concepts = self._analyze_function_patterns(func, parse_result)
            concepts.extend(pattern_concepts)
        
        return concepts
    
    def _analyze_name_semantics(self, name: str, symbol: Symbol) -> List[SemanticConcept]:
        """Analyze semantic meaning of a name"""
        concepts = []
        
        # Split camelCase and snake_case names
        words = self._split_identifier(name)
        
        # Check against domain patterns
        for pattern, concept_type in self.domain_patterns.items():
            if any(word.lower() in pattern.lower() for word in words):
                concept = SemanticConcept(
                    id=f"concept_{name}_{concept_type.value}",
                    name=pattern,
                    concept_type=concept_type,
                    description=f"Domain concept extracted from {name}",
                    confidence=0.7,
                    source_elements=[symbol.file_path],
                    keywords=words
                )
                concepts.append(concept)
        
        # Check for technical concepts
        for pattern, concept_type in self.technical_patterns.items():
            if any(word.lower() in pattern.lower() for word in words):
                concept = SemanticConcept(
                    id=f"concept_{name}_{concept_type.value}",
                    name=pattern,
                    concept_type=concept_type,
                    description=f"Technical concept extracted from {name}",
                    confidence=0.8,
                    source_elements=[symbol.file_path],
                    keywords=words
                )
                concepts.append(concept)
        
        return concepts
    
    def _analyze_text_for_concepts(self, text: str, default_type: ConceptType) -> List[SemanticConcept]:
        """Analyze text content for semantic concepts"""
        concepts = []
        
        # Extract key phrases and terms
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Look for business/domain terms
        business_terms = []
        for word in words:
            if word in self.business_patterns:
                business_terms.append(word)
        
        if business_terms:
            concept = SemanticConcept(
                id=f"concept_text_{hash(text)}",
                name=" ".join(business_terms),
                concept_type=default_type,
                description=f"Concept extracted from text: {text[:100]}...",
                confidence=0.6,
                keywords=business_terms,
                context={"source_text": text}
            )
            concepts.append(concept)
        
        return concepts
    
    def _analyze_class_patterns(self, cls: Symbol, symbols: List[Symbol]) -> List[SemanticConcept]:
        """Analyze class for design patterns and concepts"""
        concepts = []
        
        # Check for common design patterns
        class_name = cls.name.lower()
        
        pattern_indicators = {
            'factory': ConceptType.PATTERN,
            'builder': ConceptType.PATTERN,
            'singleton': ConceptType.PATTERN,
            'observer': ConceptType.PATTERN,
            'strategy': ConceptType.PATTERN,
            'adapter': ConceptType.PATTERN,
            'decorator': ConceptType.PATTERN,
            'facade': ConceptType.PATTERN,
            'proxy': ConceptType.PATTERN,
            'manager': ConceptType.RESPONSIBILITY,
            'controller': ConceptType.RESPONSIBILITY,
            'service': ConceptType.RESPONSIBILITY,
            'repository': ConceptType.RESPONSIBILITY,
            'handler': ConceptType.RESPONSIBILITY
        }
        
        for pattern, concept_type in pattern_indicators.items():
            if pattern in class_name:
                concept = SemanticConcept(
                    id=f"pattern_{cls.name}_{pattern}",
                    name=f"{pattern.title()} Pattern",
                    concept_type=concept_type,
                    description=f"{pattern.title()} pattern detected in class {cls.name}",
                    confidence=0.8,
                    source_elements=[cls.file_path],
                    keywords=[pattern, cls.name]
                )
                concepts.append(concept)
        
        return concepts
    
    def _analyze_function_patterns(self, func: Symbol, parse_result: ParseResult) -> List[SemanticConcept]:
        """Analyze function for algorithmic and behavioral patterns"""
        concepts = []
        
        func_name = func.name.lower()
        
        # Algorithm patterns
        algorithm_patterns = {
            'sort': ConceptType.ALGORITHM,
            'search': ConceptType.ALGORITHM,
            'filter': ConceptType.ALGORITHM,
            'map': ConceptType.ALGORITHM,
            'reduce': ConceptType.ALGORITHM,
            'validate': ConceptType.BUSINESS_LOGIC,
            'process': ConceptType.BUSINESS_LOGIC,
            'calculate': ConceptType.BUSINESS_LOGIC,
            'transform': ConceptType.DATA_STRUCTURE,
            'parse': ConceptType.DATA_STRUCTURE,
            'serialize': ConceptType.DATA_STRUCTURE
        }
        
        for pattern, concept_type in algorithm_patterns.items():
            if pattern in func_name:
                concept = SemanticConcept(
                    id=f"algorithm_{func.name}_{pattern}",
                    name=f"{pattern.title()} Algorithm",
                    concept_type=concept_type,
                    description=f"{pattern.title()} algorithm detected in function {func.name}",
                    confidence=0.7,
                    source_elements=[func.file_path],
                    keywords=[pattern, func.name]
                )
                concepts.append(concept)
        
        return concepts
    
    def _split_identifier(self, identifier: str) -> List[str]:
        """Split camelCase and snake_case identifiers into words"""
        # Handle camelCase
        words = re.sub(r'([a-z])([A-Z])', r'\1 \2', identifier).split()
        
        # Handle snake_case
        all_words = []
        for word in words:
            all_words.extend(word.split('_'))
        
        return [w for w in all_words if w]
    
    def _merge_similar_concepts(self, concepts: List[SemanticConcept]) -> List[SemanticConcept]:
        """Merge similar concepts to reduce duplication"""
        merged = []
        concept_groups = {}
        
        # Group by name and type
        for concept in concepts:
            key = (concept.name.lower(), concept.concept_type)
            if key not in concept_groups:
                concept_groups[key] = []
            concept_groups[key].append(concept)
        
        # Merge groups
        for group in concept_groups.values():
            if len(group) == 1:
                merged.append(group[0])
            else:
                # Merge multiple concepts
                base_concept = group[0]
                for other in group[1:]:
                    base_concept.source_elements.extend(other.source_elements)
                    base_concept.keywords.extend(other.keywords)
                    base_concept.confidence = max(base_concept.confidence, other.confidence)
                
                # Remove duplicates
                base_concept.source_elements = list(set(base_concept.source_elements))
                base_concept.keywords = list(set(base_concept.keywords))
                
                merged.append(base_concept)
        
        return merged
    
    def _load_domain_patterns(self) -> Dict[str, ConceptType]:
        """Load domain-specific patterns"""
        return {
            'user': ConceptType.DOMAIN_CONCEPT,
            'customer': ConceptType.DOMAIN_CONCEPT,
            'order': ConceptType.DOMAIN_CONCEPT,
            'product': ConceptType.DOMAIN_CONCEPT,
            'payment': ConceptType.DOMAIN_CONCEPT,
            'account': ConceptType.DOMAIN_CONCEPT,
            'profile': ConceptType.DOMAIN_CONCEPT,
            'session': ConceptType.DOMAIN_CONCEPT,
            'authentication': ConceptType.DOMAIN_CONCEPT,
            'authorization': ConceptType.DOMAIN_CONCEPT,
            'notification': ConceptType.DOMAIN_CONCEPT,
            'message': ConceptType.DOMAIN_CONCEPT,
            'email': ConceptType.DOMAIN_CONCEPT,
            'report': ConceptType.DOMAIN_CONCEPT,
            'dashboard': ConceptType.DOMAIN_CONCEPT,
            'analytics': ConceptType.DOMAIN_CONCEPT
        }
    
    def _load_technical_patterns(self) -> Dict[str, ConceptType]:
        """Load technical patterns"""
        return {
            'database': ConceptType.TECHNICAL_CONCEPT,
            'cache': ConceptType.TECHNICAL_CONCEPT,
            'queue': ConceptType.TECHNICAL_CONCEPT,
            'api': ConceptType.TECHNICAL_CONCEPT,
            'endpoint': ConceptType.TECHNICAL_CONCEPT,
            'middleware': ConceptType.TECHNICAL_CONCEPT,
            'router': ConceptType.TECHNICAL_CONCEPT,
            'handler': ConceptType.TECHNICAL_CONCEPT,
            'service': ConceptType.TECHNICAL_CONCEPT,
            'client': ConceptType.TECHNICAL_CONCEPT,
            'server': ConceptType.TECHNICAL_CONCEPT,
            'connection': ConceptType.TECHNICAL_CONCEPT,
            'configuration': ConceptType.TECHNICAL_CONCEPT,
            'logger': ConceptType.TECHNICAL_CONCEPT,
            'monitor': ConceptType.TECHNICAL_CONCEPT,
            'security': ConceptType.TECHNICAL_CONCEPT
        }
    
    def _load_business_patterns(self) -> Set[str]:
        """Load business domain terms"""
        return {
            'business', 'process', 'workflow', 'rule', 'policy', 'procedure',
            'requirement', 'specification', 'constraint', 'validation',
            'calculation', 'computation', 'algorithm', 'logic', 'decision',
            'approval', 'rejection', 'status', 'state', 'transition',
            'event', 'trigger', 'action', 'response', 'result', 'outcome'
        }


class SemanticAnalyzer:
    """Main semantic analyzer that coordinates concept extraction and analysis"""
    
    def __init__(self):
        self.concept_extractor = ConceptExtractor()
        self.relationship_patterns = self._load_relationship_patterns()
    
    async def analyze_semantics(self, parse_result: ParseResult, symbols: List[Symbol]) -> Tuple[List[SemanticConcept], List[SemanticRelationship]]:
        """Perform comprehensive semantic analysis"""
        # Extract concepts
        concepts = self.concept_extractor.extract_concepts(parse_result, symbols)
        
        # Extract relationships between concepts
        relationships = await self._extract_relationships(concepts, parse_result, symbols)
        
        # Enhance with LLM analysis if available
        enhanced_concepts, enhanced_relationships = await self._enhance_with_llm(
            concepts, relationships, parse_result
        )
        
        return enhanced_concepts, enhanced_relationships
    
    async def _extract_relationships(self, concepts: List[SemanticConcept], 
                                   parse_result: ParseResult, symbols: List[Symbol]) -> List[SemanticRelationship]:
        """Extract relationships between concepts"""
        relationships = []
        
        # Analyze co-occurrence patterns
        for i, concept_a in enumerate(concepts):
            for concept_b in concepts[i+1:]:
                relationship = self._analyze_concept_relationship(concept_a, concept_b, parse_result)
                if relationship:
                    relationships.append(relationship)
        
        return relationships
    
    def _analyze_concept_relationship(self, concept_a: SemanticConcept, concept_b: SemanticConcept, 
                                    parse_result: ParseResult) -> Optional[SemanticRelationship]:
        """Analyze relationship between two concepts"""
        # Check for common source elements
        common_sources = set(concept_a.source_elements) & set(concept_b.source_elements)
        if not common_sources:
            return None
        
        # Determine relationship type based on concept types and patterns
        relationship_type = self._determine_relationship_type(concept_a, concept_b)
        
        if relationship_type:
            strength = len(common_sources) / max(len(concept_a.source_elements), len(concept_b.source_elements))
            
            return SemanticRelationship(
                source_concept=concept_a.id,
                target_concept=concept_b.id,
                relationship_type=relationship_type,
                strength=strength,
                evidence=list(common_sources),
                context={"analysis_method": "co_occurrence"}
            )
        
        return None
    
    def _determine_relationship_type(self, concept_a: SemanticConcept, concept_b: SemanticConcept) -> Optional[str]:
        """Determine the type of relationship between concepts"""
        type_a, type_b = concept_a.concept_type, concept_b.concept_type
        
        # Define relationship rules
        relationship_rules = {
            (ConceptType.DOMAIN_CONCEPT, ConceptType.TECHNICAL_CONCEPT): "implements",
            (ConceptType.BUSINESS_LOGIC, ConceptType.ALGORITHM): "uses",
            (ConceptType.PATTERN, ConceptType.RESPONSIBILITY): "defines",
            (ConceptType.DATA_STRUCTURE, ConceptType.ALGORITHM): "processes",
            (ConceptType.ABSTRACTION, ConceptType.DOMAIN_CONCEPT): "abstracts"
        }
        
        # Check direct rules
        if (type_a, type_b) in relationship_rules:
            return relationship_rules[(type_a, type_b)]
        elif (type_b, type_a) in relationship_rules:
            return relationship_rules[(type_b, type_a)]
        
        # Default relationships
        if type_a == type_b:
            return "relates_to"
        
        return "associated_with"
    
    async def _enhance_with_llm(self, concepts: List[SemanticConcept], 
                               relationships: List[SemanticRelationship],
                               parse_result: ParseResult) -> Tuple[List[SemanticConcept], List[SemanticRelationship]]:
        """Enhance analysis with LLM insights"""
        try:
            # Create prompt for LLM analysis
            prompt = self._create_semantic_analysis_prompt(concepts, parse_result)
            
            # Get LLM response
            response = await llm_client.generate_response([
                {"role": "system", "content": "You are an expert code analyst specializing in semantic understanding."},
                {"role": "user", "content": prompt}
            ])
            
            # Parse LLM response to enhance concepts and relationships
            enhanced_concepts, enhanced_relationships = self._parse_llm_response(
                response.content, concepts, relationships
            )
            
            return enhanced_concepts, enhanced_relationships
            
        except Exception as e:
            logger.warning(f"LLM enhancement failed: {e}")
            return concepts, relationships
    
    def _create_semantic_analysis_prompt(self, concepts: List[SemanticConcept], parse_result: ParseResult) -> str:
        """Create prompt for LLM semantic analysis"""
        concept_names = [c.name for c in concepts]
        
        return f"""
        Analyze the following code file for semantic concepts and relationships:
        
        File: {parse_result.file_path}
        Language: {parse_result.language.value}
        
        Extracted concepts: {', '.join(concept_names)}
        
        Code snippet:
        {parse_result.content[:1000]}...
        
        Please identify:
        1. Additional semantic concepts not captured
        2. Relationships between concepts
        3. Domain-specific patterns or architectures
        4. Business logic flows
        
        Respond in JSON format with 'concepts' and 'relationships' arrays.
        """
    
    def _parse_llm_response(self, response: str, existing_concepts: List[SemanticConcept], 
                           existing_relationships: List[SemanticRelationship]) -> Tuple[List[SemanticConcept], List[SemanticRelationship]]:
        """Parse LLM response to extract additional concepts and relationships"""
        try:
            import json
            data = json.loads(response)
            
            enhanced_concepts = existing_concepts.copy()
            enhanced_relationships = existing_relationships.copy()
            
            # Add new concepts from LLM
            for concept_data in data.get('concepts', []):
                concept = SemanticConcept(
                    id=f"llm_concept_{hash(concept_data['name'])}",
                    name=concept_data['name'],
                    concept_type=ConceptType(concept_data.get('type', 'domain_concept')),
                    description=concept_data.get('description', ''),
                    confidence=concept_data.get('confidence', 0.8),
                    keywords=concept_data.get('keywords', []),
                    context={"source": "llm_analysis"}
                )
                enhanced_concepts.append(concept)
            
            # Add new relationships from LLM
            for rel_data in data.get('relationships', []):
                relationship = SemanticRelationship(
                    source_concept=rel_data['source'],
                    target_concept=rel_data['target'],
                    relationship_type=rel_data['type'],
                    strength=rel_data.get('strength', 0.7),
                    evidence=rel_data.get('evidence', []),
                    context={"source": "llm_analysis"}
                )
                enhanced_relationships.append(relationship)
            
            return enhanced_concepts, enhanced_relationships
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            return existing_concepts, existing_relationships
    
    def _load_relationship_patterns(self) -> Dict[str, str]:
        """Load relationship patterns"""
        return {
            'inheritance': 'extends',
            'composition': 'contains',
            'aggregation': 'has',
            'dependency': 'uses',
            'association': 'relates_to',
            'implementation': 'implements',
            'instantiation': 'creates'
        }
