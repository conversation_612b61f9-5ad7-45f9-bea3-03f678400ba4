"""
Knowledge Graph Builder and Manager

Coordinates the construction and management of the knowledge graph,
integrating semantic analysis, relationship extraction, and graph storage.
"""

import logging
import uuid
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from ccw.analysis.parser import Pa<PERSON><PERSON><PERSON>ult
from ccw.analysis.symbols import Symbol
from ccw.analysis.dependency_graph import DependencyGraph
from ccw.knowledge.graph_database import GraphDatabase, GraphNode, GraphRelationship
from ccw.knowledge.semantic_analyzer import SemanticAnalyzer, SemanticConcept, SemanticRelationship
from ccw.knowledge.relationship_extractor import RelationshipExtractor, CodeRelationship, RelationshipType
from ccw.core.config import config

logger = logging.getLogger(__name__)


@dataclass
class KnowledgeGraphStats:
    """Statistics about the knowledge graph"""
    total_nodes: int = 0
    total_relationships: int = 0
    node_types: Dict[str, int] = field(default_factory=dict)
    relationship_types: Dict[str, int] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)


class KnowledgeGraph:
    """Main knowledge graph interface"""
    
    def __init__(self, database: GraphDatabase):
        self.database = database
        self.stats = KnowledgeGraphStats()
        self._node_cache: Dict[str, GraphNode] = {}
        self._relationship_cache: Dict[str, GraphRelationship] = {}
    
    async def initialize(self) -> bool:
        """Initialize the knowledge graph"""
        try:
            success = await self.database.connect()
            if success:
                await self._load_stats()
                logger.info("Knowledge graph initialized successfully")
            return success
        except Exception as e:
            logger.error(f"Failed to initialize knowledge graph: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown the knowledge graph"""
        try:
            await self.database.disconnect()
            self._node_cache.clear()
            self._relationship_cache.clear()
            logger.info("Knowledge graph shutdown completed")
        except Exception as e:
            logger.error(f"Error during knowledge graph shutdown: {e}")
    
    async def add_node(self, node: GraphNode) -> bool:
        """Add a node to the knowledge graph"""
        try:
            success = await self.database.create_node(node)
            if success:
                self._node_cache[node.id] = node
                self.stats.total_nodes += 1
                self.stats.node_types[node.node_type] = self.stats.node_types.get(node.node_type, 0) + 1
                self.stats.last_updated = datetime.now()
            return success
        except Exception as e:
            logger.error(f"Failed to add node {node.id}: {e}")
            return False
    
    async def add_relationship(self, relationship: GraphRelationship) -> bool:
        """Add a relationship to the knowledge graph"""
        try:
            success = await self.database.create_relationship(relationship)
            if success:
                self._relationship_cache[relationship.id] = relationship
                self.stats.total_relationships += 1
                self.stats.relationship_types[relationship.relationship_type] = \
                    self.stats.relationship_types.get(relationship.relationship_type, 0) + 1
                self.stats.last_updated = datetime.now()
            return success
        except Exception as e:
            logger.error(f"Failed to add relationship {relationship.id}: {e}")
            return False
    
    async def get_node(self, node_id: str) -> Optional[GraphNode]:
        """Get a node by ID"""
        # Check cache first
        if node_id in self._node_cache:
            return self._node_cache[node_id]
        
        # Query database
        node = await self.database.get_node(node_id)
        if node:
            self._node_cache[node_id] = node
        
        return node
    
    async def get_nodes_by_type(self, node_type: str) -> List[GraphNode]:
        """Get all nodes of a specific type"""
        return await self.database.get_nodes_by_type(node_type)
    
    async def get_relationships(self, source_id: str, target_id: Optional[str] = None) -> List[GraphRelationship]:
        """Get relationships for a node"""
        return await self.database.get_relationships(source_id, target_id)
    
    async def query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a graph query"""
        return await self.database.query(query, parameters)
    
    async def find_related_concepts(self, concept_id: str, max_depth: int = 2) -> List[GraphNode]:
        """Find concepts related to a given concept"""
        related = []
        visited = set()
        queue = [(concept_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if current_id in visited or depth > max_depth:
                continue
            
            visited.add(current_id)
            
            # Get node
            node = await self.get_node(current_id)
            if node and depth > 0:  # Don't include the starting node
                related.append(node)
            
            # Get relationships
            relationships = await self.get_relationships(current_id)
            for rel in relationships:
                if rel.target_id not in visited:
                    queue.append((rel.target_id, depth + 1))
        
        return related
    
    async def find_shortest_path(self, source_id: str, target_id: str) -> Optional[List[str]]:
        """Find shortest path between two nodes"""
        # Simple BFS implementation
        queue = [(source_id, [source_id])]
        visited = set()
        
        while queue:
            current_id, path = queue.pop(0)
            
            if current_id == target_id:
                return path
            
            if current_id in visited:
                continue
            
            visited.add(current_id)
            
            # Get relationships
            relationships = await self.get_relationships(current_id)
            for rel in relationships:
                if rel.target_id not in visited:
                    new_path = path + [rel.target_id]
                    queue.append((rel.target_id, new_path))
        
        return None
    
    async def get_stats(self) -> KnowledgeGraphStats:
        """Get knowledge graph statistics"""
        return self.stats
    
    async def _load_stats(self):
        """Load statistics from the database"""
        try:
            # Query for node counts by type
            node_stats = await self.database.query(
                "MATCH (n) RETURN labels(n)[0] as type, count(n) as count"
            )
            
            self.stats.node_types = {stat['type']: stat['count'] for stat in node_stats}
            self.stats.total_nodes = sum(self.stats.node_types.values())
            
            # Query for relationship counts by type
            rel_stats = await self.database.query(
                "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
            )
            
            self.stats.relationship_types = {stat['type']: stat['count'] for stat in rel_stats}
            self.stats.total_relationships = sum(self.stats.relationship_types.values())
            
        except Exception as e:
            logger.warning(f"Failed to load stats: {e}")


class KnowledgeGraphBuilder:
    """Builds knowledge graphs from code analysis results"""
    
    def __init__(self, knowledge_graph: KnowledgeGraph):
        self.knowledge_graph = knowledge_graph
        self.semantic_analyzer = SemanticAnalyzer()
        self.relationship_extractor = RelationshipExtractor()
        self._processed_files: Set[str] = set()
    
    async def build_from_analysis(self, parse_results: List[ParseResult], 
                                 symbols: List[Symbol],
                                 dependency_graph: DependencyGraph) -> bool:
        """Build knowledge graph from analysis results"""
        try:
            logger.info(f"Building knowledge graph from {len(parse_results)} files")
            
            # Extract semantic concepts
            all_concepts = []
            for parse_result in parse_results:
                file_symbols = [s for s in symbols if s.file_path == parse_result.file_path]
                concepts, _ = await self.semantic_analyzer.analyze_semantics(parse_result, file_symbols)
                all_concepts.extend(concepts)
            
            # Extract relationships
            relationships = self.relationship_extractor.extract_relationships(
                parse_results, symbols, dependency_graph, all_concepts
            )
            
            # Add nodes to graph
            await self._add_symbol_nodes(symbols)
            await self._add_concept_nodes(all_concepts)
            await self._add_file_nodes(parse_results)
            
            # Add relationships to graph
            await self._add_relationships(relationships)
            
            # Mark files as processed
            for parse_result in parse_results:
                self._processed_files.add(parse_result.file_path)
            
            logger.info(f"Knowledge graph built successfully: {len(all_concepts)} concepts, {len(relationships)} relationships")
            return True
            
        except Exception as e:
            logger.error(f"Failed to build knowledge graph: {e}")
            return False
    
    async def update_from_file(self, parse_result: ParseResult, 
                              file_symbols: List[Symbol],
                              dependency_graph: DependencyGraph) -> bool:
        """Update knowledge graph from a single file"""
        try:
            logger.info(f"Updating knowledge graph from file: {parse_result.file_path}")
            
            # Remove existing nodes for this file
            await self._remove_file_nodes(parse_result.file_path)
            
            # Extract concepts for this file
            concepts, _ = await self.semantic_analyzer.analyze_semantics(parse_result, file_symbols)
            
            # Extract relationships involving this file
            relationships = self.relationship_extractor.extract_relationships(
                [parse_result], file_symbols, dependency_graph, concepts
            )
            
            # Add updated nodes
            await self._add_symbol_nodes(file_symbols)
            await self._add_concept_nodes(concepts)
            await self._add_file_nodes([parse_result])
            
            # Add updated relationships
            await self._add_relationships(relationships)
            
            self._processed_files.add(parse_result.file_path)
            
            logger.info(f"Knowledge graph updated for file: {parse_result.file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update knowledge graph for file {parse_result.file_path}: {e}")
            return False
    
    async def _add_symbol_nodes(self, symbols: List[Symbol]):
        """Add symbol nodes to the graph"""
        for symbol in symbols:
            node = GraphNode(
                id=f"symbol_{symbol.name}_{symbol.file_path}",
                label="Symbol",
                node_type="code_symbol",
                properties={
                    "name": symbol.name,
                    "symbol_type": symbol.symbol_type.value,
                    "file_path": symbol.file_path,
                    "line_number": symbol.line_number,
                    "signature": symbol.signature,
                    "metadata": symbol.metadata
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            await self.knowledge_graph.add_node(node)
    
    async def _add_concept_nodes(self, concepts: List[SemanticConcept]):
        """Add concept nodes to the graph"""
        for concept in concepts:
            node = GraphNode(
                id=concept.id,
                label="Concept",
                node_type="semantic_concept",
                properties={
                    "name": concept.name,
                    "concept_type": concept.concept_type.value,
                    "description": concept.description,
                    "confidence": concept.confidence,
                    "keywords": concept.keywords,
                    "source_elements": concept.source_elements,
                    "context": concept.context
                },
                created_at=concept.created_at,
                updated_at=datetime.now()
            )
            await self.knowledge_graph.add_node(node)
    
    async def _add_file_nodes(self, parse_results: List[ParseResult]):
        """Add file nodes to the graph"""
        for parse_result in parse_results:
            node = GraphNode(
                id=f"file_{parse_result.file_path}",
                label="File",
                node_type="source_file",
                properties={
                    "file_path": parse_result.file_path,
                    "language": parse_result.language.value,
                    "size": len(parse_result.content),
                    "lines": len(parse_result.content.split('\n')),
                    "functions_count": len(parse_result.functions),
                    "classes_count": len(parse_result.classes)
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            await self.knowledge_graph.add_node(node)
    
    async def _add_relationships(self, relationships: List[CodeRelationship]):
        """Add relationships to the graph"""
        for rel in relationships:
            graph_rel = GraphRelationship(
                id=rel.id,
                source_id=rel.source_id,
                target_id=rel.target_id,
                relationship_type=rel.relationship_type.value,
                properties={
                    "strength": rel.strength,
                    "confidence": rel.confidence,
                    "evidence": rel.evidence,
                    "metadata": rel.metadata
                },
                weight=rel.strength,
                created_at=rel.created_at
            )
            await self.knowledge_graph.add_relationship(graph_rel)
    
    async def _remove_file_nodes(self, file_path: str):
        """Remove nodes associated with a file"""
        try:
            # Query for nodes related to this file
            file_nodes = await self.knowledge_graph.query(
                "MATCH (n) WHERE n.file_path = $file_path RETURN n.id as id",
                {"file_path": file_path}
            )
            
            # Delete nodes
            for node_data in file_nodes:
                await self.knowledge_graph.database.delete_node(node_data['id'])
            
        except Exception as e:
            logger.warning(f"Failed to remove nodes for file {file_path}: {e}")
    
    def get_processed_files(self) -> Set[str]:
        """Get set of processed files"""
        return self._processed_files.copy()
    
    def is_file_processed(self, file_path: str) -> bool:
        """Check if a file has been processed"""
        return file_path in self._processed_files


def create_knowledge_graph() -> Optional[KnowledgeGraph]:
    """Factory function to create a knowledge graph instance"""
    try:
        # Get configuration
        kg_config = config.get_section("knowledge_graph", {})
        db_type = kg_config.get("database_type", "memory")
        
        # Create database instance
        if db_type == "neo4j":
            from ccw.knowledge.graph_database import Neo4jDatabase
            
            database = Neo4jDatabase(
                uri=kg_config.get("neo4j_uri", "bolt://localhost:7687"),
                username=kg_config.get("neo4j_username", "neo4j"),
                password=kg_config.get("neo4j_password", "password"),
                database=kg_config.get("neo4j_database", "neo4j")
            )
        else:
            from ccw.knowledge.graph_database import MemoryGraphDatabase
            database = MemoryGraphDatabase()
        
        # Create knowledge graph
        knowledge_graph = KnowledgeGraph(database)
        
        logger.info(f"Created knowledge graph with {db_type} database")
        return knowledge_graph
        
    except Exception as e:
        logger.error(f"Failed to create knowledge graph: {e}")
        return None
