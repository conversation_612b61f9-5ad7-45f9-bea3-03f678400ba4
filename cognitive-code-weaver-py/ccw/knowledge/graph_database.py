"""
Graph Database Interface and Implementations

Provides abstraction layer for graph databases with support for Neo4j
and in-memory graph storage for development and testing.
"""

import logging
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import networkx as nx

logger = logging.getLogger(__name__)


@dataclass
class GraphNode:
    """Represents a node in the knowledge graph"""
    id: str
    label: str
    node_type: str
    properties: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data["created_at"] = self.created_at.isoformat()
        data["updated_at"] = self.updated_at.isoformat()
        return data


@dataclass
class GraphRelationship:
    """Represents a relationship between nodes"""
    id: str
    source_id: str
    target_id: str
    relationship_type: str
    properties: Dict[str, Any]
    weight: float
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data["created_at"] = self.created_at.isoformat()
        return data


class GraphDatabase(ABC):
    """Abstract base class for graph database implementations"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the graph database"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from the graph database"""
        pass
    
    @abstractmethod
    async def create_node(self, node: GraphNode) -> bool:
        """Create a new node"""
        pass
    
    @abstractmethod
    async def create_relationship(self, relationship: GraphRelationship) -> bool:
        """Create a new relationship"""
        pass
    
    @abstractmethod
    async def get_node(self, node_id: str) -> Optional[GraphNode]:
        """Get a node by ID"""
        pass
    
    @abstractmethod
    async def get_nodes_by_type(self, node_type: str) -> List[GraphNode]:
        """Get all nodes of a specific type"""
        pass
    
    @abstractmethod
    async def get_relationships(self, source_id: str, target_id: Optional[str] = None) -> List[GraphRelationship]:
        """Get relationships for a node"""
        pass
    
    @abstractmethod
    async def query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a graph query"""
        pass
    
    @abstractmethod
    async def delete_node(self, node_id: str) -> bool:
        """Delete a node and its relationships"""
        pass
    
    @abstractmethod
    async def update_node(self, node_id: str, properties: Dict[str, Any]) -> bool:
        """Update node properties"""
        pass


class Neo4jDatabase(GraphDatabase):
    """Neo4j graph database implementation"""
    
    def __init__(self, uri: str, username: str, password: str, database: str = "neo4j"):
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.driver = None
        self.connected = False
    
    async def connect(self) -> bool:
        """Connect to Neo4j database"""
        try:
            from neo4j import GraphDatabase as Neo4jDriver
            
            self.driver = Neo4jDriver.driver(
                self.uri,
                auth=(self.username, self.password)
            )
            
            # Test connection
            with self.driver.session(database=self.database) as session:
                result = session.run("RETURN 1 as test")
                result.single()
            
            self.connected = True
            logger.info(f"Connected to Neo4j database: {self.uri}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Neo4j"""
        if self.driver:
            self.driver.close()
            self.connected = False
            logger.info("Disconnected from Neo4j database")
    
    async def create_node(self, node: GraphNode) -> bool:
        """Create a node in Neo4j"""
        if not self.connected:
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = f"""
                CREATE (n:{node.label} {{
                    id: $id,
                    node_type: $node_type,
                    properties: $properties,
                    created_at: $created_at,
                    updated_at: $updated_at
                }})
                RETURN n
                """
                
                result = session.run(query, {
                    "id": node.id,
                    "node_type": node.node_type,
                    "properties": json.dumps(node.properties),
                    "created_at": node.created_at.isoformat(),
                    "updated_at": node.updated_at.isoformat()
                })
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Failed to create node {node.id}: {e}")
            return False
    
    async def create_relationship(self, relationship: GraphRelationship) -> bool:
        """Create a relationship in Neo4j"""
        if not self.connected:
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = f"""
                MATCH (a {{id: $source_id}}), (b {{id: $target_id}})
                CREATE (a)-[r:{relationship.relationship_type} {{
                    id: $id,
                    properties: $properties,
                    weight: $weight,
                    created_at: $created_at
                }}]->(b)
                RETURN r
                """
                
                result = session.run(query, {
                    "source_id": relationship.source_id,
                    "target_id": relationship.target_id,
                    "id": relationship.id,
                    "properties": json.dumps(relationship.properties),
                    "weight": relationship.weight,
                    "created_at": relationship.created_at.isoformat()
                })
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Failed to create relationship {relationship.id}: {e}")
            return False
    
    async def get_node(self, node_id: str) -> Optional[GraphNode]:
        """Get a node by ID from Neo4j"""
        if not self.connected:
            return None
        
        try:
            with self.driver.session(database=self.database) as session:
                query = "MATCH (n {id: $node_id}) RETURN n"
                result = session.run(query, {"node_id": node_id})
                record = result.single()
                
                if record:
                    node_data = record["n"]
                    return GraphNode(
                        id=node_data["id"],
                        label=list(node_data.labels)[0],
                        node_type=node_data["node_type"],
                        properties=json.loads(node_data["properties"]),
                        created_at=datetime.fromisoformat(node_data["created_at"]),
                        updated_at=datetime.fromisoformat(node_data["updated_at"])
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"Failed to get node {node_id}: {e}")
            return None
    
    async def get_nodes_by_type(self, node_type: str) -> List[GraphNode]:
        """Get all nodes of a specific type"""
        if not self.connected:
            return []
        
        try:
            with self.driver.session(database=self.database) as session:
                query = "MATCH (n {node_type: $node_type}) RETURN n"
                result = session.run(query, {"node_type": node_type})
                
                nodes = []
                for record in result:
                    node_data = record["n"]
                    node = GraphNode(
                        id=node_data["id"],
                        label=list(node_data.labels)[0],
                        node_type=node_data["node_type"],
                        properties=json.loads(node_data["properties"]),
                        created_at=datetime.fromisoformat(node_data["created_at"]),
                        updated_at=datetime.fromisoformat(node_data["updated_at"])
                    )
                    nodes.append(node)
                
                return nodes
                
        except Exception as e:
            logger.error(f"Failed to get nodes by type {node_type}: {e}")
            return []
    
    async def get_relationships(self, source_id: str, target_id: Optional[str] = None) -> List[GraphRelationship]:
        """Get relationships for a node"""
        if not self.connected:
            return []
        
        try:
            with self.driver.session(database=self.database) as session:
                if target_id:
                    query = """
                    MATCH (a {id: $source_id})-[r]->(b {id: $target_id})
                    RETURN r, type(r) as rel_type
                    """
                    params = {"source_id": source_id, "target_id": target_id}
                else:
                    query = """
                    MATCH (a {id: $source_id})-[r]->()
                    RETURN r, type(r) as rel_type
                    """
                    params = {"source_id": source_id}
                
                result = session.run(query, params)
                
                relationships = []
                for record in result:
                    rel_data = record["r"]
                    relationship = GraphRelationship(
                        id=rel_data["id"],
                        source_id=source_id,
                        target_id=rel_data.end_node["id"],
                        relationship_type=record["rel_type"],
                        properties=json.loads(rel_data["properties"]),
                        weight=rel_data["weight"],
                        created_at=datetime.fromisoformat(rel_data["created_at"])
                    )
                    relationships.append(relationship)
                
                return relationships
                
        except Exception as e:
            logger.error(f"Failed to get relationships for {source_id}: {e}")
            return []
    
    async def query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a Cypher query"""
        if not self.connected:
            return []
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(query, parameters or {})
                return [record.data() for record in result]
                
        except Exception as e:
            logger.error(f"Failed to execute query: {e}")
            return []
    
    async def delete_node(self, node_id: str) -> bool:
        """Delete a node and its relationships"""
        if not self.connected:
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = "MATCH (n {id: $node_id}) DETACH DELETE n"
                result = session.run(query, {"node_id": node_id})
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete node {node_id}: {e}")
            return False
    
    async def update_node(self, node_id: str, properties: Dict[str, Any]) -> bool:
        """Update node properties"""
        if not self.connected:
            return False
        
        try:
            with self.driver.session(database=self.database) as session:
                query = """
                MATCH (n {id: $node_id})
                SET n.properties = $properties, n.updated_at = $updated_at
                RETURN n
                """
                
                result = session.run(query, {
                    "node_id": node_id,
                    "properties": json.dumps(properties),
                    "updated_at": datetime.now().isoformat()
                })
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Failed to update node {node_id}: {e}")
            return False


class MemoryGraphDatabase(GraphDatabase):
    """In-memory graph database implementation using NetworkX"""
    
    def __init__(self):
        self.graph = nx.MultiDiGraph()
        self.nodes: Dict[str, GraphNode] = {}
        self.relationships: Dict[str, GraphRelationship] = {}
        self.connected = False
    
    async def connect(self) -> bool:
        """Connect to in-memory database"""
        self.connected = True
        logger.info("Connected to in-memory graph database")
        return True
    
    async def disconnect(self):
        """Disconnect from in-memory database"""
        self.connected = False
        self.graph.clear()
        self.nodes.clear()
        self.relationships.clear()
        logger.info("Disconnected from in-memory graph database")
    
    async def create_node(self, node: GraphNode) -> bool:
        """Create a node in memory"""
        if not self.connected:
            return False
        
        try:
            self.nodes[node.id] = node
            self.graph.add_node(node.id, **node.to_dict())
            return True
            
        except Exception as e:
            logger.error(f"Failed to create node {node.id}: {e}")
            return False
    
    async def create_relationship(self, relationship: GraphRelationship) -> bool:
        """Create a relationship in memory"""
        if not self.connected:
            return False
        
        try:
            self.relationships[relationship.id] = relationship
            self.graph.add_edge(
                relationship.source_id,
                relationship.target_id,
                key=relationship.id,
                **relationship.to_dict()
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to create relationship {relationship.id}: {e}")
            return False
    
    async def get_node(self, node_id: str) -> Optional[GraphNode]:
        """Get a node by ID"""
        return self.nodes.get(node_id)
    
    async def get_nodes_by_type(self, node_type: str) -> List[GraphNode]:
        """Get all nodes of a specific type"""
        return [node for node in self.nodes.values() if node.node_type == node_type]
    
    async def get_relationships(self, source_id: str, target_id: Optional[str] = None) -> List[GraphRelationship]:
        """Get relationships for a node"""
        relationships = []
        
        if target_id:
            # Get specific relationship
            if self.graph.has_edge(source_id, target_id):
                edge_data = self.graph.get_edge_data(source_id, target_id)
                for key, data in edge_data.items():
                    if key in self.relationships:
                        relationships.append(self.relationships[key])
        else:
            # Get all outgoing relationships
            for target in self.graph.successors(source_id):
                edge_data = self.graph.get_edge_data(source_id, target)
                for key, data in edge_data.items():
                    if key in self.relationships:
                        relationships.append(self.relationships[key])
        
        return relationships
    
    async def query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Execute a simple query (limited functionality for in-memory)"""
        # This is a simplified query implementation
        # In a real scenario, you'd implement a query parser
        results = []
        
        if "MATCH" in query and "RETURN" in query:
            # Simple node matching
            if "node_type:" in query:
                node_type = query.split("node_type:")[1].split("}")[0].strip().strip('"\'')
                nodes = await self.get_nodes_by_type(node_type)
                results = [node.to_dict() for node in nodes]
        
        return results
    
    async def delete_node(self, node_id: str) -> bool:
        """Delete a node and its relationships"""
        if node_id in self.nodes:
            # Remove relationships
            relationships_to_remove = []
            for rel_id, rel in self.relationships.items():
                if rel.source_id == node_id or rel.target_id == node_id:
                    relationships_to_remove.append(rel_id)
            
            for rel_id in relationships_to_remove:
                del self.relationships[rel_id]
            
            # Remove node
            del self.nodes[node_id]
            self.graph.remove_node(node_id)
            return True
        
        return False
    
    async def update_node(self, node_id: str, properties: Dict[str, Any]) -> bool:
        """Update node properties"""
        if node_id in self.nodes:
            node = self.nodes[node_id]
            node.properties.update(properties)
            node.updated_at = datetime.now()
            
            # Update graph
            self.graph.nodes[node_id].update(node.to_dict())
            return True
        
        return False
    
    def get_networkx_graph(self) -> nx.MultiDiGraph:
        """Get the underlying NetworkX graph"""
        return self.graph
