"""
Concept Mapper for Knowledge Graph

Maps and organizes semantic concepts, creating hierarchical relationships
and concept taxonomies for better knowledge organization.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ccw.knowledge.semantic_analyzer import <PERSON>man<PERSON><PERSON>oncept, ConceptType
from ccw.llm.client import llm_client

logger = logging.getLogger(__name__)


class ConceptHierarchyLevel(Enum):
    """Levels in the concept hierarchy"""
    DOMAIN = "domain"
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    INSTANCE = "instance"


@dataclass
class CodeConcept:
    """Enhanced concept with mapping and hierarchy information"""
    base_concept: SemanticConcept
    hierarchy_level: ConceptHierarchyLevel
    parent_concepts: List[str] = field(default_factory=list)
    child_concepts: List[str] = field(default_factory=list)
    related_concepts: List[str] = field(default_factory=list)
    taxonomy_path: List[str] = field(default_factory=list)
    abstraction_level: float = 0.5  # 0.0 = very concrete, 1.0 = very abstract
    domain_relevance: float = 0.5   # 0.0 = technical, 1.0 = business domain
    usage_frequency: int = 0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class ConceptTaxonomy:
    """Represents a concept taxonomy tree"""
    root_concept: str
    levels: Dict[ConceptHierarchyLevel, List[str]] = field(default_factory=dict)
    relationships: Dict[str, List[str]] = field(default_factory=dict)  # parent -> children
    metadata: Dict[str, Any] = field(default_factory=dict)


class ConceptMapper:
    """Maps and organizes semantic concepts into hierarchical structures"""
    
    def __init__(self):
        self.concept_hierarchies = self._load_concept_hierarchies()
        self.domain_taxonomies = self._load_domain_taxonomies()
        self.concept_cache: Dict[str, CodeConcept] = {}
    
    def map_concepts(self, concepts: List[SemanticConcept]) -> List[CodeConcept]:
        """Map semantic concepts to enhanced code concepts"""
        mapped_concepts = []
        
        for concept in concepts:
            mapped_concept = self._map_single_concept(concept)
            mapped_concepts.append(mapped_concept)
            self.concept_cache[concept.id] = mapped_concept
        
        # Build relationships between mapped concepts
        self._build_concept_relationships(mapped_concepts)
        
        return mapped_concepts
    
    def create_taxonomy(self, concepts: List[CodeConcept], domain: str) -> ConceptTaxonomy:
        """Create a concept taxonomy for a specific domain"""
        taxonomy = ConceptTaxonomy(root_concept=f"domain_{domain}")
        
        # Organize concepts by hierarchy level
        for concept in concepts:
            level = concept.hierarchy_level
            if level not in taxonomy.levels:
                taxonomy.levels[level] = []
            taxonomy.levels[level].append(concept.base_concept.id)
        
        # Build parent-child relationships
        for concept in concepts:
            if concept.parent_concepts:
                for parent_id in concept.parent_concepts:
                    if parent_id not in taxonomy.relationships:
                        taxonomy.relationships[parent_id] = []
                    taxonomy.relationships[parent_id].append(concept.base_concept.id)
        
        # Add metadata
        taxonomy.metadata = {
            "domain": domain,
            "total_concepts": len(concepts),
            "created_at": datetime.now().isoformat(),
            "abstraction_levels": self._calculate_abstraction_distribution(concepts),
            "concept_types": self._calculate_type_distribution(concepts)
        }
        
        return taxonomy
    
    def find_concept_clusters(self, concepts: List[CodeConcept], similarity_threshold: float = 0.7) -> List[List[str]]:
        """Find clusters of similar concepts"""
        clusters = []
        processed = set()
        
        for concept in concepts:
            if concept.base_concept.id in processed:
                continue
            
            cluster = [concept.base_concept.id]
            processed.add(concept.base_concept.id)
            
            # Find similar concepts
            for other_concept in concepts:
                if (other_concept.base_concept.id not in processed and
                    self._calculate_concept_similarity(concept, other_concept) >= similarity_threshold):
                    cluster.append(other_concept.base_concept.id)
                    processed.add(other_concept.base_concept.id)
            
            if len(cluster) > 1:
                clusters.append(cluster)
        
        return clusters
    
    def suggest_concept_hierarchy(self, concepts: List[CodeConcept]) -> Dict[str, List[str]]:
        """Suggest hierarchical organization of concepts"""
        hierarchy = {}
        
        # Group concepts by abstraction level
        abstraction_groups = {}
        for concept in concepts:
            level = round(concept.abstraction_level, 1)
            if level not in abstraction_groups:
                abstraction_groups[level] = []
            abstraction_groups[level].append(concept)
        
        # Create hierarchy from most abstract to most concrete
        sorted_levels = sorted(abstraction_groups.keys(), reverse=True)
        
        for i, level in enumerate(sorted_levels):
            level_concepts = abstraction_groups[level]
            
            if i == 0:  # Most abstract level - these are root concepts
                hierarchy["root"] = [c.base_concept.id for c in level_concepts]
            else:
                # Find parent concepts from higher abstraction levels
                for concept in level_concepts:
                    parents = self._find_potential_parents(concept, concepts, level)
                    if parents:
                        parent_id = parents[0].base_concept.id
                        if parent_id not in hierarchy:
                            hierarchy[parent_id] = []
                        hierarchy[parent_id].append(concept.base_concept.id)
        
        return hierarchy
    
    async def enhance_with_llm_mapping(self, concepts: List[CodeConcept]) -> List[CodeConcept]:
        """Enhance concept mapping using LLM analysis"""
        try:
            # Create prompt for LLM concept mapping
            prompt = self._create_concept_mapping_prompt(concepts)
            
            # Get LLM response
            response = await llm_client.generate_response([
                {"role": "system", "content": "You are an expert in knowledge organization and concept mapping."},
                {"role": "user", "content": prompt}
            ])
            
            # Parse and apply LLM suggestions
            enhanced_concepts = self._apply_llm_mapping_suggestions(response.content, concepts)
            
            return enhanced_concepts
            
        except Exception as e:
            logger.warning(f"LLM concept mapping enhancement failed: {e}")
            return concepts
    
    def _map_single_concept(self, concept: SemanticConcept) -> CodeConcept:
        """Map a single semantic concept to a code concept"""
        # Determine hierarchy level
        hierarchy_level = self._determine_hierarchy_level(concept)
        
        # Calculate abstraction level
        abstraction_level = self._calculate_abstraction_level(concept)
        
        # Calculate domain relevance
        domain_relevance = self._calculate_domain_relevance(concept)
        
        # Create taxonomy path
        taxonomy_path = self._create_taxonomy_path(concept)
        
        return CodeConcept(
            base_concept=concept,
            hierarchy_level=hierarchy_level,
            taxonomy_path=taxonomy_path,
            abstraction_level=abstraction_level,
            domain_relevance=domain_relevance,
            usage_frequency=len(concept.source_elements)
        )
    
    def _determine_hierarchy_level(self, concept: SemanticConcept) -> ConceptHierarchyLevel:
        """Determine the hierarchy level of a concept"""
        concept_type = concept.concept_type
        
        # Map concept types to hierarchy levels
        type_to_level = {
            ConceptType.DOMAIN_CONCEPT: ConceptHierarchyLevel.DOMAIN,
            ConceptType.BUSINESS_LOGIC: ConceptHierarchyLevel.CATEGORY,
            ConceptType.TECHNICAL_CONCEPT: ConceptHierarchyLevel.CATEGORY,
            ConceptType.PATTERN: ConceptHierarchyLevel.SUBCATEGORY,
            ConceptType.ALGORITHM: ConceptHierarchyLevel.SUBCATEGORY,
            ConceptType.DATA_STRUCTURE: ConceptHierarchyLevel.INSTANCE,
            ConceptType.RESPONSIBILITY: ConceptHierarchyLevel.INSTANCE,
            ConceptType.ABSTRACTION: ConceptHierarchyLevel.DOMAIN
        }
        
        return type_to_level.get(concept_type, ConceptHierarchyLevel.INSTANCE)
    
    def _calculate_abstraction_level(self, concept: SemanticConcept) -> float:
        """Calculate the abstraction level of a concept (0.0 = concrete, 1.0 = abstract)"""
        # Base abstraction on concept type
        type_abstraction = {
            ConceptType.ABSTRACTION: 0.9,
            ConceptType.DOMAIN_CONCEPT: 0.8,
            ConceptType.BUSINESS_LOGIC: 0.7,
            ConceptType.PATTERN: 0.6,
            ConceptType.TECHNICAL_CONCEPT: 0.5,
            ConceptType.ALGORITHM: 0.4,
            ConceptType.RESPONSIBILITY: 0.3,
            ConceptType.DATA_STRUCTURE: 0.2
        }
        
        base_level = type_abstraction.get(concept.concept_type, 0.5)
        
        # Adjust based on keywords
        abstract_keywords = {'abstract', 'interface', 'base', 'generic', 'template', 'pattern'}
        concrete_keywords = {'implementation', 'concrete', 'specific', 'instance', 'data'}
        
        concept_words = set(word.lower() for word in concept.keywords)
        
        if concept_words & abstract_keywords:
            base_level += 0.1
        if concept_words & concrete_keywords:
            base_level -= 0.1
        
        return max(0.0, min(1.0, base_level))
    
    def _calculate_domain_relevance(self, concept: SemanticConcept) -> float:
        """Calculate domain relevance (0.0 = technical, 1.0 = business domain)"""
        business_keywords = {
            'user', 'customer', 'order', 'payment', 'account', 'business',
            'process', 'workflow', 'rule', 'policy', 'requirement'
        }
        
        technical_keywords = {
            'database', 'api', 'server', 'client', 'cache', 'queue',
            'connection', 'configuration', 'logger', 'monitor'
        }
        
        concept_words = set(word.lower() for word in concept.keywords)
        
        business_score = len(concept_words & business_keywords)
        technical_score = len(concept_words & technical_keywords)
        
        if business_score + technical_score == 0:
            return 0.5  # Neutral
        
        return business_score / (business_score + technical_score)
    
    def _create_taxonomy_path(self, concept: SemanticConcept) -> List[str]:
        """Create a taxonomy path for the concept"""
        path = []
        
        # Add concept type as first level
        path.append(concept.concept_type.value)
        
        # Add domain-specific path elements based on keywords
        if concept.keywords:
            # Use the most significant keyword as the next level
            primary_keyword = max(concept.keywords, key=len) if concept.keywords else ""
            if primary_keyword:
                path.append(primary_keyword.lower())
        
        # Add concept name as the final level
        path.append(concept.name.lower().replace(' ', '_'))
        
        return path
    
    def _build_concept_relationships(self, concepts: List[CodeConcept]):
        """Build relationships between concepts"""
        for i, concept_a in enumerate(concepts):
            for concept_b in concepts[i+1:]:
                relationship_type = self._analyze_concept_relationship(concept_a, concept_b)
                
                if relationship_type == "parent_child":
                    # Determine which is parent based on abstraction level
                    if concept_a.abstraction_level > concept_b.abstraction_level:
                        concept_a.child_concepts.append(concept_b.base_concept.id)
                        concept_b.parent_concepts.append(concept_a.base_concept.id)
                    else:
                        concept_b.child_concepts.append(concept_a.base_concept.id)
                        concept_a.parent_concepts.append(concept_b.base_concept.id)
                
                elif relationship_type == "related":
                    concept_a.related_concepts.append(concept_b.base_concept.id)
                    concept_b.related_concepts.append(concept_a.base_concept.id)
    
    def _analyze_concept_relationship(self, concept_a: CodeConcept, concept_b: CodeConcept) -> Optional[str]:
        """Analyze the relationship between two concepts"""
        # Check for hierarchical relationship
        abstraction_diff = abs(concept_a.abstraction_level - concept_b.abstraction_level)
        
        if abstraction_diff > 0.3:  # Significant abstraction difference
            # Check for keyword overlap indicating hierarchy
            keywords_a = set(word.lower() for word in concept_a.base_concept.keywords)
            keywords_b = set(word.lower() for word in concept_b.base_concept.keywords)
            
            overlap = keywords_a & keywords_b
            if len(overlap) > 0:
                return "parent_child"
        
        # Check for related concepts
        similarity = self._calculate_concept_similarity(concept_a, concept_b)
        if similarity > 0.5:
            return "related"
        
        return None
    
    def _calculate_concept_similarity(self, concept_a: CodeConcept, concept_b: CodeConcept) -> float:
        """Calculate similarity between two concepts"""
        # Keyword similarity
        keywords_a = set(word.lower() for word in concept_a.base_concept.keywords)
        keywords_b = set(word.lower() for word in concept_b.base_concept.keywords)
        
        if not keywords_a or not keywords_b:
            return 0.0
        
        intersection = keywords_a & keywords_b
        union = keywords_a | keywords_b
        
        keyword_similarity = len(intersection) / len(union) if union else 0.0
        
        # Type similarity
        type_similarity = 1.0 if concept_a.base_concept.concept_type == concept_b.base_concept.concept_type else 0.0
        
        # Domain relevance similarity
        domain_similarity = 1.0 - abs(concept_a.domain_relevance - concept_b.domain_relevance)
        
        # Weighted average
        return (keyword_similarity * 0.5 + type_similarity * 0.3 + domain_similarity * 0.2)
    
    def _find_potential_parents(self, concept: CodeConcept, all_concepts: List[CodeConcept], 
                               current_level: float) -> List[CodeConcept]:
        """Find potential parent concepts for a given concept"""
        potential_parents = []
        
        for other_concept in all_concepts:
            if (other_concept.abstraction_level > current_level and
                self._calculate_concept_similarity(concept, other_concept) > 0.3):
                potential_parents.append(other_concept)
        
        # Sort by similarity
        potential_parents.sort(
            key=lambda c: self._calculate_concept_similarity(concept, c),
            reverse=True
        )
        
        return potential_parents
    
    def _calculate_abstraction_distribution(self, concepts: List[CodeConcept]) -> Dict[str, int]:
        """Calculate distribution of concepts across abstraction levels"""
        distribution = {"high": 0, "medium": 0, "low": 0}
        
        for concept in concepts:
            if concept.abstraction_level > 0.7:
                distribution["high"] += 1
            elif concept.abstraction_level > 0.3:
                distribution["medium"] += 1
            else:
                distribution["low"] += 1
        
        return distribution
    
    def _calculate_type_distribution(self, concepts: List[CodeConcept]) -> Dict[str, int]:
        """Calculate distribution of concept types"""
        distribution = {}
        
        for concept in concepts:
            concept_type = concept.base_concept.concept_type.value
            distribution[concept_type] = distribution.get(concept_type, 0) + 1
        
        return distribution
    
    def _create_concept_mapping_prompt(self, concepts: List[CodeConcept]) -> str:
        """Create prompt for LLM concept mapping"""
        concept_info = []
        for concept in concepts[:10]:  # Limit to first 10 for prompt size
            concept_info.append({
                "name": concept.base_concept.name,
                "type": concept.base_concept.concept_type.value,
                "keywords": concept.base_concept.keywords,
                "abstraction_level": concept.abstraction_level
            })
        
        return f"""
        Analyze the following software concepts and suggest improved hierarchical organization:
        
        Concepts: {concept_info}
        
        Please provide:
        1. Suggested parent-child relationships
        2. Concept clusters or groups
        3. Improved abstraction levels (0.0 = concrete, 1.0 = abstract)
        4. Domain categorization suggestions
        
        Respond in JSON format with 'relationships', 'clusters', 'abstraction_adjustments', and 'domain_categories'.
        """
    
    def _apply_llm_mapping_suggestions(self, response: str, concepts: List[CodeConcept]) -> List[CodeConcept]:
        """Apply LLM mapping suggestions to concepts"""
        try:
            import json
            suggestions = json.loads(response)
            
            # Apply relationship suggestions
            relationships = suggestions.get('relationships', [])
            for rel in relationships:
                parent_id = rel.get('parent')
                child_id = rel.get('child')
                
                parent_concept = next((c for c in concepts if c.base_concept.id == parent_id), None)
                child_concept = next((c for c in concepts if c.base_concept.id == child_id), None)
                
                if parent_concept and child_concept:
                    if child_id not in parent_concept.child_concepts:
                        parent_concept.child_concepts.append(child_id)
                    if parent_id not in child_concept.parent_concepts:
                        child_concept.parent_concepts.append(parent_id)
            
            # Apply abstraction level adjustments
            adjustments = suggestions.get('abstraction_adjustments', {})
            for concept_id, new_level in adjustments.items():
                concept = next((c for c in concepts if c.base_concept.id == concept_id), None)
                if concept and 0.0 <= new_level <= 1.0:
                    concept.abstraction_level = new_level
            
            return concepts
            
        except Exception as e:
            logger.error(f"Failed to apply LLM mapping suggestions: {e}")
            return concepts
    
    def _load_concept_hierarchies(self) -> Dict[str, Dict[str, List[str]]]:
        """Load predefined concept hierarchies"""
        return {
            "software_architecture": {
                "presentation": ["ui", "view", "controller", "endpoint"],
                "business": ["service", "logic", "rule", "process"],
                "data": ["repository", "model", "entity", "database"],
                "infrastructure": ["config", "util", "client", "adapter"]
            },
            "design_patterns": {
                "creational": ["factory", "builder", "singleton", "prototype"],
                "structural": ["adapter", "decorator", "facade", "proxy"],
                "behavioral": ["observer", "strategy", "command", "state"]
            }
        }
    
    def _load_domain_taxonomies(self) -> Dict[str, List[str]]:
        """Load domain-specific taxonomies"""
        return {
            "business": ["customer", "order", "payment", "account", "product"],
            "technical": ["database", "api", "server", "client", "cache"],
            "security": ["authentication", "authorization", "encryption", "validation"],
            "data": ["model", "entity", "repository", "query", "transaction"]
        }
