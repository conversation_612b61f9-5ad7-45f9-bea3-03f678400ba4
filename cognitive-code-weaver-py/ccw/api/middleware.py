"""
API Middleware for Cognitive Code Weaver

Provides middleware for CORS, authentication, rate limiting,
request logging, and error handling.
"""

import time
import logging
import json
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta

from fastapi import <PERSON><PERSON><PERSON>, Request, Response, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.gzip import GZipMiddleware

from ccw.core.config import config

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging API requests and responses"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Start timing
        start_time = time.time()
        
        # Log request
        logger.info(
            f"API Request: {request.method} {request.url.path} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            logger.info(
                f"API Response: {response.status_code} "
                f"in {duration:.3f}s for {request.method} {request.url.path}"
            )
            
            # Add timing header
            response.headers["X-Process-Time"] = str(duration)
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"API Error: {str(e)} in {duration:.3f}s "
                f"for {request.method} {request.url.path}"
            )
            raise


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware"""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts: Dict[str, Dict[str, Any]] = {}
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/api/v1/health"]:
            return await call_next(request)
        
        # Clean up old entries periodically
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries()
            self.last_cleanup = current_time
        
        # Check rate limit
        if self._is_rate_limited(client_ip, current_time):
            logger.warning(f"Rate limit exceeded for {client_ip}")
            return JSONResponse(
                status_code=429,
                content={
                    "status": "error",
                    "message": "Rate limit exceeded",
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )
        
        # Record request
        self._record_request(client_ip, current_time)
        
        return await call_next(request)
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """Check if client is rate limited"""
        if client_ip not in self.request_counts:
            return False
        
        client_data = self.request_counts[client_ip]
        window_start = current_time - 60  # 1 minute window
        
        # Count requests in current window
        recent_requests = [
            req_time for req_time in client_data["requests"]
            if req_time > window_start
        ]
        
        return len(recent_requests) >= self.requests_per_minute
    
    def _record_request(self, client_ip: str, current_time: float):
        """Record a request for rate limiting"""
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = {"requests": []}
        
        self.request_counts[client_ip]["requests"].append(current_time)
        
        # Keep only recent requests (last hour)
        window_start = current_time - 3600
        self.request_counts[client_ip]["requests"] = [
            req_time for req_time in self.request_counts[client_ip]["requests"]
            if req_time > window_start
        ]
    
    def _cleanup_old_entries(self):
        """Clean up old rate limiting entries"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 hour
        
        clients_to_remove = []
        for client_ip, client_data in self.request_counts.items():
            # Remove old requests
            client_data["requests"] = [
                req_time for req_time in client_data["requests"]
                if req_time > cutoff_time
            ]
            
            # Remove clients with no recent requests
            if not client_data["requests"]:
                clients_to_remove.append(client_ip)
        
        for client_ip in clients_to_remove:
            del self.request_counts[client_ip]


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling and formatting errors"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except HTTPException:
            # Let FastAPI handle HTTP exceptions
            raise
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unhandled error in API: {str(e)}", exc_info=True)
            
            # Return formatted error response
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": "Internal server error",
                    "error_code": "INTERNAL_ERROR",
                    "timestamp": datetime.now().isoformat(),
                    "request_id": getattr(request.state, "request_id", None)
                }
            )


class RequestIDMiddleware(BaseHTTPMiddleware):
    """Middleware for adding request IDs"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        import uuid
        request_id = str(uuid.uuid4())
        
        # Store in request state
        request.state.request_id = request_id
        
        # Process request
        response = await call_next(request)
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add CSP header for API endpoints
        if request.url.path.startswith("/api/"):
            response.headers["Content-Security-Policy"] = "default-src 'none'"
        
        return response


def setup_middleware(app: FastAPI):
    """Setup all middleware for the FastAPI app"""
    
    # Get configuration
    api_config = config.get_section("api", {})
    cors_config = api_config.get("cors", {})
    rate_limit_config = api_config.get("rate_limiting", {})
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_config.get("allowed_origins", ["*"]),
        allow_credentials=cors_config.get("allow_credentials", True),
        allow_methods=cors_config.get("allowed_methods", ["*"]),
        allow_headers=cors_config.get("allowed_headers", ["*"]),
        expose_headers=["X-Request-ID", "X-Process-Time"]
    )
    
    # Trusted host middleware (if configured)
    allowed_hosts = api_config.get("allowed_hosts")
    if allowed_hosts:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=allowed_hosts
        )
    
    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom middleware (order matters - last added is executed first)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RequestIDMiddleware)
    app.add_middleware(RequestLoggingMiddleware)
    
    # Rate limiting middleware (if enabled)
    if rate_limit_config.get("enabled", True):
        requests_per_minute = rate_limit_config.get("requests_per_minute", 60)
        app.add_middleware(RateLimitingMiddleware, requests_per_minute=requests_per_minute)
    
    logger.info("API middleware setup completed")


class MetricsCollector:
    """Collect API metrics for monitoring"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        self.endpoint_stats: Dict[str, Dict[str, Any]] = {}
        self.start_time = time.time()
    
    def record_request(self, method: str, path: str, status_code: int, response_time: float):
        """Record API request metrics"""
        self.request_count += 1
        self.total_response_time += response_time
        
        if status_code >= 400:
            self.error_count += 1
        
        # Record endpoint-specific stats
        endpoint_key = f"{method} {path}"
        if endpoint_key not in self.endpoint_stats:
            self.endpoint_stats[endpoint_key] = {
                "count": 0,
                "error_count": 0,
                "total_time": 0.0,
                "min_time": float('inf'),
                "max_time": 0.0
            }
        
        stats = self.endpoint_stats[endpoint_key]
        stats["count"] += 1
        stats["total_time"] += response_time
        stats["min_time"] = min(stats["min_time"], response_time)
        stats["max_time"] = max(stats["max_time"], response_time)
        
        if status_code >= 400:
            stats["error_count"] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics"""
        uptime = time.time() - self.start_time
        
        return {
            "uptime": uptime,
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "average_response_time": self.total_response_time / max(self.request_count, 1),
            "requests_per_second": self.request_count / max(uptime, 1),
            "endpoint_stats": {
                endpoint: {
                    **stats,
                    "average_time": stats["total_time"] / max(stats["count"], 1),
                    "error_rate": stats["error_count"] / max(stats["count"], 1)
                }
                for endpoint, stats in self.endpoint_stats.items()
            }
        }
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        self.endpoint_stats.clear()
        self.start_time = time.time()


# Global metrics collector
metrics_collector = MetricsCollector()


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting API metrics"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            response_time = time.time() - start_time
            
            # Record metrics
            metrics_collector.record_request(
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                response_time=response_time
            )
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            
            # Record error metrics
            metrics_collector.record_request(
                method=request.method,
                path=request.url.path,
                status_code=500,
                response_time=response_time
            )
            
            raise


def add_metrics_middleware(app: FastAPI):
    """Add metrics collection middleware"""
    app.add_middleware(MetricsMiddleware)
    
    # Add metrics endpoint
    @app.get("/api/v1/metrics")
    async def get_metrics():
        """Get API metrics"""
        return metrics_collector.get_metrics()
