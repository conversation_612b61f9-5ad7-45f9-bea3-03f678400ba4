"""
FastAPI Server for Cognitive Code Weaver

Main API server providing REST endpoints and WebSocket connections
for web UI integration and external system integration.
"""

import asyncio
import logging
import time
import uuid
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.routing import APIRouter

from ccw.core.config import config, init_config
from ccw.core.registry import agent_registry
from ccw.core.message_bus import message_bus
from ccw.core.agent import create_agent_task, create_agent_context, AgentStatus
from ccw.agents.master_agent import MasterAgent
from ccw.analysis.workspace import WorkspaceAnalyzer
from ccw.api.models import *
from ccw.api.websocket import websocket_manager
from ccw.api.middleware import setup_middleware

logger = logging.getLogger(__name__)

# Global state
master_agent: Optional[MasterAgent] = None
workspace_analyzer: Optional[WorkspaceAnalyzer] = None
active_tasks: Dict[str, TaskInfo] = {}
active_analyses: Dict[str, AnalysisProgressUpdate] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Cognitive Code Weaver API server...")
    
    try:
        # Initialize configuration
        init_config()
        
        # Initialize global components
        global master_agent, workspace_analyzer
        
        # Start message bus
        await message_bus.start()
        
        # Initialize workspace analyzer
        workspace_analyzer = WorkspaceAnalyzer()
        
        # Initialize and start master agent
        master_agent = MasterAgent()
        await master_agent.startup()
        
        # Start WebSocket manager
        await websocket_manager.start()
        
        logger.info("API server startup completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to start API server: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down API server...")
    
    try:
        # Stop WebSocket manager
        await websocket_manager.stop()
        
        # Stop master agent
        if master_agent:
            await master_agent.shutdown()
        
        # Stop message bus
        await message_bus.stop()
        
        logger.info("API server shutdown completed")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI app
app = FastAPI(
    title="Cognitive Code Weaver API",
    description="AI-powered code analysis and reasoning system API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Setup middleware
setup_middleware(app)

# Create API router
api_router = APIRouter(prefix="/api/v1")


# Health and Status Endpoints
@api_router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint"""
    return HealthCheckResponse(
        version="1.0.0",
        uptime=time.time() - getattr(app.state, 'start_time', time.time())
    )


@api_router.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """Get comprehensive system status"""
    try:
        import psutil
        
        # Get agent information
        agents_info = agent_registry.list_agents()
        running_agents = sum(1 for agent in agents_info if agent.get('is_running', False))
        
        # Get task statistics
        completed_tasks = sum(1 for task in active_tasks.values() if task.status == AgentStatus.COMPLETED)
        failed_tasks = sum(1 for task in active_tasks.values() if task.status == AgentStatus.FAILED)
        active_task_count = sum(1 for task in active_tasks.values() if task.status == AgentStatus.RUNNING)
        
        # Get system metrics
        memory_usage = psutil.virtual_memory().used / 1024 / 1024  # MB
        cpu_usage = psutil.cpu_percent()
        
        # Get LLM provider status
        llm_status = {}
        try:
            from ccw.llm.client import llm_client
            providers = llm_client.get_available_providers()
            validation_results = await llm_client.validate_providers()
            llm_status = {provider: validation_results.get(provider, False) for provider in providers}
        except Exception:
            llm_status = {"error": "Unable to check LLM providers"}
        
        system_status = SystemStatus(
            version="1.0.0",
            uptime=time.time() - getattr(app.state, 'start_time', time.time()),
            agents_running=running_agents,
            total_agents=len(agents_info),
            active_tasks=active_task_count,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            message_bus_status="running" if message_bus.running else "stopped",
            llm_provider_status=llm_status
        )
        
        return SystemStatusResponse(system=system_status)
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")


# Analysis Endpoints
@api_router.post("/analysis", response_model=AnalysisResponse)
async def start_analysis(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start code analysis"""
    try:
        analysis_id = str(uuid.uuid4())
        
        # Create analysis task
        task_data = {
            "workspace_path": request.workspace_path,
            "analysis_type": request.analysis_type,
            "include_dependencies": request.include_dependencies,
            "include_metrics": request.include_metrics,
            "include_symbols": request.include_symbols,
            "max_files": request.max_files,
            "file_patterns": request.file_patterns,
            "exclude_patterns": request.exclude_patterns,
            "analysis_id": analysis_id
        }
        
        # Add background task
        background_tasks.add_task(
            _run_analysis_task,
            analysis_id,
            task_data,
            request.output_format
        )
        
        return AnalysisResponse(
            status=APIStatus.PROCESSING,
            message="Analysis started",
            task_id=analysis_id
        )
        
    except Exception as e:
        logger.error(f"Error starting analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start analysis: {str(e)}")


@api_router.get("/analysis/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis_result(analysis_id: str):
    """Get analysis result by ID"""
    try:
        if analysis_id in active_analyses:
            progress = active_analyses[analysis_id]
            return AnalysisResponse(
                status=APIStatus.PROCESSING,
                message=f"Analysis in progress: {progress.stage}",
                task_id=analysis_id
            )
        
        # Check if analysis is completed (would be stored in database in production)
        # For now, return not found
        raise HTTPException(status_code=404, detail="Analysis not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis result: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get analysis result: {str(e)}")


# Agent Management Endpoints
@api_router.get("/agents", response_model=AgentListResponse)
async def list_agents():
    """List all available agents"""
    try:
        agents_info = agent_registry.list_agents()
        
        agents = []
        for agent_info in agents_info:
            # Get agent instance for additional info
            agent = agent_registry.get_agent(agent_info['agent_id'])
            if agent:
                metrics = agent.get_metrics()
                agent_model = AgentInfo(
                    agent_id=agent_info['agent_id'],
                    name=agent_info['agent_id'].replace('_', ' ').title(),
                    description=f"Agent for {agent_info['agent_id']} operations",
                    capabilities=agent_info['capabilities'],
                    status=AgentStatus.RUNNING if agent_info['is_running'] else AgentStatus.IDLE,
                    is_running=agent_info['is_running'],
                    tasks_completed=metrics.get('tasks_completed', 0),
                    tasks_failed=metrics.get('tasks_failed', 0),
                    average_execution_time=metrics.get('average_execution_time', 0.0)
                )
                agents.append(agent_model)
        
        return AgentListResponse(agents=agents)
        
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list agents: {str(e)}")


@api_router.get("/agents/{agent_id}", response_model=AgentInfo)
async def get_agent_info(agent_id: str):
    """Get detailed information about a specific agent"""
    try:
        agent = agent_registry.get_agent(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent_info = agent.get_info()
        metrics = agent.get_metrics()
        
        return AgentInfo(
            agent_id=agent_id,
            name=agent_id.replace('_', ' ').title(),
            description=f"Agent for {agent_id} operations",
            capabilities=agent_info['capabilities'],
            status=AgentStatus.RUNNING if agent_info['is_running'] else AgentStatus.IDLE,
            is_running=agent_info['is_running'],
            tasks_completed=metrics.get('tasks_completed', 0),
            tasks_failed=metrics.get('tasks_failed', 0),
            average_execution_time=metrics.get('average_execution_time', 0.0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get agent info: {str(e)}")


# Task Management Endpoints
@api_router.post("/tasks", response_model=TaskResponse)
async def create_task(request: TaskRequest):
    """Create and execute a new task"""
    try:
        task_id = str(uuid.uuid4())
        
        # Create agent task
        agent_task = create_agent_task(
            task_type=request.task_type,
            description=request.description,
            data=request.data,
            priority=request.priority
        )
        agent_task.id = task_id
        
        # Create context
        context = create_agent_context(
            session_id=f"api_session_{int(time.time())}",
            user_query=request.description
        )
        
        # Store task info
        task_info = TaskInfo(
            task_id=task_id,
            task_type=request.task_type,
            description=request.description,
            status=AgentStatus.PENDING,
            created_at=datetime.now()
        )
        active_tasks[task_id] = task_info
        
        # Execute task asynchronously
        asyncio.create_task(_execute_task(agent_task, context, request.agent_id))
        
        return TaskResponse(
            status=APIStatus.PROCESSING,
            message="Task created and queued for execution",
            task=task_info
        )
        
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")


@api_router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(page: int = 1, page_size: int = 20):
    """List all tasks with pagination"""
    try:
        # Simple pagination (in production, use database)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        all_tasks = list(active_tasks.values())
        paginated_tasks = all_tasks[start_idx:end_idx]
        
        return TaskListResponse(tasks=paginated_tasks)
        
    except Exception as e:
        logger.error(f"Error listing tasks: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list tasks: {str(e)}")


@api_router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(task_id: str):
    """Get task information by ID"""
    try:
        if task_id not in active_tasks:
            raise HTTPException(status_code=404, detail="Task not found")
        
        task_info = active_tasks[task_id]
        return TaskResponse(task=task_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting task: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get task: {str(e)}")


# Query Endpoints
@api_router.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process natural language query about codebase"""
    try:
        if not master_agent:
            raise HTTPException(status_code=503, detail="Master agent not available")
        
        # Create query task
        task_data = {
            "user_query": request.query,
            "workspace_path": request.workspace_path,
            "include_context": request.include_context,
            "max_context_files": request.max_context_files
        }
        
        task = create_agent_task(
            task_type="natural_language_query",
            description=f"Process query: {request.query}",
            data=task_data
        )
        
        context = create_agent_context(
            session_id=f"query_session_{int(time.time())}",
            user_query=request.query,
            workspace_path=request.workspace_path
        )
        
        # Execute query
        result = await master_agent.execute_with_monitoring(task, context)
        
        if result.status == AgentStatus.COMPLETED:
            return QueryResponse(
                answer=result.data.get('answer', 'No answer generated'),
                confidence=result.data.get('confidence', 0.0),
                context_files=result.data.get('context_files', []),
                related_symbols=result.data.get('related_symbols', []),
                suggestions=result.data.get('suggestions', []),
                task_id=result.task_id
            )
        else:
            return QueryResponse(
                status=APIStatus.ERROR,
                message=result.error or "Query processing failed"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process query: {str(e)}")


# Helper functions
async def _run_analysis_task(analysis_id: str, task_data: Dict, output_format: OutputFormat):
    """Run analysis task in background"""
    try:
        if not workspace_analyzer:
            logger.error("Workspace analyzer not available")
            return
        
        # Update progress
        progress = AnalysisProgressUpdate(
            analysis_id=analysis_id,
            stage="initializing",
            files_processed=0,
            total_files=0,
            progress=0.0
        )
        active_analyses[analysis_id] = progress
        
        # Broadcast progress
        await websocket_manager.broadcast({
            "type": "analysis_progress",
            "data": progress.dict()
        })
        
        # Run analysis
        result = workspace_analyzer.analyze_workspace(
            workspace_path=task_data["workspace_path"],
            max_files=task_data.get("max_files")
        )
        
        # Update final progress
        progress.stage = "completed"
        progress.progress = 1.0
        active_analyses[analysis_id] = progress
        
        # Broadcast completion
        await websocket_manager.broadcast({
            "type": "analysis_complete",
            "data": {
                "analysis_id": analysis_id,
                "result": result.__dict__ if hasattr(result, '__dict__') else str(result)
            }
        })
        
        # Clean up
        del active_analyses[analysis_id]
        
    except Exception as e:
        logger.error(f"Error in analysis task {analysis_id}: {e}")
        
        # Broadcast error
        await websocket_manager.broadcast({
            "type": "analysis_error",
            "data": {
                "analysis_id": analysis_id,
                "error": str(e)
            }
        })
        
        # Clean up
        active_analyses.pop(analysis_id, None)


async def _execute_task(agent_task, context, agent_id: Optional[str] = None):
    """Execute agent task"""
    try:
        task_id = agent_task.id
        
        # Update task status
        if task_id in active_tasks:
            active_tasks[task_id].status = AgentStatus.RUNNING
            active_tasks[task_id].started_at = datetime.now()
        
        # Choose agent
        if agent_id:
            agent = agent_registry.get_agent(agent_id)
            if not agent:
                raise ValueError(f"Agent {agent_id} not found")
        else:
            agent = master_agent
        
        if not agent:
            raise ValueError("No agent available for task execution")
        
        # Execute task
        result = await agent.execute_with_monitoring(agent_task, context)
        
        # Update task status
        if task_id in active_tasks:
            active_tasks[task_id].status = result.status
            active_tasks[task_id].completed_at = datetime.now()
            active_tasks[task_id].duration = (
                active_tasks[task_id].completed_at - active_tasks[task_id].started_at
            ).total_seconds() if active_tasks[task_id].started_at else None
            active_tasks[task_id].result = result.data
            active_tasks[task_id].error = result.error
        
        # Broadcast task completion
        await websocket_manager.broadcast({
            "type": "task_complete",
            "data": {
                "task_id": task_id,
                "status": result.status.value,
                "result": result.data
            }
        })
        
    except Exception as e:
        logger.error(f"Error executing task {agent_task.id}: {e}")
        
        # Update task status
        if agent_task.id in active_tasks:
            active_tasks[agent_task.id].status = AgentStatus.FAILED
            active_tasks[agent_task.id].error = str(e)
            active_tasks[agent_task.id].completed_at = datetime.now()
        
        # Broadcast task error
        await websocket_manager.broadcast({
            "type": "task_error",
            "data": {
                "task_id": agent_task.id,
                "error": str(e)
            }
        })


# Configuration Endpoints
@api_router.get("/config", response_model=ConfigurationResponse)
async def get_configuration():
    """Get current system configuration"""
    try:
        # Get all configuration sections
        configuration = {
            "llm": config.get_section("llm", {}),
            "analysis": config.get_section("analysis", {}),
            "agents": config.get_section("agents", {}),
            "api": config.get_section("api", {}),
            "logging": config.get_section("logging", {})
        }

        return ConfigurationResponse(configuration=configuration)

    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {str(e)}")


@api_router.put("/config", response_model=ConfigurationResponse)
async def update_configuration(update: ConfigurationUpdate):
    """Update system configuration"""
    try:
        # Update configuration
        config.set(update.section, update.key, update.value)

        # Get updated section
        updated_section = config.get_section(update.section, {})

        return ConfigurationResponse(
            message=f"Configuration updated: {update.section}.{update.key}",
            configuration={update.section: updated_section}
        )

    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update configuration: {str(e)}")


# File Upload Endpoints
@api_router.post("/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """Upload file for analysis"""
    try:
        import tempfile
        import os

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        return FileUploadResponse(
            message="File uploaded successfully",
            file_path=temp_file_path,
            file_size=len(content)
        )

    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")


# Export Endpoints
@api_router.post("/export", response_model=ExportResponse)
async def export_analysis(request: ExportRequest):
    """Export analysis results"""
    try:
        # In production, this would generate and store export files
        # For now, return a mock response

        import tempfile
        import json

        # Create temporary export file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=f".{request.format.value}") as temp_file:
            if request.format == OutputFormat.JSON:
                export_data = {
                    "analysis_id": request.analysis_id,
                    "exported_at": datetime.now().isoformat(),
                    "format": request.format.value,
                    "include_raw_data": request.include_raw_data,
                    "include_visualizations": request.include_visualizations
                }
                json.dump(export_data, temp_file, indent=2)
            else:
                temp_file.write(f"Export data for analysis {request.analysis_id}")

            export_file_path = temp_file.name

        return ExportResponse(
            message="Export created successfully",
            download_url=f"/api/v1/download/{os.path.basename(export_file_path)}",
            file_size=os.path.getsize(export_file_path),
            expires_at=datetime.now() + timedelta(hours=24)
        )

    except Exception as e:
        logger.error(f"Error creating export: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create export: {str(e)}")


# Include router in app
app.include_router(api_router)

# Include WebSocket router
app.include_router(websocket_manager.get_router())

# Mount static files (for future UI)
# app.mount("/static", StaticFiles(directory="static"), name="static")

# Set startup time
@app.on_event("startup")
async def set_startup_time():
    app.state.start_time = time.time()
