"""
API Data Models for Cognitive Code Weaver

Pydantic models for request/response serialization, validation,
and documentation for the web API.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field, validator
from dataclasses import asdict

from ccw.core.agent import AgentStatus, TaskPriority
from ccw.analysis.parser import LanguageType


class APIStatus(str, Enum):
    """API response status"""
    SUCCESS = "success"
    ERROR = "error"
    PENDING = "pending"
    PROCESSING = "processing"


class AnalysisType(str, Enum):
    """Types of analysis that can be performed"""
    QUICK = "quick"
    STANDARD = "standard"
    DEEP = "deep"
    CUSTOM = "custom"


class OutputFormat(str, Enum):
    """Output format options"""
    JSON = "json"
    HTML = "html"
    MARKDOWN = "markdown"
    TEXT = "text"


# Base Response Models
class BaseResponse(BaseModel):
    """Base response model"""
    status: APIStatus
    message: str = ""
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = None


class ErrorResponse(BaseResponse):
    """Error response model"""
    status: APIStatus = APIStatus.ERROR
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


# Analysis Request/Response Models
class AnalysisRequest(BaseModel):
    """Request model for code analysis"""
    workspace_path: str = Field(..., description="Path to the workspace to analyze")
    analysis_type: AnalysisType = Field(default=AnalysisType.STANDARD, description="Type of analysis to perform")
    include_dependencies: bool = Field(default=True, description="Include dependency analysis")
    include_metrics: bool = Field(default=True, description="Include code metrics")
    include_symbols: bool = Field(default=True, description="Include symbol extraction")
    max_files: Optional[int] = Field(default=None, description="Maximum number of files to analyze")
    file_patterns: Optional[List[str]] = Field(default=None, description="File patterns to include")
    exclude_patterns: Optional[List[str]] = Field(default=None, description="File patterns to exclude")
    output_format: OutputFormat = Field(default=OutputFormat.JSON, description="Output format")
    
    @validator('workspace_path')
    def validate_workspace_path(cls, v):
        if not v or not v.strip():
            raise ValueError('Workspace path cannot be empty')
        return v.strip()


class FileInfo(BaseModel):
    """File information model"""
    path: str
    language: LanguageType
    size_bytes: int
    lines_of_code: int
    functions_count: int
    classes_count: int
    complexity_score: float
    last_modified: datetime


class AnalysisResult(BaseModel):
    """Analysis result model"""
    workspace_path: str
    analysis_type: AnalysisType
    total_files: int
    analyzed_files: int
    skipped_files: int
    total_lines_of_code: int
    total_functions: int
    total_classes: int
    average_complexity: float
    files: List[FileInfo]
    dependencies: Optional[Dict[str, Any]] = None
    metrics: Optional[Dict[str, Any]] = None
    symbols: Optional[List[Dict[str, Any]]] = None
    quality_report: Optional[Dict[str, Any]] = None
    analysis_duration: float
    errors: List[str] = []


class AnalysisResponse(BaseResponse):
    """Response model for analysis results"""
    status: APIStatus = APIStatus.SUCCESS
    result: Optional[AnalysisResult] = None
    task_id: Optional[str] = None


# Agent Models
class AgentInfo(BaseModel):
    """Agent information model"""
    agent_id: str
    name: str
    description: str
    capabilities: List[str]
    status: AgentStatus
    is_running: bool
    current_task: Optional[str] = None
    tasks_completed: int
    tasks_failed: int
    average_execution_time: float
    last_activity: Optional[datetime] = None


class AgentListResponse(BaseResponse):
    """Response model for agent list"""
    status: APIStatus = APIStatus.SUCCESS
    agents: List[AgentInfo]


class TaskRequest(BaseModel):
    """Request model for creating tasks"""
    task_type: str = Field(..., description="Type of task to execute")
    description: str = Field(..., description="Task description")
    data: Dict[str, Any] = Field(default_factory=dict, description="Task data")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    timeout: Optional[float] = Field(default=None, description="Task timeout in seconds")
    agent_id: Optional[str] = Field(default=None, description="Specific agent to execute task")


class TaskInfo(BaseModel):
    """Task information model"""
    task_id: str
    task_type: str
    description: str
    status: AgentStatus
    agent_id: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TaskResponse(BaseResponse):
    """Response model for task operations"""
    status: APIStatus = APIStatus.SUCCESS
    task: Optional[TaskInfo] = None


class TaskListResponse(BaseResponse):
    """Response model for task list"""
    status: APIStatus = APIStatus.SUCCESS
    tasks: List[TaskInfo]


# Query Models
class QueryRequest(BaseModel):
    """Request model for natural language queries"""
    query: str = Field(..., description="Natural language query about the codebase")
    workspace_path: Optional[str] = Field(default=None, description="Workspace context for the query")
    include_context: bool = Field(default=True, description="Include relevant code context in response")
    max_context_files: int = Field(default=5, description="Maximum number of context files to include")
    
    @validator('query')
    def validate_query(cls, v):
        if not v or not v.strip():
            raise ValueError('Query cannot be empty')
        if len(v.strip()) < 3:
            raise ValueError('Query must be at least 3 characters long')
        return v.strip()


class QueryResponse(BaseResponse):
    """Response model for query results"""
    status: APIStatus = APIStatus.SUCCESS
    answer: Optional[str] = None
    confidence: Optional[float] = None
    context_files: Optional[List[str]] = None
    related_symbols: Optional[List[str]] = None
    suggestions: Optional[List[str]] = None
    task_id: Optional[str] = None


# System Models
class SystemStatus(BaseModel):
    """System status model"""
    version: str
    uptime: float
    agents_running: int
    total_agents: int
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    memory_usage_mb: float
    cpu_usage_percent: float
    message_bus_status: str
    llm_provider_status: Dict[str, bool]
    database_status: Optional[str] = None


class SystemStatusResponse(BaseResponse):
    """Response model for system status"""
    status: APIStatus = APIStatus.SUCCESS
    system: SystemStatus


class ConfigurationUpdate(BaseModel):
    """Configuration update model"""
    section: str = Field(..., description="Configuration section to update")
    key: str = Field(..., description="Configuration key")
    value: Any = Field(..., description="New configuration value")
    
    @validator('section')
    def validate_section(cls, v):
        allowed_sections = ['llm', 'analysis', 'agents', 'ui', 'logging']
        if v not in allowed_sections:
            raise ValueError(f'Section must be one of: {allowed_sections}')
        return v


class ConfigurationResponse(BaseResponse):
    """Response model for configuration operations"""
    status: APIStatus = APIStatus.SUCCESS
    configuration: Optional[Dict[str, Any]] = None


# WebSocket Models
class WebSocketMessage(BaseModel):
    """WebSocket message model"""
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Message data")
    timestamp: datetime = Field(default_factory=datetime.now)
    source: Optional[str] = Field(default=None, description="Message source")


class AgentStatusUpdate(BaseModel):
    """Agent status update for WebSocket"""
    agent_id: str
    status: AgentStatus
    current_task: Optional[str] = None
    progress: Optional[float] = None
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class TaskProgressUpdate(BaseModel):
    """Task progress update for WebSocket"""
    task_id: str
    status: AgentStatus
    progress: float = Field(ge=0.0, le=1.0, description="Progress as a fraction (0.0 to 1.0)")
    message: Optional[str] = None
    agent_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class AnalysisProgressUpdate(BaseModel):
    """Analysis progress update for WebSocket"""
    analysis_id: str
    stage: str = Field(..., description="Current analysis stage")
    files_processed: int
    total_files: int
    current_file: Optional[str] = None
    progress: float = Field(ge=0.0, le=1.0)
    estimated_remaining: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# Utility Models
class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str
    uptime: float


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    page_size: int = Field(default=20, ge=1, le=100, description="Number of items per page")
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.page_size


class PaginatedResponse(BaseResponse):
    """Paginated response model"""
    page: int
    page_size: int
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool
    items: List[Any]


# File Upload Models
class FileUploadResponse(BaseResponse):
    """File upload response model"""
    status: APIStatus = APIStatus.SUCCESS
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    analysis_id: Optional[str] = None


# Export Models
class ExportRequest(BaseModel):
    """Export request model"""
    analysis_id: str = Field(..., description="Analysis ID to export")
    format: OutputFormat = Field(default=OutputFormat.JSON, description="Export format")
    include_raw_data: bool = Field(default=False, description="Include raw analysis data")
    include_visualizations: bool = Field(default=True, description="Include visualization data")


class ExportResponse(BaseResponse):
    """Export response model"""
    status: APIStatus = APIStatus.SUCCESS
    download_url: Optional[str] = None
    file_size: Optional[int] = None
    expires_at: Optional[datetime] = None
