"""
WebSocket Manager for Real-time Communication

Provides WebSocket connections for real-time updates on agent status,
task progress, analysis results, and system events.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Set, Any, Optional
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect
from fastapi.routing import APIRouter

from ccw.core.message_bus import message_bus, MessageHandler, Message, MessageType
from ccw.api.models import WebSocketMessage, AgentStatusUpdate, TaskProgressUpdate, AnalysisProgressUpdate

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and message broadcasting"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # connection_id -> set of topics
        self.topic_subscribers: Dict[str, Set[str]] = {}  # topic -> set of connection_ids
        self.message_history: List[WebSocketMessage] = []
        self.max_history_size = 1000
    
    async def connect(self, websocket: WebSocket, connection_id: str, 
                     client_info: Optional[Dict[str, Any]] = None):
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "connected_at": datetime.now(),
            "client_info": client_info or {},
            "last_activity": datetime.now()
        }
        self.subscriptions[connection_id] = set()
        
        logger.info(f"WebSocket connection established: {connection_id}")
        
        # Send connection confirmation
        await self.send_personal_message(connection_id, {
            "type": "connection_established",
            "data": {
                "connection_id": connection_id,
                "server_time": datetime.now().isoformat(),
                "available_topics": list(self.get_available_topics())
            }
        })
        
        # Send recent message history
        await self.send_message_history(connection_id)
    
    def disconnect(self, connection_id: str):
        """Remove WebSocket connection"""
        if connection_id in self.active_connections:
            # Remove from all topic subscriptions
            for topic in self.subscriptions.get(connection_id, set()):
                if topic in self.topic_subscribers:
                    self.topic_subscribers[topic].discard(connection_id)
                    if not self.topic_subscribers[topic]:
                        del self.topic_subscribers[topic]
            
            # Clean up connection data
            del self.active_connections[connection_id]
            self.connection_metadata.pop(connection_id, None)
            self.subscriptions.pop(connection_id, None)
            
            logger.info(f"WebSocket connection closed: {connection_id}")
    
    async def send_personal_message(self, connection_id: str, message: Dict[str, Any]):
        """Send message to specific connection"""
        if connection_id in self.active_connections:
            try:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message))
                
                # Update last activity
                if connection_id in self.connection_metadata:
                    self.connection_metadata[connection_id]["last_activity"] = datetime.now()
                    
            except Exception as e:
                logger.error(f"Error sending message to {connection_id}: {e}")
                # Remove broken connection
                self.disconnect(connection_id)
    
    async def broadcast(self, message: Dict[str, Any], topic: Optional[str] = None):
        """Broadcast message to all connections or topic subscribers"""
        ws_message = WebSocketMessage(
            type=message.get("type", "broadcast"),
            data=message.get("data", {}),
            source="server"
        )
        
        # Add to message history
        self.message_history.append(ws_message)
        if len(self.message_history) > self.max_history_size:
            self.message_history.pop(0)
        
        # Determine recipients
        if topic and topic in self.topic_subscribers:
            recipients = self.topic_subscribers[topic]
        else:
            recipients = set(self.active_connections.keys())
        
        # Send to recipients
        message_json = json.dumps(ws_message.dict())
        disconnected = []
        
        for connection_id in recipients:
            if connection_id in self.active_connections:
                try:
                    websocket = self.active_connections[connection_id]
                    await websocket.send_text(message_json)
                    
                    # Update last activity
                    if connection_id in self.connection_metadata:
                        self.connection_metadata[connection_id]["last_activity"] = datetime.now()
                        
                except Exception as e:
                    logger.error(f"Error broadcasting to {connection_id}: {e}")
                    disconnected.append(connection_id)
        
        # Clean up disconnected connections
        for connection_id in disconnected:
            self.disconnect(connection_id)
    
    async def send_message_history(self, connection_id: str, limit: int = 50):
        """Send recent message history to connection"""
        recent_messages = self.message_history[-limit:] if self.message_history else []
        
        await self.send_personal_message(connection_id, {
            "type": "message_history",
            "data": {
                "messages": [msg.dict() for msg in recent_messages],
                "total_messages": len(self.message_history)
            }
        })
    
    def subscribe_to_topic(self, connection_id: str, topic: str):
        """Subscribe connection to topic"""
        if connection_id in self.subscriptions:
            self.subscriptions[connection_id].add(topic)
            
            if topic not in self.topic_subscribers:
                self.topic_subscribers[topic] = set()
            self.topic_subscribers[topic].add(connection_id)
            
            logger.debug(f"Connection {connection_id} subscribed to topic: {topic}")
    
    def unsubscribe_from_topic(self, connection_id: str, topic: str):
        """Unsubscribe connection from topic"""
        if connection_id in self.subscriptions:
            self.subscriptions[connection_id].discard(topic)
            
        if topic in self.topic_subscribers:
            self.topic_subscribers[topic].discard(connection_id)
            if not self.topic_subscribers[topic]:
                del self.topic_subscribers[topic]
                
        logger.debug(f"Connection {connection_id} unsubscribed from topic: {topic}")
    
    def get_available_topics(self) -> Set[str]:
        """Get list of available topics"""
        return {
            "agent_status",
            "task_progress", 
            "analysis_progress",
            "system_events",
            "error_notifications",
            "performance_metrics"
        }
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "active_topics": len(self.topic_subscribers),
            "message_history_size": len(self.message_history),
            "connections": [
                {
                    "connection_id": conn_id,
                    "connected_at": metadata["connected_at"].isoformat(),
                    "last_activity": metadata["last_activity"].isoformat(),
                    "subscribed_topics": list(self.subscriptions.get(conn_id, set())),
                    "client_info": metadata.get("client_info", {})
                }
                for conn_id, metadata in self.connection_metadata.items()
            ]
        }


class WebSocketManager(MessageHandler):
    """WebSocket manager with message bus integration"""
    
    def __init__(self):
        super().__init__("websocket_manager")
        self.connection_manager = ConnectionManager()
        self.router = APIRouter()
        self.setup_routes()
        self.running = False
    
    def setup_routes(self):
        """Setup WebSocket routes"""
        
        @self.router.websocket("/ws/{connection_id}")
        async def websocket_endpoint(websocket: WebSocket, connection_id: str):
            await self.handle_websocket_connection(websocket, connection_id)
    
    async def handle_websocket_connection(self, websocket: WebSocket, connection_id: str):
        """Handle individual WebSocket connection"""
        await self.connection_manager.connect(websocket, connection_id)
        
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                await self.handle_client_message(connection_id, message_data)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected: {connection_id}")
        except Exception as e:
            logger.error(f"WebSocket error for {connection_id}: {e}")
        finally:
            self.connection_manager.disconnect(connection_id)
    
    async def handle_client_message(self, connection_id: str, message_data: Dict[str, Any]):
        """Handle message from WebSocket client"""
        try:
            message_type = message_data.get("type")
            data = message_data.get("data", {})
            
            if message_type == "subscribe":
                # Subscribe to topic
                topic = data.get("topic")
                if topic:
                    self.connection_manager.subscribe_to_topic(connection_id, topic)
                    await self.connection_manager.send_personal_message(connection_id, {
                        "type": "subscription_confirmed",
                        "data": {"topic": topic}
                    })
            
            elif message_type == "unsubscribe":
                # Unsubscribe from topic
                topic = data.get("topic")
                if topic:
                    self.connection_manager.unsubscribe_from_topic(connection_id, topic)
                    await self.connection_manager.send_personal_message(connection_id, {
                        "type": "unsubscription_confirmed",
                        "data": {"topic": topic}
                    })
            
            elif message_type == "ping":
                # Respond to ping
                await self.connection_manager.send_personal_message(connection_id, {
                    "type": "pong",
                    "data": {"timestamp": datetime.now().isoformat()}
                })
            
            elif message_type == "get_stats":
                # Send connection statistics
                stats = self.connection_manager.get_connection_stats()
                await self.connection_manager.send_personal_message(connection_id, {
                    "type": "connection_stats",
                    "data": stats
                })
            
            elif message_type == "get_history":
                # Send message history
                limit = data.get("limit", 50)
                await self.connection_manager.send_message_history(connection_id, limit)
            
            else:
                logger.warning(f"Unknown message type from {connection_id}: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling client message from {connection_id}: {e}")
            await self.connection_manager.send_personal_message(connection_id, {
                "type": "error",
                "data": {"message": f"Error processing message: {str(e)}"}
            })
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """Handle messages from message bus"""
        try:
            # Convert message bus messages to WebSocket messages
            if message.type == MessageType.STATUS_UPDATE:
                await self.broadcast_agent_status_update(message)
            
            elif message.type == MessageType.TASK_PROGRESS:
                await self.broadcast_task_progress(message)
            
            elif message.type == MessageType.SYSTEM_EVENT:
                await self.broadcast_system_event(message)
            
            elif message.type == MessageType.ERROR:
                await self.broadcast_error_notification(message)
            
        except Exception as e:
            logger.error(f"Error handling message bus message: {e}")
        
        return None
    
    async def broadcast_agent_status_update(self, message: Message):
        """Broadcast agent status update"""
        status_update = AgentStatusUpdate(
            agent_id=message.sender,
            status=message.payload.get("status", "unknown"),
            current_task=message.payload.get("current_task"),
            progress=message.payload.get("progress"),
            message=message.payload.get("message")
        )
        
        await self.connection_manager.broadcast({
            "type": "agent_status_update",
            "data": status_update.dict()
        }, topic="agent_status")
    
    async def broadcast_task_progress(self, message: Message):
        """Broadcast task progress update"""
        progress_update = TaskProgressUpdate(
            task_id=message.payload.get("task_id", "unknown"),
            status=message.payload.get("status", "unknown"),
            progress=message.payload.get("progress", 0.0),
            message=message.payload.get("message"),
            agent_id=message.sender
        )
        
        await self.connection_manager.broadcast({
            "type": "task_progress_update",
            "data": progress_update.dict()
        }, topic="task_progress")
    
    async def broadcast_system_event(self, message: Message):
        """Broadcast system event"""
        await self.connection_manager.broadcast({
            "type": "system_event",
            "data": {
                "event_type": message.payload.get("event_type", "unknown"),
                "source": message.sender,
                "details": message.payload.get("details", {}),
                "timestamp": datetime.now().isoformat()
            }
        }, topic="system_events")
    
    async def broadcast_error_notification(self, message: Message):
        """Broadcast error notification"""
        await self.connection_manager.broadcast({
            "type": "error_notification",
            "data": {
                "source": message.sender,
                "error_type": message.payload.get("error_type", "unknown"),
                "error_message": message.payload.get("error_message", ""),
                "details": message.payload.get("details", {}),
                "timestamp": datetime.now().isoformat()
            }
        }, topic="error_notifications")
    
    async def broadcast(self, message: Dict[str, Any], topic: Optional[str] = None):
        """Public method to broadcast messages"""
        await self.connection_manager.broadcast(message, topic)
    
    async def start(self):
        """Start WebSocket manager"""
        if self.running:
            return
        
        self.running = True
        
        # Register with message bus
        message_bus.register_handler(self)
        message_bus.subscribe_to_topic(self.handler_id, "agent_status")
        message_bus.subscribe_to_topic(self.handler_id, "task_progress")
        message_bus.subscribe_to_topic(self.handler_id, "system_events")
        message_bus.subscribe_to_topic(self.handler_id, "error_notifications")
        
        logger.info("WebSocket manager started")
    
    async def stop(self):
        """Stop WebSocket manager"""
        if not self.running:
            return
        
        self.running = False
        
        # Disconnect all connections
        for connection_id in list(self.connection_manager.active_connections.keys()):
            self.connection_manager.disconnect(connection_id)
        
        # Unregister from message bus
        message_bus.unregister_handler(self.handler_id)
        
        logger.info("WebSocket manager stopped")
    
    def get_router(self) -> APIRouter:
        """Get FastAPI router for WebSocket endpoints"""
        return self.router
    
    def get_stats(self) -> Dict[str, Any]:
        """Get WebSocket manager statistics"""
        return {
            "running": self.running,
            "connection_stats": self.connection_manager.get_connection_stats(),
            "handler_id": self.handler_id
        }


# Global WebSocket manager instance
websocket_manager = WebSocketManager()
