"""
API Package for Cognitive Code Weaver

Provides REST API endpoints and WebSocket connections for web UI integration.
Includes real-time agent monitoring, analysis workflows, and system management.
"""

from .server import app, api_router
from .models import *
from .websocket import websocket_manager
from .middleware import setup_middleware

__all__ = [
    "app",
    "api_router", 
    "websocket_manager",
    "setup_middleware"
]
