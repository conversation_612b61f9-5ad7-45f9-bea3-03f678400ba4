"""
Planner Agent - Task planning and decomposition

Responsible for breaking down complex tasks into manageable sub-tasks
and creating execution plans for other agents.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus, TaskPriority
from ccw.core.registry import register_agent
from ccw.core.config import config

logger = logging.getLogger(__name__)


class PlanType(Enum):
    """Types of plans the planner can create"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    ITERATIVE = "iterative"


@dataclass
class TaskStep:
    """Represents a single step in a task plan"""
    step_id: str
    agent_type: str
    task_type: str
    description: str
    inputs: Dict[str, Any]
    expected_outputs: List[str]
    dependencies: List[str]
    estimated_time: float
    priority: TaskPriority
    metadata: Dict[str, Any]


@dataclass
class ExecutionPlan:
    """Represents a complete execution plan"""
    plan_id: str
    plan_type: PlanType
    description: str
    steps: List[TaskStep]
    estimated_total_time: float
    success_probability: float
    alternative_plans: List['ExecutionPlan']
    metadata: Dict[str, Any]


@register_agent("planner", capabilities=[
    "task_decomposition",
    "execution_planning",
    "dependency_analysis",
    "resource_optimization",
    "plan_validation"
])
class PlannerAgent(Agent):
    """
    Planner Agent responsible for task planning and decomposition.
    
    Responsibilities:
    - Decompose complex tasks into sub-tasks
    - Create execution plans for agent coordination
    - Analyze task dependencies
    - Optimize resource allocation
    - Validate and adjust plans
    """
    
    def __init__(self, agent_id: str = "planner", config_dict: Dict[str, Any] = None):
        super().__init__(agent_id, config_dict or {})
        
        self.plan_templates: Dict[str, Dict[str, Any]] = {}
        self.execution_history: List[ExecutionPlan] = []
        self.agent_capabilities: Dict[str, List[str]] = {}
        
        # Configuration
        self.max_plan_depth = config.get("planner.max_plan_depth", 5)
        self.default_step_time = config.get("planner.default_step_time", 30.0)
        self.enable_parallel_planning = config.get("planner.enable_parallel_planning", True)
        
        # Load plan templates
        self._load_plan_templates()
        
        self.logger.info("Planner Agent initialized")
    
    def _define_capabilities(self) -> List[str]:
        """Define planner agent capabilities"""
        return [
            "task_decomposition",
            "execution_planning",
            "dependency_analysis",
            "resource_optimization",
            "plan_validation",
            "timeline_estimation",
            "risk_assessment"
        ]
    
    def _define_dependencies(self) -> List[str]:
        """Planner depends on agent registry for capability information"""
        return ["agent_registry"]
    
    async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Execute planning task.
        
        Args:
            task: Planning task to execute
            context: Execution context
            
        Returns:
            Planning result
        """
        if task.type == "task_decomposition":
            return await self._decompose_task(task, context)
        elif task.type == "execution_planning":
            return await self._create_execution_plan(task, context)
        elif task.type == "dependency_analysis":
            return await self._analyze_dependencies(task, context)
        elif task.type == "resource_optimization":
            return await self._optimize_resources(task, context)
        elif task.type == "plan_validation":
            return await self._validate_plan(task, context)
        else:
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=f"Unknown task type: {task.type}"
            )
    
    async def _decompose_task(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Decompose a complex task into manageable sub-tasks.
        
        Args:
            task: Task to decompose
            context: Execution context
            
        Returns:
            Task decomposition result
        """
        try:
            main_task = task.data.get("main_task", "")
            complexity = task.data.get("complexity", "medium")
            
            self.logger.info(f"Decomposing task: {main_task}")
            
            # Analyze the main task
            task_analysis = await self._analyze_main_task(main_task, context)
            
            # Generate sub-tasks based on analysis
            sub_tasks = await self._generate_sub_tasks(task_analysis, complexity)
            
            # Analyze dependencies between sub-tasks
            dependencies = await self._analyze_task_dependencies(sub_tasks)
            
            # Estimate execution times
            time_estimates = await self._estimate_execution_times(sub_tasks)
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "main_task": main_task,
                    "sub_tasks": sub_tasks,
                    "dependencies": dependencies,
                    "time_estimates": time_estimates,
                    "total_estimated_time": sum(time_estimates.values()),
                    "decomposition_confidence": task_analysis.get("confidence", 0.8)
                },
                confidence=task_analysis.get("confidence", 0.8)
            )
            
        except Exception as e:
            self.logger.error(f"Error decomposing task: {e}")
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _create_execution_plan(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Create a detailed execution plan for a set of tasks.
        
        Args:
            task: Planning task
            context: Execution context
            
        Returns:
            Execution plan result
        """
        try:
            tasks_to_plan = task.data.get("tasks", [])
            plan_type = PlanType(task.data.get("plan_type", "sequential"))
            
            self.logger.info(f"Creating {plan_type.value} execution plan for {len(tasks_to_plan)} tasks")
            
            # Create task steps
            steps = []
            for i, task_info in enumerate(tasks_to_plan):
                step = await self._create_task_step(task_info, i, context)
                steps.append(step)
            
            # Optimize step order based on dependencies
            optimized_steps = await self._optimize_step_order(steps, plan_type)
            
            # Calculate total time and success probability
            total_time = await self._calculate_total_time(optimized_steps, plan_type)
            success_probability = await self._estimate_success_probability(optimized_steps)
            
            # Create the execution plan
            execution_plan = ExecutionPlan(
                plan_id=f"plan_{int(time.time())}",
                plan_type=plan_type,
                description=f"Execution plan for {len(optimized_steps)} tasks",
                steps=optimized_steps,
                estimated_total_time=total_time,
                success_probability=success_probability,
                alternative_plans=[],
                metadata={
                    "created_at": time.time(),
                    "planner_agent": self.agent_id,
                    "context_session": context.session_id
                }
            )
            
            # Store in history
            self.execution_history.append(execution_plan)
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "execution_plan": execution_plan,
                    "plan_summary": {
                        "total_steps": len(optimized_steps),
                        "estimated_time": total_time,
                        "success_probability": success_probability,
                        "plan_type": plan_type.value
                    }
                },
                confidence=success_probability
            )
            
        except Exception as e:
            self.logger.error(f"Error creating execution plan: {e}")
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _analyze_main_task(self, main_task: str, context: AgentContext) -> Dict[str, Any]:
        """Analyze the main task to understand its requirements"""
        task_lower = main_task.lower()
        
        analysis = {
            "task_type": "general",
            "complexity": "medium",
            "requires_code_reading": False,
            "requires_reasoning": False,
            "requires_analysis": False,
            "requires_synthesis": False,
            "confidence": 0.8
        }
        
        # Determine task type and requirements
        if any(word in task_lower for word in ["explain", "describe", "how"]):
            analysis["task_type"] = "explanation"
            analysis["requires_code_reading"] = True
            analysis["requires_reasoning"] = True
        
        elif any(word in task_lower for word in ["find", "search", "locate"]):
            analysis["task_type"] = "search"
            analysis["requires_code_reading"] = True
            analysis["requires_analysis"] = True
        
        elif any(word in task_lower for word in ["analyze", "examine", "review"]):
            analysis["task_type"] = "analysis"
            analysis["requires_code_reading"] = True
            analysis["requires_analysis"] = True
            analysis["requires_reasoning"] = True
        
        elif any(word in task_lower for word in ["fix", "debug", "solve"]):
            analysis["task_type"] = "debugging"
            analysis["requires_code_reading"] = True
            analysis["requires_reasoning"] = True
            analysis["requires_analysis"] = True
        
        # Assess complexity
        word_count = len(main_task.split())
        if word_count > 20:
            analysis["complexity"] = "high"
        elif word_count < 5:
            analysis["complexity"] = "low"
        
        return analysis
    
    async def _generate_sub_tasks(self, task_analysis: Dict[str, Any], 
                                 complexity: str) -> List[Dict[str, Any]]:
        """Generate sub-tasks based on task analysis"""
        sub_tasks = []
        
        # Always start with context building
        sub_tasks.append({
            "task_id": "context_building",
            "agent_type": "cognitive_agent",
            "task_type": "context_building",
            "description": "Build context and analyze intent",
            "priority": "high"
        })
        
        # Add task-specific sub-tasks
        if task_analysis.get("requires_code_reading", False):
            sub_tasks.append({
                "task_id": "code_reading",
                "agent_type": "code_reader",
                "task_type": "code_analysis",
                "description": "Read and analyze code structure",
                "priority": "normal"
            })
        
        if task_analysis.get("requires_reasoning", False):
            sub_tasks.append({
                "task_id": "reasoning",
                "agent_type": "reasoner",
                "task_type": "logical_reasoning",
                "description": "Perform logical reasoning and inference",
                "priority": "normal"
            })
        
        if task_analysis.get("requires_analysis", False):
            sub_tasks.append({
                "task_id": "analysis",
                "agent_type": "analyzer",
                "task_type": "detailed_analysis",
                "description": "Perform detailed analysis",
                "priority": "normal"
            })
        
        # Add synthesis task if multiple sub-tasks
        if len(sub_tasks) > 2:
            sub_tasks.append({
                "task_id": "synthesis",
                "agent_type": "cognitive_agent",
                "task_type": "result_synthesis",
                "description": "Synthesize results from all agents",
                "priority": "high"
            })
        
        return sub_tasks
    
    async def _analyze_task_dependencies(self, sub_tasks: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Analyze dependencies between sub-tasks"""
        dependencies = {}
        
        for task in sub_tasks:
            task_id = task["task_id"]
            deps = []
            
            # Context building has no dependencies
            if task_id == "context_building":
                deps = []
            
            # Most tasks depend on context building
            elif task_id in ["code_reading", "reasoning", "analysis"]:
                deps = ["context_building"]
            
            # Synthesis depends on all other tasks
            elif task_id == "synthesis":
                deps = [t["task_id"] for t in sub_tasks if t["task_id"] != "synthesis"]
            
            dependencies[task_id] = deps
        
        return dependencies
    
    async def _estimate_execution_times(self, sub_tasks: List[Dict[str, Any]]) -> Dict[str, float]:
        """Estimate execution times for sub-tasks"""
        time_estimates = {}
        
        # Base time estimates (in seconds)
        base_times = {
            "context_building": 10.0,
            "code_reading": 15.0,
            "reasoning": 20.0,
            "analysis": 25.0,
            "synthesis": 10.0
        }
        
        for task in sub_tasks:
            task_id = task["task_id"]
            base_time = base_times.get(task_id, self.default_step_time)
            
            # Adjust based on priority
            if task.get("priority") == "high":
                time_estimates[task_id] = base_time * 1.2
            elif task.get("priority") == "low":
                time_estimates[task_id] = base_time * 0.8
            else:
                time_estimates[task_id] = base_time
        
        return time_estimates
    
    async def _create_task_step(self, task_info: Dict[str, Any], 
                               step_index: int, context: AgentContext) -> TaskStep:
        """Create a TaskStep from task information"""
        return TaskStep(
            step_id=f"step_{step_index}_{task_info['task_id']}",
            agent_type=task_info["agent_type"],
            task_type=task_info["task_type"],
            description=task_info["description"],
            inputs=task_info.get("inputs", {}),
            expected_outputs=task_info.get("expected_outputs", []),
            dependencies=task_info.get("dependencies", []),
            estimated_time=task_info.get("estimated_time", self.default_step_time),
            priority=TaskPriority[task_info.get("priority", "NORMAL").upper()],
            metadata=task_info.get("metadata", {})
        )
    
    async def _optimize_step_order(self, steps: List[TaskStep], 
                                  plan_type: PlanType) -> List[TaskStep]:
        """Optimize the order of steps based on dependencies and plan type"""
        if plan_type == PlanType.SEQUENTIAL:
            # Simple dependency-based ordering
            ordered_steps = []
            remaining_steps = steps.copy()
            
            while remaining_steps:
                # Find steps with no unmet dependencies
                ready_steps = []
                for step in remaining_steps:
                    deps_met = all(
                        any(s.step_id.endswith(dep) for s in ordered_steps)
                        for dep in step.dependencies
                    )
                    if deps_met:
                        ready_steps.append(step)
                
                if not ready_steps:
                    # If no steps are ready, take the first one (break circular deps)
                    ready_steps = [remaining_steps[0]]
                
                # Add highest priority step
                next_step = max(ready_steps, key=lambda s: s.priority.value)
                ordered_steps.append(next_step)
                remaining_steps.remove(next_step)
            
            return ordered_steps
        
        else:
            # For other plan types, return as-is for now
            return steps
    
    async def _calculate_total_time(self, steps: List[TaskStep], 
                                   plan_type: PlanType) -> float:
        """Calculate total execution time based on plan type"""
        if plan_type == PlanType.SEQUENTIAL:
            return sum(step.estimated_time for step in steps)
        elif plan_type == PlanType.PARALLEL:
            return max(step.estimated_time for step in steps) if steps else 0.0
        else:
            # Conservative estimate for other types
            return sum(step.estimated_time for step in steps) * 0.8
    
    async def _estimate_success_probability(self, steps: List[TaskStep]) -> float:
        """Estimate the probability of successful plan execution"""
        if not steps:
            return 1.0
        
        # Simple model: each step has 90% success rate
        step_success_rate = 0.9
        return step_success_rate ** len(steps)
    
    def _load_plan_templates(self):
        """Load predefined plan templates"""
        self.plan_templates = {
            "code_explanation": {
                "steps": ["context_building", "code_reading", "reasoning", "synthesis"],
                "plan_type": "sequential"
            },
            "bug_detection": {
                "steps": ["context_building", "code_reading", "bug_detection", "synthesis"],
                "plan_type": "sequential"
            },
            "code_search": {
                "steps": ["context_building", "code_reading", "search"],
                "plan_type": "sequential"
            }
        }
    
    async def _analyze_dependencies(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Analyze dependencies for a task"""
        # Implementation for dependency analysis
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"dependency_analysis": "completed"}
        )
    
    async def _optimize_resources(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Optimize resource allocation"""
        # Implementation for resource optimization
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"resource_optimization": "completed"}
        )
    
    async def _validate_plan(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Validate an execution plan"""
        # Implementation for plan validation
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"plan_validation": "completed"}
        )
