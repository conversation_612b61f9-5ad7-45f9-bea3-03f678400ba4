"""
Master Agent - Central orchestrator for all agent activities

Coordinates sub-agents, manages task distribution, and provides the main
interface for user queries and system operations.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus, TaskPriority
from ccw.core.registry import agent_registry, register_agent
from ccw.core.message_bus import message_bus, MessageHandler, Message, MessageType
from ccw.core.config import config
from ccw.llm.client import llm_client
from ccw.llm.providers.base import create_system_message, create_user_message

logger = logging.getLogger(__name__)


@dataclass
class QueryPlan:
    """Represents a plan for executing a user query"""
    query_id: str
    user_query: str
    task_sequence: List[AgentTask]
    estimated_time: float
    confidence: float
    metadata: Dict[str, Any]


@register_agent("master_agent", capabilities=[
    "query_orchestration",
    "task_planning", 
    "agent_coordination",
    "result_synthesis",
    "system_management"
])
class MasterAgent(Agent, MessageHandler):
    """
    Master Agent that orchestrates all other agents in the system.
    
    Responsibilities:
    - Receive and parse user queries
    - Create execution plans
    - Coordinate sub-agents
    - Synthesize results
    - Manage system resources
    """
    
    def __init__(self, agent_id: str = "master_agent", config_dict: Dict[str, Any] = None):
        Agent.__init__(self, agent_id, config_dict or {})
        MessageHandler.__init__(self, agent_id)
        
        self.active_queries: Dict[str, QueryPlan] = {}
        self.agent_performance: Dict[str, Dict[str, float]] = {}
        self.system_metrics = {
            "queries_processed": 0,
            "total_processing_time": 0.0,
            "average_response_time": 0.0,
            "success_rate": 0.0
        }
        
        # Configuration
        self.max_concurrent_queries = config.get("agents.max_concurrent_tasks", 5)
        self.default_timeout = config.get("agents.task_timeout", 300.0)
        
        # Register with message bus
        message_bus.register_handler(self)
        message_bus.subscribe_to_topic(self.agent_id, "system_events")
        message_bus.subscribe_to_topic(self.agent_id, "agent_status")
        
        self.logger.info("Master Agent initialized")
    
    def _define_capabilities(self) -> List[str]:
        """Define master agent capabilities"""
        return [
            "query_orchestration",
            "task_planning",
            "agent_coordination", 
            "result_synthesis",
            "system_management",
            "performance_monitoring"
        ]
    
    def _define_dependencies(self) -> List[str]:
        """Master agent depends on cognitive agent and sub-agents"""
        return ["cognitive_agent"]
    
    async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Execute a task based on its type.
        
        Args:
            task: Task to execute
            context: Execution context
            
        Returns:
            Execution result
        """
        if task.type == "query_orchestration":
            return await self._handle_user_query(task, context)
        elif task.type == "task_planning":
            return await self._create_task_plan(task, context)
        elif task.type == "system_management":
            return await self._handle_system_command(task, context)
        elif task.type == "performance_monitoring":
            return await self._get_system_metrics(task, context)
        else:
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=f"Unknown task type: {task.type}"
            )
    
    async def _handle_user_query(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Handle a user query by orchestrating appropriate agents.
        
        Args:
            task: Query task
            context: Query context
            
        Returns:
            Query result
        """
        start_time = time.time()
        user_query = task.data.get("query", context.user_query)
        
        try:
            self.logger.info(f"Processing user query: {user_query[:100]}...")
            
            # Create query plan
            plan = await self._create_query_plan(user_query, context)
            self.active_queries[task.id] = plan
            
            # Execute the plan
            results = await self._execute_query_plan(plan, context)
            
            # Synthesize final result
            final_result = await self._synthesize_results(results, user_query)
            
            # Update metrics
            processing_time = time.time() - start_time
            self._update_system_metrics(processing_time, success=True)
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "query": user_query,
                    "answer": final_result.get("answer", ""),
                    "suggestions": final_result.get("suggestions", []),
                    "confidence": final_result.get("confidence", 0.0),
                    "processing_time": processing_time,
                    "agents_used": final_result.get("agents_used", [])
                },
                confidence=final_result.get("confidence", 0.0),
                suggestions=final_result.get("suggestions", [])
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_system_metrics(processing_time, success=False)
            
            self.logger.error(f"Error processing query: {e}")
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e),
                execution_time=processing_time
            )
        finally:
            # Clean up
            self.active_queries.pop(task.id, None)
    
    async def _create_query_plan(self, user_query: str, context: AgentContext) -> QueryPlan:
        """
        Create an execution plan for a user query.
        
        Args:
            user_query: The user's query
            context: Query context
            
        Returns:
            Query execution plan
        """
        # Analyze query intent and complexity
        query_analysis = await self._analyze_query_intent(user_query)
        
        # Determine required agents and task sequence
        task_sequence = []
        estimated_time = 0.0
        
        # Always start with cognitive agent for context building
        cognitive_task = AgentTask(
            type="context_building",
            description="Build context for query processing",
            data={"query": user_query, "analysis": query_analysis},
            priority=TaskPriority.HIGH
        )
        task_sequence.append(cognitive_task)
        estimated_time += 10.0  # Estimated time for cognitive processing
        
        # Add specific agents based on query type
        if query_analysis.get("requires_code_reading", False):
            code_reader_task = AgentTask(
                type="code_analysis",
                description="Analyze code structure and content",
                data={"query": user_query},
                priority=TaskPriority.NORMAL
            )
            task_sequence.append(code_reader_task)
            estimated_time += 15.0
        
        if query_analysis.get("requires_reasoning", False):
            reasoner_task = AgentTask(
                type="logical_reasoning",
                description="Perform logical reasoning and inference",
                data={"query": user_query},
                priority=TaskPriority.NORMAL
            )
            task_sequence.append(reasoner_task)
            estimated_time += 20.0
        
        if query_analysis.get("requires_bug_detection", False):
            bug_detector_task = AgentTask(
                type="bug_analysis",
                description="Analyze code for potential bugs",
                data={"query": user_query},
                priority=TaskPriority.NORMAL
            )
            task_sequence.append(bug_detector_task)
            estimated_time += 25.0
        
        return QueryPlan(
            query_id=f"query_{int(time.time())}",
            user_query=user_query,
            task_sequence=task_sequence,
            estimated_time=estimated_time,
            confidence=query_analysis.get("confidence", 0.8),
            metadata=query_analysis
        )
    
    async def _analyze_query_intent(self, user_query: str) -> Dict[str, Any]:
        """
        Analyze user query to determine intent and required capabilities using LLM.

        Args:
            user_query: The user's query

        Returns:
            Query analysis results
        """
        try:
            # Use LLM for advanced intent analysis
            messages = [
                create_system_message("""You are an expert at analyzing user queries about code and software development.
Analyze the given query and return a JSON object with the following structure:
{
    "query_type": "explanation|bug_detection|optimization|search|planning|general",
    "requires_code_reading": boolean,
    "requires_reasoning": boolean,
    "requires_bug_detection": boolean,
    "requires_planning": boolean,
    "complexity": "low|medium|high",
    "confidence": float (0.0-1.0),
    "key_concepts": ["concept1", "concept2"],
    "suggested_approach": "brief description"
}"""),
                create_user_message(f"Analyze this query: {user_query}")
            ]

            response = await llm_client.generate_response(
                messages,
                max_tokens=300,
                temperature=0.3
            )

            # Try to parse JSON response
            import json
            try:
                analysis = json.loads(response.content)
                analysis["llm_enhanced"] = True
                return analysis
            except json.JSONDecodeError:
                # Fall back to keyword-based analysis
                self.logger.warning("Failed to parse LLM response, using fallback analysis")

        except Exception as e:
            self.logger.warning(f"LLM intent analysis failed: {e}, using fallback")

        # Fallback to keyword-based analysis
        return self._fallback_intent_analysis(user_query)

    def _fallback_intent_analysis(self, user_query: str) -> Dict[str, Any]:
        """Fallback keyword-based intent analysis"""
        query_lower = user_query.lower()

        analysis = {
            "query_type": "general",
            "requires_code_reading": False,
            "requires_reasoning": False,
            "requires_bug_detection": False,
            "requires_planning": False,
            "complexity": "medium",
            "confidence": 0.6,
            "llm_enhanced": False
        }

        # Simple keyword-based analysis
        if any(word in query_lower for word in ["explain", "how does", "what is", "describe"]):
            analysis["query_type"] = "explanation"
            analysis["requires_code_reading"] = True
            analysis["requires_reasoning"] = True

        elif any(word in query_lower for word in ["bug", "error", "issue", "problem", "fix"]):
            analysis["query_type"] = "bug_detection"
            analysis["requires_bug_detection"] = True
            analysis["requires_code_reading"] = True

        elif any(word in query_lower for word in ["optimize", "improve", "refactor", "better"]):
            analysis["query_type"] = "optimization"
            analysis["requires_code_reading"] = True
            analysis["requires_reasoning"] = True

        elif any(word in query_lower for word in ["find", "search", "locate", "where"]):
            analysis["query_type"] = "search"
            analysis["requires_code_reading"] = True

        elif any(word in query_lower for word in ["plan", "design", "architecture", "structure"]):
            analysis["query_type"] = "planning"
            analysis["requires_planning"] = True
            analysis["requires_reasoning"] = True

        # Determine complexity
        if len(user_query.split()) > 20:
            analysis["complexity"] = "high"
        elif len(user_query.split()) < 5:
            analysis["complexity"] = "low"

        return analysis
    
    async def _execute_query_plan(self, plan: QueryPlan, context: AgentContext) -> List[AgentResult]:
        """
        Execute a query plan by coordinating sub-agents.
        
        Args:
            plan: Query execution plan
            context: Execution context
            
        Returns:
            List of agent results
        """
        results = []
        
        for task in plan.task_sequence:
            try:
                # Find appropriate agent for the task
                agent = agent_registry.find_best_agent_for_task(task)
                
                if not agent:
                    self.logger.warning(f"No agent found for task type: {task.type}")
                    continue
                
                # Execute task
                self.logger.info(f"Executing task {task.type} with agent {agent.agent_id}")
                result = await agent.execute_with_monitoring(task, context)
                results.append(result)
                
                # Update context with results for next task
                if result.status == AgentStatus.COMPLETED:
                    context.previous_results[agent.agent_id] = result.data
                
            except Exception as e:
                self.logger.error(f"Error executing task {task.type}: {e}")
                # Continue with other tasks
                continue
        
        return results
    
    async def _synthesize_results(self, results: List[AgentResult], user_query: str) -> Dict[str, Any]:
        """
        Synthesize results from multiple agents into a final response.
        
        Args:
            results: List of agent results
            user_query: Original user query
            
        Returns:
            Synthesized result
        """
        successful_results = [r for r in results if r.status == AgentStatus.COMPLETED]
        
        if not successful_results:
            return {
                "answer": "I couldn't process your query. Please try rephrasing it.",
                "confidence": 0.0,
                "suggestions": ["Try asking a more specific question"],
                "agents_used": []
            }
        
        # Combine answers and suggestions
        combined_answer = []
        all_suggestions = []
        agents_used = []
        total_confidence = 0.0
        
        for result in successful_results:
            agents_used.append(result.agent_id)
            total_confidence += result.confidence
            
            if "answer" in result.data:
                combined_answer.append(result.data["answer"])
            
            if result.suggestions:
                all_suggestions.extend(result.suggestions)
        
        # Calculate average confidence
        avg_confidence = total_confidence / len(successful_results) if successful_results else 0.0
        
        # Create final answer
        final_answer = "\n\n".join(combined_answer) if combined_answer else "No specific answer found."
        
        return {
            "answer": final_answer,
            "confidence": avg_confidence,
            "suggestions": list(set(all_suggestions)),  # Remove duplicates
            "agents_used": agents_used
        }
    
    async def _create_task_plan(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Create a task execution plan"""
        # Implementation for task planning
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"plan": "Task plan created"}
        )
    
    async def _handle_system_command(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Handle system management commands"""
        command = task.data.get("command", "")
        
        if command == "status":
            return await self._get_system_status()
        elif command == "agents":
            return await self._list_agents()
        elif command == "metrics":
            return await self._get_system_metrics(task, context)
        else:
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=f"Unknown system command: {command}"
            )
    
    async def _get_system_status(self) -> AgentResult:
        """Get overall system status"""
        agent_info = agent_registry.list_agents()
        
        status_data = {
            "system_status": "operational",
            "active_queries": len(self.active_queries),
            "registered_agents": len(agent_info),
            "available_agents": len([a for a in agent_info.values() 
                                   if a.get("status") in ["idle", "completed"]]),
            "system_metrics": self.system_metrics
        }
        
        return AgentResult(
            agent_id=self.agent_id,
            task_id="system_status",
            status=AgentStatus.COMPLETED,
            data=status_data
        )
    
    async def _list_agents(self) -> AgentResult:
        """List all registered agents"""
        agent_info = agent_registry.list_agents()
        
        return AgentResult(
            agent_id=self.agent_id,
            task_id="list_agents",
            status=AgentStatus.COMPLETED,
            data={"agents": agent_info}
        )
    
    async def _get_system_metrics(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Get system performance metrics"""
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"metrics": self.system_metrics}
        )
    
    def _update_system_metrics(self, processing_time: float, success: bool):
        """Update system performance metrics"""
        self.system_metrics["queries_processed"] += 1
        self.system_metrics["total_processing_time"] += processing_time
        
        total_queries = self.system_metrics["queries_processed"]
        self.system_metrics["average_response_time"] = (
            self.system_metrics["total_processing_time"] / total_queries
        )
        
        # Update success rate (simplified)
        if success:
            current_success_rate = self.system_metrics["success_rate"]
            self.system_metrics["success_rate"] = (
                (current_success_rate * (total_queries - 1) + 1.0) / total_queries
            )
        else:
            current_success_rate = self.system_metrics["success_rate"]
            self.system_metrics["success_rate"] = (
                (current_success_rate * (total_queries - 1)) / total_queries
            )
    
    async def handle_message(self, message: Message) -> Optional[Message]:
        """Handle incoming messages from other agents"""
        if message.type == MessageType.STATUS_UPDATE:
            # Handle agent status updates
            agent_id = message.sender
            status_data = message.payload
            self.agent_performance[agent_id] = status_data
        
        elif message.type == MessageType.QUERY:
            # Handle queries from other agents
            response_data = {"status": "received", "timestamp": time.time()}
            return Message(
                type=MessageType.TASK_RESPONSE,
                sender=self.agent_id,
                recipient=message.sender,
                payload=response_data,
                correlation_id=message.correlation_id
            )
        
        return None

    async def shutdown(self):
        """Shutdown the master agent gracefully"""
        self.logger.info("Shutting down Master Agent...")

        # Cancel active queries
        for query_id in list(self.active_queries.keys()):
            self.logger.info(f"Cancelling active query: {query_id}")
            del self.active_queries[query_id]

        # Unregister from message bus
        message_bus.unregister_handler(self.agent_id)

        self.logger.info("Master Agent shutdown complete")
