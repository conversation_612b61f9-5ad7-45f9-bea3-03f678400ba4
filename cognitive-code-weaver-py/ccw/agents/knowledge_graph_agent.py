"""
Knowledge Graph Agent

Specialized agent for building, maintaining, and querying the knowledge graph.
Integrates semantic analysis with the existing agent framework.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from ccw.core.agent import BaseAgent, AgentTask, AgentResult, AgentStatus, AgentContext
from ccw.core.message_bus import message_bus, MessageType
from ccw.analysis.parser import Parse<PERSON>esult
from ccw.analysis.symbols import Symbol
from ccw.analysis.dependency_graph import DependencyGraph
from ccw.knowledge.knowledge_graph import KnowledgeGraph, KnowledgeGraphBuilder, create_knowledge_graph
from ccw.knowledge.query_engine import GraphQueryEngine, SemanticQuery, QueryType
from ccw.knowledge.concept_mapper import ConceptMapper
from ccw.knowledge.semantic_analyzer import SemanticAnalyzer

logger = logging.getLogger(__name__)


class KnowledgeGraphAgent(BaseAgent):
    """Agent responsible for knowledge graph operations"""
    
    def __init__(self):
        super().__init__("knowledge_graph_agent")
        self.knowledge_graph: Optional[KnowledgeGraph] = None
        self.graph_builder: Optional[KnowledgeGraphBuilder] = None
        self.query_engine: Optional[GraphQueryEngine] = None
        self.concept_mapper = ConceptMapper()
        self.semantic_analyzer = SemanticAnalyzer()
        
        # Task handlers
        self.task_handlers = {
            "build_knowledge_graph": self._handle_build_knowledge_graph,
            "update_knowledge_graph": self._handle_update_knowledge_graph,
            "query_knowledge_graph": self._handle_query_knowledge_graph,
            "semantic_search": self._handle_semantic_search,
            "find_relationships": self._handle_find_relationships,
            "explore_concepts": self._handle_explore_concepts,
            "analyze_code_semantics": self._handle_analyze_code_semantics,
            "get_graph_stats": self._handle_get_graph_stats
        }
    
    async def startup(self):
        """Initialize the knowledge graph agent"""
        try:
            await super().startup()
            
            # Initialize knowledge graph
            self.knowledge_graph = create_knowledge_graph()
            if not self.knowledge_graph:
                raise RuntimeError("Failed to create knowledge graph")
            
            # Initialize knowledge graph
            success = await self.knowledge_graph.initialize()
            if not success:
                raise RuntimeError("Failed to initialize knowledge graph")
            
            # Initialize components
            self.graph_builder = KnowledgeGraphBuilder(self.knowledge_graph)
            self.query_engine = GraphQueryEngine(self.knowledge_graph)
            
            # Subscribe to relevant messages
            message_bus.subscribe_to_topic(self.agent_id, "analysis_complete")
            message_bus.subscribe_to_topic(self.agent_id, "file_updated")
            
            logger.info("Knowledge Graph Agent started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Knowledge Graph Agent: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the knowledge graph agent"""
        try:
            if self.knowledge_graph:
                await self.knowledge_graph.shutdown()
            
            await super().shutdown()
            logger.info("Knowledge Graph Agent shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during Knowledge Graph Agent shutdown: {e}")
    
    async def execute_task(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Execute a knowledge graph task"""
        try:
            handler = self.task_handlers.get(task.task_type)
            if not handler:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error=f"Unknown task type: {task.task_type}"
                )
            
            # Execute the task
            result = await handler(task, context)
            
            # Broadcast completion
            await message_bus.publish(
                topic="knowledge_graph_task_complete",
                message_type=MessageType.TASK_COMPLETE,
                sender=self.agent_id,
                payload={
                    "task_id": task.id,
                    "task_type": task.task_type,
                    "status": result.status.value,
                    "result_data": result.data
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _handle_build_knowledge_graph(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Build knowledge graph from analysis results"""
        try:
            parse_results = task.data.get("parse_results", [])
            symbols = task.data.get("symbols", [])
            dependency_graph = task.data.get("dependency_graph")
            
            if not parse_results or not symbols:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Missing required data: parse_results and symbols"
                )
            
            # Convert data if needed
            if isinstance(parse_results[0], dict):
                parse_results = [self._dict_to_parse_result(pr) for pr in parse_results]
            
            if isinstance(symbols[0], dict):
                symbols = [self._dict_to_symbol(s) for s in symbols]
            
            if isinstance(dependency_graph, dict):
                dependency_graph = self._dict_to_dependency_graph(dependency_graph)
            
            # Build knowledge graph
            success = await self.graph_builder.build_from_analysis(
                parse_results, symbols, dependency_graph
            )
            
            if success:
                stats = await self.knowledge_graph.get_stats()
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={
                        "success": True,
                        "stats": {
                            "total_nodes": stats.total_nodes,
                            "total_relationships": stats.total_relationships,
                            "node_types": stats.node_types,
                            "relationship_types": stats.relationship_types
                        },
                        "processed_files": len(parse_results)
                    }
                )
            else:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Failed to build knowledge graph"
                )
                
        except Exception as e:
            logger.error(f"Build knowledge graph failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _handle_update_knowledge_graph(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Update knowledge graph for specific files"""
        try:
            parse_result = task.data.get("parse_result")
            file_symbols = task.data.get("file_symbols", [])
            dependency_graph = task.data.get("dependency_graph")
            
            if not parse_result:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Missing required data: parse_result"
                )
            
            # Convert data if needed
            if isinstance(parse_result, dict):
                parse_result = self._dict_to_parse_result(parse_result)
            
            if isinstance(file_symbols[0], dict):
                file_symbols = [self._dict_to_symbol(s) for s in file_symbols]
            
            if isinstance(dependency_graph, dict):
                dependency_graph = self._dict_to_dependency_graph(dependency_graph)
            
            # Update knowledge graph
            success = await self.graph_builder.update_from_file(
                parse_result, file_symbols, dependency_graph
            )
            
            if success:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.COMPLETED,
                    data={
                        "success": True,
                        "updated_file": parse_result.file_path
                    }
                )
            else:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Failed to update knowledge graph"
                )
                
        except Exception as e:
            logger.error(f"Update knowledge graph failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _handle_query_knowledge_graph(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Execute a query against the knowledge graph"""
        try:
            query_text = task.data.get("query")
            query_type = task.data.get("query_type", "semantic_search")
            parameters = task.data.get("parameters", {})
            max_results = task.data.get("max_results", 50)
            
            if not query_text:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Missing required parameter: query"
                )
            
            # Create semantic query
            semantic_query = SemanticQuery(
                query_text=query_text,
                query_type=QueryType(query_type),
                parameters=parameters,
                max_results=max_results
            )
            
            # Execute query
            result = await self.query_engine.execute_query(semantic_query)
            
            # Convert result to serializable format
            result_data = {
                "nodes": [node.to_dict() for node in result.nodes],
                "relationships": [rel.to_dict() for rel in result.relationships],
                "total_results": result.total_results,
                "execution_time": result.execution_time,
                "confidence_scores": result.confidence_scores,
                "metadata": result.metadata
            }
            
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data=result_data
            )
            
        except Exception as e:
            logger.error(f"Query knowledge graph failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _handle_semantic_search(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Perform semantic search"""
        search_query = SemanticQuery(
            query_text=task.data.get("query", ""),
            query_type=QueryType.SEMANTIC_SEARCH,
            max_results=task.data.get("max_results", 20),
            confidence_threshold=task.data.get("confidence_threshold", 0.5)
        )
        
        result = await self.query_engine.execute_query(search_query)
        
        return AgentResult(
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={
                "results": [node.to_dict() for node in result.nodes],
                "total_results": result.total_results,
                "execution_time": result.execution_time
            }
        )
    
    async def _handle_find_relationships(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Find relationships between entities"""
        relationship_query = SemanticQuery(
            query_text="",
            query_type=QueryType.RELATIONSHIP_QUERY,
            parameters={
                "source_id": task.data.get("source_id"),
                "target_id": task.data.get("target_id"),
                "relationship_type": task.data.get("relationship_type")
            }
        )
        
        result = await self.query_engine.execute_query(relationship_query)
        
        return AgentResult(
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={
                "relationships": [rel.to_dict() for rel in result.relationships],
                "nodes": [node.to_dict() for node in result.nodes],
                "total_results": result.total_results
            }
        )
    
    async def _handle_explore_concepts(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Explore concepts and their relationships"""
        exploration_query = SemanticQuery(
            query_text="",
            query_type=QueryType.CONCEPT_EXPLORATION,
            parameters={
                "concept_id": task.data.get("concept_id"),
                "max_depth": task.data.get("max_depth", 2)
            }
        )
        
        result = await self.query_engine.execute_query(exploration_query)
        
        return AgentResult(
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={
                "concepts": [node.to_dict() for node in result.nodes],
                "relationships": [rel.to_dict() for rel in result.relationships],
                "exploration_depth": task.data.get("max_depth", 2)
            }
        )
    
    async def _handle_analyze_code_semantics(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Analyze code semantics and extract concepts"""
        try:
            parse_result = task.data.get("parse_result")
            symbols = task.data.get("symbols", [])
            
            if not parse_result:
                return AgentResult(
                    task_id=task.id,
                    status=AgentStatus.FAILED,
                    error="Missing required data: parse_result"
                )
            
            # Convert data if needed
            if isinstance(parse_result, dict):
                parse_result = self._dict_to_parse_result(parse_result)
            
            if symbols and isinstance(symbols[0], dict):
                symbols = [self._dict_to_symbol(s) for s in symbols]
            
            # Perform semantic analysis
            concepts, relationships = await self.semantic_analyzer.analyze_semantics(parse_result, symbols)
            
            # Map concepts
            mapped_concepts = self.concept_mapper.map_concepts(concepts)
            
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "concepts": [
                        {
                            "id": c.base_concept.id,
                            "name": c.base_concept.name,
                            "type": c.base_concept.concept_type.value,
                            "description": c.base_concept.description,
                            "confidence": c.base_concept.confidence,
                            "hierarchy_level": c.hierarchy_level.value,
                            "abstraction_level": c.abstraction_level,
                            "domain_relevance": c.domain_relevance
                        }
                        for c in mapped_concepts
                    ],
                    "relationships": [
                        {
                            "source": r.source_concept,
                            "target": r.target_concept,
                            "type": r.relationship_type,
                            "strength": r.strength
                        }
                        for r in relationships
                    ],
                    "total_concepts": len(concepts),
                    "total_relationships": len(relationships)
                }
            )
            
        except Exception as e:
            logger.error(f"Analyze code semantics failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _handle_get_graph_stats(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Get knowledge graph statistics"""
        try:
            stats = await self.knowledge_graph.get_stats()
            
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "total_nodes": stats.total_nodes,
                    "total_relationships": stats.total_relationships,
                    "node_types": stats.node_types,
                    "relationship_types": stats.relationship_types,
                    "last_updated": stats.last_updated.isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Get graph stats failed: {e}")
            return AgentResult(
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    def _dict_to_parse_result(self, data: Dict[str, Any]) -> ParseResult:
        """Convert dictionary to ParseResult"""
        from ccw.analysis.parser import LanguageType
        
        return ParseResult(
            file_path=data["file_path"],
            language=LanguageType(data["language"]),
            content=data["content"],
            functions=data.get("functions", []),
            classes=data.get("classes", []),
            imports=data.get("imports", []),
            metadata=data.get("metadata", {})
        )
    
    def _dict_to_symbol(self, data: Dict[str, Any]) -> Symbol:
        """Convert dictionary to Symbol"""
        from ccw.analysis.symbols import SymbolType
        
        return Symbol(
            name=data["name"],
            symbol_type=SymbolType(data["symbol_type"]),
            file_path=data["file_path"],
            line_number=data["line_number"],
            signature=data.get("signature"),
            metadata=data.get("metadata", {})
        )
    
    def _dict_to_dependency_graph(self, data: Dict[str, Any]) -> DependencyGraph:
        """Convert dictionary to DependencyGraph"""
        dep_graph = DependencyGraph()
        dep_graph.dependencies = data.get("dependencies", {})
        return dep_graph
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return [
            "build_knowledge_graph",
            "update_knowledge_graph", 
            "query_knowledge_graph",
            "semantic_search",
            "find_relationships",
            "explore_concepts",
            "analyze_code_semantics",
            "get_graph_stats"
        ]
