"""
Cognitive Agent - Advanced cognitive processing and context building

Handles complex reasoning, context assembly, and knowledge integration.
Provides the cognitive foundation for other agents.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass

from ccw.core.agent import Agent, AgentTask, AgentContext, AgentResult, AgentStatus
from ccw.core.registry import register_agent
from ccw.core.config import config
from ccw.llm.client import llm_client
from ccw.llm.providers.base import create_system_message, create_user_message

logger = logging.getLogger(__name__)


@dataclass
class CognitiveContext:
    """Enhanced context with cognitive processing results"""
    original_context: AgentContext
    processed_query: str
    intent_analysis: Dict[str, Any]
    knowledge_fragments: List[Dict[str, Any]]
    reasoning_chains: List[Dict[str, Any]]
    confidence_scores: Dict[str, float]
    metadata: Dict[str, Any]


@register_agent("cognitive_agent", capabilities=[
    "context_building",
    "intent_analysis", 
    "knowledge_integration",
    "reasoning_coordination",
    "cognitive_processing"
])
class CognitiveAgent(Agent):
    """
    Cognitive Agent responsible for advanced cognitive processing.
    
    Responsibilities:
    - Build rich context from user queries
    - Analyze intent and extract key concepts
    - Integrate knowledge from multiple sources
    - Coordinate reasoning processes
    - Provide cognitive foundation for other agents
    """
    
    def __init__(self, agent_id: str = "cognitive_agent", config_dict: Dict[str, Any] = None):
        super().__init__(agent_id, config_dict or {})
        
        self.knowledge_cache: Dict[str, Any] = {}
        self.reasoning_patterns: Dict[str, List[str]] = {}
        self.concept_graph: Dict[str, List[str]] = {}
        
        # Configuration
        self.max_context_size = config.get("cognitive.max_context_size", 10000)
        self.enable_caching = config.get("performance.enable_caching", True)
        self.cache_ttl = config.get("cognitive.cache_ttl", 3600)  # 1 hour
        
        self.logger.info("Cognitive Agent initialized")
    
    def _define_capabilities(self) -> List[str]:
        """Define cognitive agent capabilities"""
        return [
            "context_building",
            "intent_analysis",
            "knowledge_integration", 
            "reasoning_coordination",
            "cognitive_processing",
            "concept_extraction",
            "pattern_recognition"
        ]
    
    def _define_dependencies(self) -> List[str]:
        """Cognitive agent may depend on knowledge graph and semantic analysis"""
        return ["knowledge_graph", "semantic_analyzer"]
    
    async def execute(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Execute cognitive processing task.
        
        Args:
            task: Task to execute
            context: Execution context
            
        Returns:
            Cognitive processing result
        """
        if task.type == "context_building":
            return await self._build_context(task, context)
        elif task.type == "intent_analysis":
            return await self._analyze_intent(task, context)
        elif task.type == "knowledge_integration":
            return await self._integrate_knowledge(task, context)
        elif task.type == "reasoning_coordination":
            return await self._coordinate_reasoning(task, context)
        elif task.type == "cognitive_processing":
            return await self._process_cognitively(task, context)
        else:
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=f"Unknown task type: {task.type}"
            )
    
    async def _build_context(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """
        Build rich context for query processing.
        
        Args:
            task: Context building task
            context: Base context
            
        Returns:
            Enhanced context result
        """
        try:
            user_query = task.data.get("query", context.user_query)
            self.logger.info(f"Building context for query: {user_query[:100]}...")
            
            # Analyze query intent
            intent_analysis = await self._perform_intent_analysis(user_query)
            
            # Extract key concepts
            concepts = await self._extract_concepts(user_query, context)
            
            # Gather relevant knowledge
            knowledge_fragments = await self._gather_knowledge(concepts, context)
            
            # Build reasoning chains
            reasoning_chains = await self._build_reasoning_chains(
                user_query, concepts, knowledge_fragments
            )
            
            # Calculate confidence scores
            confidence_scores = await self._calculate_confidence_scores(
                intent_analysis, concepts, knowledge_fragments
            )
            
            # Create enhanced context
            cognitive_context = CognitiveContext(
                original_context=context,
                processed_query=user_query,
                intent_analysis=intent_analysis,
                knowledge_fragments=knowledge_fragments,
                reasoning_chains=reasoning_chains,
                confidence_scores=confidence_scores,
                metadata={
                    "processing_timestamp": time.time(),
                    "concepts_count": len(concepts),
                    "knowledge_fragments_count": len(knowledge_fragments)
                }
            )
            
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.COMPLETED,
                data={
                    "cognitive_context": cognitive_context,
                    "intent_analysis": intent_analysis,
                    "key_concepts": concepts,
                    "knowledge_fragments": knowledge_fragments,
                    "reasoning_chains": reasoning_chains,
                    "confidence_scores": confidence_scores
                },
                confidence=confidence_scores.get("overall", 0.8)
            )
            
        except Exception as e:
            self.logger.error(f"Error building context: {e}")
            return AgentResult(
                agent_id=self.agent_id,
                task_id=task.id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _perform_intent_analysis(self, query: str) -> Dict[str, Any]:
        """
        Analyze user query intent using LLM-powered analysis.

        Args:
            query: User query to analyze

        Returns:
            Intent analysis results
        """
        try:
            # Use LLM for sophisticated intent analysis
            messages = [
                create_system_message("""You are an expert at analyzing user queries about software development and code analysis.
Analyze the given query and provide detailed intent analysis in JSON format:
{
    "primary_intent": "explanation|debugging|optimization|search|planning|analysis",
    "intent_confidence": float (0.0-1.0),
    "query_complexity": "simple|medium|complex",
    "requires_code_analysis": boolean,
    "requires_reasoning": boolean,
    "requires_search": boolean,
    "key_entities": ["entity1", "entity2"],
    "technical_domain": "web|mobile|backend|frontend|devops|ml|general",
    "urgency_level": "low|medium|high",
    "expected_response_type": "explanation|code|list|analysis|recommendation"
}"""),
                create_user_message(f"Analyze this software development query: {query}")
            ]

            response = await llm_client.generate_response(
                messages,
                max_tokens=400,
                temperature=0.2
            )

            # Parse LLM response
            import json
            try:
                analysis = json.loads(response.content)
                analysis["llm_enhanced"] = True
                analysis["analysis_method"] = "llm"
                return analysis
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse LLM intent analysis, using fallback")

        except Exception as e:
            self.logger.warning(f"LLM intent analysis failed: {e}, using fallback")

        # Fallback to keyword-based analysis
        return self._fallback_intent_analysis(query)

    def _fallback_intent_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback keyword-based intent analysis"""
        query_lower = query.lower()

        # Basic intent classification
        intent_scores = {
            "explanation": 0.0,
            "debugging": 0.0,
            "optimization": 0.0,
            "search": 0.0,
            "planning": 0.0,
            "analysis": 0.0
        }

        # Explanation intent
        explanation_keywords = ["explain", "how does", "what is", "describe", "tell me about"]
        intent_scores["explanation"] = sum(1 for kw in explanation_keywords if kw in query_lower) / len(explanation_keywords)

        # Debugging intent
        debug_keywords = ["bug", "error", "issue", "problem", "fix", "debug", "wrong"]
        intent_scores["debugging"] = sum(1 for kw in debug_keywords if kw in query_lower) / len(debug_keywords)

        # Optimization intent
        optimization_keywords = ["optimize", "improve", "better", "faster", "efficient", "performance"]
        intent_scores["optimization"] = sum(1 for kw in optimization_keywords if kw in query_lower) / len(optimization_keywords)

        # Search intent
        search_keywords = ["find", "search", "locate", "where", "show me", "list"]
        intent_scores["search"] = sum(1 for kw in search_keywords if kw in query_lower) / len(search_keywords)

        # Planning intent
        planning_keywords = ["plan", "design", "architecture", "structure", "organize"]
        intent_scores["planning"] = sum(1 for kw in planning_keywords if kw in query_lower) / len(planning_keywords)

        # Analysis intent
        analysis_keywords = ["analyze", "examine", "review", "assess", "evaluate"]
        intent_scores["analysis"] = sum(1 for kw in analysis_keywords if kw in query_lower) / len(analysis_keywords)

        # Determine primary intent
        primary_intent = max(intent_scores.items(), key=lambda x: x[1])

        return {
            "primary_intent": primary_intent[0],
            "intent_confidence": primary_intent[1],
            "all_intent_scores": intent_scores,
            "query_complexity": self._assess_query_complexity(query),
            "requires_code_analysis": any(word in query_lower for word in ["code", "function", "class", "method"]),
            "requires_reasoning": intent_scores["explanation"] > 0.3 or intent_scores["analysis"] > 0.3,
            "requires_search": intent_scores["search"] > 0.3 or intent_scores["debugging"] > 0.3,
            "llm_enhanced": False,
            "analysis_method": "keyword"
        }
    
    async def _extract_concepts(self, query: str, context: AgentContext) -> List[Dict[str, Any]]:
        """
        Extract key concepts from the query and context.
        
        Args:
            query: User query
            context: Execution context
            
        Returns:
            List of extracted concepts
        """
        concepts = []
        
        # Simple keyword extraction (can be enhanced with NLP)
        technical_terms = [
            "authentication", "authorization", "database", "api", "function", "class",
            "method", "variable", "algorithm", "data structure", "design pattern",
            "architecture", "framework", "library", "module", "component"
        ]
        
        query_lower = query.lower()
        for term in technical_terms:
            if term in query_lower:
                concepts.append({
                    "concept": term,
                    "type": "technical_term",
                    "confidence": 0.8,
                    "context": "query"
                })
        
        # Extract file-related concepts from context
        if context.file_context:
            for file_path in context.file_context:
                file_name = file_path.split("/")[-1]
                concepts.append({
                    "concept": file_name,
                    "type": "file",
                    "confidence": 0.9,
                    "context": "file_context",
                    "file_path": file_path
                })
        
        return concepts
    
    async def _gather_knowledge(self, concepts: List[Dict[str, Any]], 
                               context: AgentContext) -> List[Dict[str, Any]]:
        """
        Gather relevant knowledge fragments for the concepts.
        
        Args:
            concepts: Extracted concepts
            context: Execution context
            
        Returns:
            List of knowledge fragments
        """
        knowledge_fragments = []
        
        for concept in concepts:
            concept_name = concept["concept"]
            
            # Check cache first
            if self.enable_caching and concept_name in self.knowledge_cache:
                cached_knowledge = self.knowledge_cache[concept_name]
                if time.time() - cached_knowledge["timestamp"] < self.cache_ttl:
                    knowledge_fragments.append(cached_knowledge["data"])
                    continue
            
            # Gather knowledge for this concept
            fragment = {
                "concept": concept_name,
                "type": concept["type"],
                "description": f"Knowledge about {concept_name}",
                "related_concepts": self.concept_graph.get(concept_name, []),
                "confidence": concept["confidence"],
                "source": "cognitive_agent",
                "timestamp": time.time()
            }
            
            # Add to cache
            if self.enable_caching:
                self.knowledge_cache[concept_name] = {
                    "data": fragment,
                    "timestamp": time.time()
                }
            
            knowledge_fragments.append(fragment)
        
        return knowledge_fragments
    
    async def _build_reasoning_chains(self, query: str, concepts: List[Dict[str, Any]], 
                                    knowledge_fragments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Build reasoning chains for the query.
        
        Args:
            query: User query
            concepts: Extracted concepts
            knowledge_fragments: Gathered knowledge
            
        Returns:
            List of reasoning chains
        """
        reasoning_chains = []
        
        # Simple reasoning chain construction
        if concepts:
            main_concept = concepts[0]["concept"]
            
            chain = {
                "chain_id": f"chain_{int(time.time())}",
                "type": "concept_exploration",
                "steps": [
                    {
                        "step": 1,
                        "action": "identify_main_concept",
                        "result": main_concept,
                        "confidence": 0.8
                    },
                    {
                        "step": 2,
                        "action": "gather_related_knowledge",
                        "result": f"Found {len(knowledge_fragments)} knowledge fragments",
                        "confidence": 0.7
                    },
                    {
                        "step": 3,
                        "action": "synthesize_understanding",
                        "result": "Ready for detailed analysis",
                        "confidence": 0.6
                    }
                ],
                "confidence": 0.7
            }
            
            reasoning_chains.append(chain)
        
        return reasoning_chains
    
    async def _calculate_confidence_scores(self, intent_analysis: Dict[str, Any],
                                         concepts: List[Dict[str, Any]],
                                         knowledge_fragments: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate confidence scores for different aspects.
        
        Args:
            intent_analysis: Intent analysis results
            concepts: Extracted concepts
            knowledge_fragments: Knowledge fragments
            
        Returns:
            Confidence scores dictionary
        """
        scores = {
            "intent_confidence": intent_analysis.get("intent_confidence", 0.5),
            "concept_extraction": min(1.0, len(concepts) * 0.2),
            "knowledge_availability": min(1.0, len(knowledge_fragments) * 0.15),
            "context_completeness": 0.7  # Base score
        }
        
        # Calculate overall confidence
        scores["overall"] = sum(scores.values()) / len(scores)
        
        return scores
    
    def _assess_query_complexity(self, query: str) -> str:
        """Assess the complexity of a query"""
        word_count = len(query.split())
        
        if word_count < 5:
            return "simple"
        elif word_count < 15:
            return "medium"
        else:
            return "complex"
    
    async def _analyze_intent(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Analyze intent for a specific task"""
        query = task.data.get("query", "")
        intent_analysis = await self._perform_intent_analysis(query)
        
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"intent_analysis": intent_analysis},
            confidence=intent_analysis.get("intent_confidence", 0.5)
        )
    
    async def _integrate_knowledge(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Integrate knowledge from multiple sources"""
        # Implementation for knowledge integration
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"knowledge_integration": "completed"}
        )
    
    async def _coordinate_reasoning(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Coordinate reasoning processes"""
        # Implementation for reasoning coordination
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"reasoning_coordination": "completed"}
        )
    
    async def _process_cognitively(self, task: AgentTask, context: AgentContext) -> AgentResult:
        """Perform general cognitive processing"""
        # Implementation for general cognitive processing
        return AgentResult(
            agent_id=self.agent_id,
            task_id=task.id,
            status=AgentStatus.COMPLETED,
            data={"cognitive_processing": "completed"}
        )
