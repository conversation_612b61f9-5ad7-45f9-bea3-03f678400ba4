# Cognitive Code Weaver Configuration File
# This file contains all configuration options for the system

# LLM Integration Settings
llm:
  provider: openai  # Options: openai, anthropic, local
  model: gpt-4
  api_key: ${CCW_LLM_API_KEY}  # Set via environment variable
  base_url: ""  # Optional: custom API endpoint
  timeout: 30.0
  max_retries: 3
  temperature: 0.7
  max_tokens: 4000

  # Provider-specific settings
  openai:
    models:
      - gpt-4
      - gpt-4-turbo
      - gpt-3.5-turbo
    embedding_model: text-embedding-ada-002

  anthropic:
    models:
      - claude-3-opus-20240229
      - claude-3-sonnet-20240229
      - claude-3-haiku-20240307
    api_key: ${CCW_ANTHROPIC_API_KEY}

  local:
    backend: ollama  # Options: ollama, llamacpp, custom
    base_url: http://localhost:11434
    models:
      - llama2:7b
      - codellama:7b
      - mistral:7b

  # Client configuration
  client:
    primary_provider: openai
    fallback_providers:
      - anthropic
      - local
    enable_caching: true
    cache_ttl: 3600  # 1 hour
    enable_fallback: true
    response_timeout: 60.0

# Database Configuration
database:
  type: memory  # Options: memory, neo4j, sqlite
  url: ${CCW_DATABASE_URL}  # For external databases
  username: ${CCW_DATABASE_USERNAME}
  password: ${CCW_DATABASE_PASSWORD}
  database: ccw

# Code Analysis Settings
analysis:
  max_file_size: 1048576  # 1MB
  exclude_patterns:
    - "*/node_modules/*"
    - "*/dist/*"
    - "*/build/*"
    - "*/.git/*"
    - "*/coverage/*"
    - "*/__pycache__/*"
    - "*.pyc"
    - "*.pyo"
    - "*.pyd"
    - "*.so"
    - "*.dll"
  include_patterns:
    - "**/*.py"
    - "**/*.js"
    - "**/*.ts"
    - "**/*.jsx"
    - "**/*.tsx"
    - "**/*.java"
    - "**/*.c"
    - "**/*.cpp"
    - "**/*.cs"
    - "**/*.go"
    - "**/*.rb"
    - "**/*.php"
    - "**/*.rs"
  languages:
    - python
    - javascript
    - typescript
    - java
    - c
    - cpp
    - csharp
    - go
    - ruby
    - php
    - rust

# Agent Configuration
agents:
  enabled:
    - master_agent
    - cognitive_agent
    - planner
    - code_reader
    - reasoner
    - bug_detector
  plugins_dir: ./plugins
  max_concurrent_tasks: 5
  task_timeout: 300.0

# Semantic Analysis Settings
semantic:
  enable_topic_modeling: true
  min_concept_frequency: 2
  embedding_model: sentence-transformers/all-MiniLM-L6-v2
  max_concepts: 1000
  similarity_threshold: 0.7

# Retrieval Settings
retrieval:
  max_results: 10
  semantic_weight: 0.4
  structural_weight: 0.3
  contextual_weight: 0.3
  similarity_threshold: 0.7
  enable_hybrid_search: true

# User Interface Settings
ui:
  auto_analyze_on_startup: false
  show_progress: true
  output_format: text  # Options: text, json, yaml
  color_output: true
  interactive_mode: true

# Logging Configuration
logging:
  level: INFO  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ""  # Optional: log to file
  max_file_size: 10485760  # 10MB
  backup_count: 5
  enable_structured_logging: true

# Performance Settings
performance:
  enable_monitoring: true
  metrics_retention_days: 30
  cache_size: 1000
  enable_caching: true
  cache_ttl: 3600  # 1 hour
  max_memory_usage: 2147483648  # 2GB

# Cognitive Processing Settings
cognitive:
  max_context_size: 10000
  enable_reasoning_chains: true
  confidence_threshold: 0.5
  max_reasoning_depth: 5

# Planner Settings
planner:
  max_plan_depth: 5
  default_step_time: 30.0
  enable_parallel_planning: true
  plan_optimization: true

# Message Bus Settings
message_bus:
  enable_persistence: false
  persistence_file: messages.json
  max_history_size: 1000
  message_ttl: 3600  # 1 hour

# Plugin Settings
plugins:
  auto_load: true
  plugin_dirs:
    - ./plugins
    - ~/.ccw/plugins
  enable_remote_plugins: false
  plugin_timeout: 60.0

# Security Settings
security:
  enable_sandboxing: true
  max_execution_time: 300.0
  allowed_imports:
    - os
    - sys
    - json
    - yaml
    - pathlib
  blocked_imports:
    - subprocess
    - eval
    - exec

# Development Settings (only for development)
development:
  debug_mode: false
  enable_profiling: false
  mock_llm_responses: false
  test_mode: false
