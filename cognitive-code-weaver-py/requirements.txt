# Core dependencies
asyncio-mqtt>=0.13.0
pydantic>=2.0.0
pyyaml>=6.0
click>=8.0.0
rich>=13.0.0
typer>=0.9.0

# LLM Integration
openai>=1.0.0
anthropic>=0.7.0
requests>=2.31.0
httpx>=0.24.0

# Code Analysis
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0
tree-sitter-java>=0.20.0
tree-sitter-c>=0.20.0
tree-sitter-cpp>=0.20.0
tree-sitter-go>=0.20.0
tree-sitter-rust>=0.20.0
ast-tools>=0.1.0

# Natural Language Processing
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0
scikit-learn>=1.3.0
nltk>=3.8.0
spacy>=3.6.0

# Knowledge Graph & Database
neo4j>=5.0.0
networkx>=3.1.0
rdflib>=6.3.0
py2neo>=2021.2.3

# Vector Database
pinecone-client>=2.2.0
chromadb>=0.4.0
faiss-cpu>=1.7.4

# Semantic Analysis
gensim>=4.3.0
umap-learn>=0.5.3
hdbscan>=0.8.29

# Web Framework & API
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0
aiohttp>=3.9.0
python-multipart>=0.0.6
jinja2>=3.1.0

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-xdist>=3.0.0
pytest-mock>=3.10.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.3.0

# Monitoring & Logging
structlog>=23.1.0
prometheus-client>=0.17.0
psutil>=5.9.0

# Utilities
python-dotenv>=1.0.0
pathlib2>=2.3.7
dataclasses-json>=0.5.14
typing-extensions>=4.7.0
uuid>=1.30

# Optional: Jupyter for development
jupyter>=1.0.0
ipykernel>=6.25.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Optional: Advanced ML features
torch-geometric>=2.3.0
dgl>=1.1.0
optuna>=3.3.0
