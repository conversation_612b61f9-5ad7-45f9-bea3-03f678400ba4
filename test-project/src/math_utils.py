"""
Mathematical utility functions for advanced calculations
"""
import math
from typing import List, Union

class MathUtils:
    """Utility class for advanced mathematical operations"""
    
    def __init__(self):
        self.pi = math.pi
        self.e = math.e
    
    def factorial(self, n: int) -> int:
        """
        Calculate factorial of a number
        
        Args:
            n (int): Number to calculate factorial for
            
        Returns:
            int: Factorial of n
            
        Raises:
            ValueError: If n is negative
        """
        if n < 0:
            raise ValueError("Factorial is not defined for negative numbers")
        if n == 0 or n == 1:
            return 1
        return n * self.factorial(n - 1)
    
    def fibonacci(self, n: int) -> int:
        """
        Calculate nth Fibonacci number
        
        Args:
            n (int): Position in Fibonacci sequence
            
        Returns:
            int: nth Fibonacci number
        """
        if n <= 1:
            return n
        return self.fibonacci(n - 1) + self.fibonacci(n - 2)
    
    def is_prime(self, n: int) -> bool:
        """
        Check if a number is prime
        
        Args:
            n (int): Number to check
            
        Returns:
            bool: True if prime, False otherwise
        """
        if n < 2:
            return False
        for i in range(2, int(math.sqrt(n)) + 1):
            if n % i == 0:
                return False
        return True
    
    def gcd(self, a: int, b: int) -> int:
        """
        Calculate Greatest Common Divisor using Euclidean algorithm
        
        Args:
            a (int): First number
            b (int): Second number
            
        Returns:
            int: GCD of a and b
        """
        while b:
            a, b = b, a % b
        return a
    
    def lcm(self, a: int, b: int) -> int:
        """
        Calculate Least Common Multiple
        
        Args:
            a (int): First number
            b (int): Second number
            
        Returns:
            int: LCM of a and b
        """
        return abs(a * b) // self.gcd(a, b)
    
    def mean(self, numbers: List[Union[int, float]]) -> float:
        """
        Calculate arithmetic mean of a list of numbers
        
        Args:
            numbers (List[Union[int, float]]): List of numbers
            
        Returns:
            float: Arithmetic mean
        """
        if not numbers:
            raise ValueError("Cannot calculate mean of empty list")
        return sum(numbers) / len(numbers)
    
    def median(self, numbers: List[Union[int, float]]) -> float:
        """
        Calculate median of a list of numbers
        
        Args:
            numbers (List[Union[int, float]]): List of numbers
            
        Returns:
            float: Median value
        """
        if not numbers:
            raise ValueError("Cannot calculate median of empty list")
        
        sorted_numbers = sorted(numbers)
        n = len(sorted_numbers)
        
        if n % 2 == 0:
            return (sorted_numbers[n // 2 - 1] + sorted_numbers[n // 2]) / 2
        else:
            return sorted_numbers[n // 2]

def main():
    """Demo function to test MathUtils"""
    utils = MathUtils()
    
    print(f"Factorial of 5: {utils.factorial(5)}")
    print(f"10th Fibonacci number: {utils.fibonacci(10)}")
    print(f"Is 17 prime? {utils.is_prime(17)}")
    print(f"GCD of 48 and 18: {utils.gcd(48, 18)}")
    print(f"LCM of 12 and 15: {utils.lcm(12, 15)}")
    
    numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    print(f"Mean of {numbers}: {utils.mean(numbers)}")
    print(f"Median of {numbers}: {utils.median(numbers)}")

if __name__ == "__main__":
    main()
