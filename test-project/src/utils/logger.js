/**
 * Simple logging utility class
 */
class Logger {
  constructor(name) {
    this.name = name;
    this.logLevel = 'INFO';
  }

  /**
   * Log an info message
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  info(message, ...args) {
    this._log('INFO', message, ...args);
  }

  /**
   * Log a warning message
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  warn(message, ...args) {
    this._log('WARN', message, ...args);
  }

  /**
   * Log an error message
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  error(message, ...args) {
    this._log('ERROR', message, ...args);
  }

  /**
   * Log a debug message
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  debug(message, ...args) {
    if (this.logLevel === 'DEBUG') {
      this._log('DEBUG', message, ...args);
    }
  }

  /**
   * Internal logging method
   * @private
   * @param {string} level - Log level
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  _log(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] [${this.name}] ${message}`;
    
    if (args.length > 0) {
      console.log(logMessage, ...args);
    } else {
      console.log(logMessage);
    }
  }

  /**
   * Set the log level
   * @param {string} level - Log level (DEBUG, INFO, WARN, ERROR)
   */
  setLogLevel(level) {
    this.logLevel = level.toUpperCase();
  }
}

module.exports = Logger;
