// Sample JavaScript project for testing Cognitive Code Weaver
const Calculator = require('./calculator');
const Logger = require('./utils/logger');

/**
 * Main application entry point
 * Demonstrates basic calculator functionality
 */
function main() {
  const logger = new Logger('MainApp');
  logger.info('Starting calculator application');

  const calc = new Calculator();
  
  // Perform some calculations
  const result1 = calc.add(10, 5);
  const result2 = calc.multiply(result1, 2);
  const result3 = calc.divide(result2, 3);

  logger.info(`Calculation results: ${result1}, ${result2}, ${result3}`);
  
  // Test error handling
  try {
    calc.divide(10, 0);
  } catch (error) {
    logger.error('Division by zero error:', error.message);
  }

  logger.info('Application completed');
}

// Export for testing
module.exports = { main };

// Run if this is the main module
if (require.main === module) {
  main();
}
